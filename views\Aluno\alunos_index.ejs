<!DOCTYPE html>
<html lang="pt-br">

<head>
</head>

<body>

  <main id="main" class="main">

    <div class="pagetitle">
      <h1>Gerenciamento Acadêmicos</h1>
      <nav>
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="/">Pág.inicial</a></li>
          <li class="breadcrumb-item active">Gerenciamento Acadêmicos</li>
        </ol>
      </nav>
    </div><!-- End Page Title -->

    <section class="section dashboard">
      <div class="row">

        <!-- Left side columns -->
        <div class="col-lg-8">
          <div class="row">
            
       <!-- Perfil Card com Link -->
<div class="col-xxl-4 col-md-6">
  <a href="alunos/perfil" style="text-decoration: none; cursor: pointer;">
    <div class="card info-card revenue-card">
      <div class="card-body">
        <h5 class="card-title">Perfil <span></span></h5>

        <div class="d-flex align-items-center">
          <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
            <i class="bi bi-person-circle"></i>
          </div>
          <div class="ps-3">
            <h6><%= rows[0].usu_nome %></h6>
          </div>
        </div>
      </div>
    </div>
  </a>
</div>

<!-- End Perfil Card -->

            <!-- Sales Card - não conectado ao BD -->
            <div class="col-xxl-4 col-md-6">
              <div class="card info-card sales-card">

              

                <div class="card-body">
                  <h5 class="card-title">Gastos na Cantina <span>| Hoje</span></h5>

                  <div class="d-flex align-items-center">
                    <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                      <i class="bi bi-cart" style= "color: red;"></i>
                    </div>
                    <div class="ps-3">
                      <h6>R$5,00</h6>
                     

                    </div>
                  </div>
                </div>

              </div>
            </div><!-- End Sales Card -->

            <!-- Revenue Card -->
            <div class="col-xxl-4 col-md-6">
              <div class="card info-card revenue-card">

             
                <div class="card-body">
                  <h5 class="card-title">Valor da Mensalidade <span> |2024 </span></h5>

                  <div class="d-flex align-items-center">
                    <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                      <i class="bi bi-currency-dollar"></i>
                    </div>
                    <div class="ps-3">
                      <h6>R$1.264,00</h6>
                      
                    </div>
                  </div>
                </div>

              </div>
            </div><!-- End Revenue Card -->

           
  
      

        

          

          </div>
        </div><!-- End Left side columns -->

        <!-- Right side columns -->
      
               <!-- Table notas bimestres  -->
       
                <div class="card">
                  <div class="card-body">
                    <h5 class="card-title"> Notas</h5>
                  
                      <!-- Select para o ano -->
                      <h5 class="card-title mt-3">
                        <label for="ano-select" class="form-label">Selecione o Ano:</label>
                        <select class="form-select" id="ano-select" name="ano">
                          <option value="" selected disabled>Selecione o ano</option>
                          <option value="2022">2022-1°A</option>
                          <option value="2023">2023-2°A</option>
                          <option value="2024" selected>2024-3°A</option> <!-- Ano pré-selecionado -->
                        </select>
                      </h5>
      
                 
                      <table class="table table-hover">
                        <thead>
                          <tr>
                            <th scope="col">#</th>
                            <th scope="col">Disciplina</th>
                            <th scope="col">1° bimestre </th>
                            <th scope="col">2° bimestre</th>
                            <th scope="col">3° bimestre</th> 
                            <th scope="col">4° bimestre</th>
                            <th scope="col">Média Final</th>
                          </tr>
                        </thead>
                        <tbody>
                          
                        </tbody>
                      </table>

                    <!-- End Table with stripped rows -->
              
                  </div>
                </div>
                  <!-- Table disciplinas  -->
       
                  <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <label for="turma-select" class="form-label">Horário - Selecione o bimestre:</label>
                            <select class="form-select" id="turma-select" name="turma">
                                <option value="" selected disabled>Selecione o bimestre</option>
                                <option value="1b">1° B. </option>
                                <option value="2b" selected>2° B. </option>
                                <option value="3b">3° B.</option>
                                <option value="4b">4° B.</option>
                              </select>
                          </h5>
                      
                            <!-- Select para o ano -->
                      <h5 class="card-title mt-3">
                        <label for="ano-select" class="form-label">Selecione o Ano:</label>
                        <select class="form-select" id="ano-select" name="ano">
                          <option value="" selected disabled>Selecione o ano</option>
                          <option value="2022">2022-1°A</option>
                          <option value="2023">2023-2°A</option>
                          <option value="2024" selected>2024-3°A</option> <!-- Ano pré-selecionado -->
                        </select>
                      </h5>
                   
                      <table class="table table-striped">
                        <thead>
                          <tr>
                            <th scope="col">#</th>
                            <th scope="col">Professor</th>
                            <th scope="col">Nome da displina</th>
                            <th scope="col">Dia das aulas</th>
                            <th scope="col">Horário</th>
                            <th scope="col">carga horária p/ semana</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <th scope="row">1</th>
                            <td>Brandon Jacob</td>
                            <td>Português</td>
                            <td>Todos os dias</td>
                            <td>7:30-8:30 horas</td>
                            <td>5 horas</td>
                          </tr>
                          <tr>
                            <th scope="row">2</th>
                            <td>Bridie Kessler</td>
                            <td>Matemática</td>
                            <td>Todos os dias</td>
                            <td>8:30-9:30 horas</td>
                            <td>5 horas</td>
                          </tr>
                          <tr>
                            <th scope="row">3</th>
                            <td>Ashleigh Langosh</td>
                            <td>Física</td>
                            <td>segunda e quinta</td>
                            <td>9:50-10:50 horas</td>
                            <td>2 horas</td>
                          </tr>
                          <tr>
                            <th scope="row">4</th>
                            <td>Angus Grady</td>
                            <td>Biologia</td>
                            <td>terça e sexta</td>
                            <td>9:50-10:50 horas</td>
                            <td>2 horas</td>
                          </tr>
                          <tr>
                            <th scope="row">5</th>
                            <td>Raheem Lehner</td>
                            <td>História</td>
                            <td>segunda </td>
                            <td>10:50-12:30 horas</td>
                            <td>2 horas</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                      <!-- End Table with stripped rows -->

                <!--Tabela atividades-->
                <div class="card mt-4">
                  <div class="card-body">
                    <h5 class="card-title">Atividades </h5>
                    <% if (typeof atividades_cadastradas  != 'undefined') { %>
                    <% if (atividades_cadastradas && atividades_cadastradas.length > 0) { %>
                      <% var i = 0; %>
                      <table class="table table-bordered">
                        <thead>
                          <tr>
                            <th>Título</th>
                            <th>Descrição</th>
                            <th>Data de Entrega</th>
                            <th>Ações</th>
                            <th>Nota</th>
                          </tr>
                        </thead>
                        <tbody>
                          <% atividades_cadastradas.forEach(atividade => { %>
                            <tr class="mb-3">
                              <td><%= atividade.titulo %></td>
                              <td><%= atividade.descricao %></td>
                              <td><%= new Date(atividade.data_entrega).toLocaleString() %></td>
                              <td class="text-center">
                                <a href="/system/alunos/resposta?id=<%= atividade.ati_id %>" class="btn btn-primary btn-sm">responder</a>
                                <% if (atividadesFeitas[i].feito == 1) { %>
                                <a class="btn btn-success btn-sm" role="alert">Atividade respondida</a>
                                <% } %>
                          
                              </td>
                              <td><%= atividadesFeitas[i].nota %></td>
                            </tr>
                            <% i++; %>
                          <% }) %>
                        </tbody>
                      </table>
                      <% } %>
                    <% } else { %>
                      <p>Nenhuma atividade ainda.</p>
                    <% } %>
                    
                  </div>
                </div>

                <!--<div class="card">
                  <div class="card-body">
                    <h5 class="card-title">Atividades </h5>

                    
                    General Form Elements 
                    <form>
                      <div class="row mb-3">
                        <label for="arquivos" class="col-sm-2 col-form-label"> Enviar Arquivos </label>
                        <div class="col-sm-10">
                          <input class="form-control" type="file" id="formFile">
                        </div>
                      </div>
                      <div class="row mb-3">
                        <label for="TextResposta" class="col-sm-2 col-form-label">Resposta</label>
                        <div class="col-sm-10">
                          <textarea class="form-control" style="height: 100px"></textarea>
                        </div>
                      </div>
                      <div class="row mb-3">
                        <div class="col-sm-10">
                          <button type="submit" class="btn btn-primary">Enviar atividade</button>
                        </div>
                      </div>
                
                              </form> End General Form Elements -->
                
                            </div>
                          </div>
                
                 
                
                        
                
                       
                      


        </div><!-- End Right side columns -->
      </div>
    </section>

  </main><!-- End #main -->

  <!-- ======= Footer ======= -->
  <footer id="footer" class="footer">
    <div class="copyright">
      &copy; Copyright <strong><span>NiceAdmin</span></strong>. All Rights Reserved
      &copy; Copyright <strong><span>Devminds</span></strong>. All Rights Reserved
      &copy; Copyright <strong><span>Aurora Boreal</span></strong>. All Rights Reserved
    </div>

  </footer><!-- End Footer -->

  <a href="#" class="back-to-top d-flex align-items-center justify-content-center"><i
      class="bi bi-arrow-up-short"></i></a>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/apexcharts/apexcharts.min.js"></script>
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/chart.js/chart.umd.js"></script>
  <script src="assets/vendor/echarts/echarts.min.js"></script>
  <script src="assets/vendor/quill/quill.js"></script>
  <script src="assets/vendor/simple-datatables/simple-datatables.js"></script>
  <script src="assets/vendor/tinymce/tinymce.min.js"></script>
  <script src="assets/vendor/php-email-form/validate.js"></script>

  <!-- Template Main JS File -->
  <script src="assets/js/main.js"></script>

</body>

</html>