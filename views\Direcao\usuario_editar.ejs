<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">

    <title>Alunos-Aurora Boreal</title>
    <meta content="" name="description">
    <meta content="" name="keywords">

    <!-- Favicons -->
    <link href="/static/img/AURORALOGOSFUNDO.ico" rel="icon">
    <link href="/static/img/AURORALOGOSFUNDO.ico" rel="apple-touch-icon">

    <!-- Google Fonts -->
    <link href="https://fonts.gstatic.com" rel="preconnect">
    <link
        href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Nunito:300,300i,400,400i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i"
        rel="stylesheet">

    <!-- Vendor CSS Files -->
    <link href="/static/assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
    <link href="/static/assets/vendor/quill/quill.snow.css" rel="stylesheet">
    <link href="/static/assets/vendor/quill/quill.bubble.css" rel="stylesheet">
    <link href="/static/assets/vendor/remixicon/remixicon.css" rel="stylesheet">
    <link href="/static/assets/vendor/simple-datatables/style.css" rel="stylesheet">

    <!-- Template Main CSS File -->
    <link href="/static/assets/css/style.css" rel="stylesheet">

    <!-- =======================================================
  * Template Name: NiceAdmin
  * Template URL: https://bootstrapmade.com/nice-admin-bootstrap-admin-html-template/
  * Updated: Apr 20 2024 with Bootstrap v5.3.3
  * Author: BootstrapMade.com
  * License: https://bootstrapmade.com/license/
======================================================== -->
</head>

<body>

    <!-- ======= Header ======= -->
    <header id="header" class="header fixed-top d-flex align-items-center">

        <div class="d-flex align-items-center justify-content-between">
            <a href="/" class="logo d-flex align-items-center">
                <img src="/static/img/AURORALOGOSFUNDO.ico" alt="">
                <span class="d-none d-lg-block">Aurora Boreal</span>
            </a>
            <i class="bi bi-list toggle-sidebar-btn"></i>
        </div><!-- End Logo -->




        </ul>
        </nav><!-- End Icons Navigation -->

    </header><!-- End Header -->


    <main id="main" class="main">
        <div class="pagetitle">
            <h1>Cadastro de Usuário</h1>
            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Pág.inicial</a></li>
                    <li class="breadcrumb-item active">Cadastro de Usuário</li>
                </ol>
            </nav>
        </div>
        <!-- End Page Title -->

        <section class="section register">
            <div class="row">
                <div class="col-lg-6 offset-lg-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Formulário de Cadastro</h5>

                            <form class="row g-3">
                                <div class="col-12">
                                    <label for="inputName" class="form-label">Nome Completo</label>
                                    <input type="text" class="form-control" id="inputName" required />
                                </div>
                                <div class="col-12">
                                    <label for="inputEmail" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="inputEmail" required />
                                </div>
                                <div class="col-12">
                                    <label for="inputPassword" class="form-label">Senha</label>
                                    <input type="password" class="form-control" id="inputPassword" required />
                                </div>
                                <div class="col-12">
                                    <label for="inputConfirmPassword" class="form-label">Confirme a Senha</label>
                                    <input type="password" class="form-control" id="inputConfirmPassword" required />
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary w-100">
                                        Cadastrar
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- ======= Footer ======= -->
    <footer id="footer" class="footer">
        <div class="copyright">
            &copy; Copyright <strong><span>NiceAdmin</span></strong>. All Rights Reserved
            &copy; Copyright <strong><span>Devminds</span></strong>. All Rights Reserved
            &copy; Copyright <strong><span>Aurora Boreal</span></strong>. All Rights Reserved
        </div>

    </footer><!-- End Footer -->

    <a href="#" class="back-to-top d-flex align-items-center justify-content-center"><i
            class="bi bi-arrow-up-short"></i></a>

    <!-- Vendor JS Files -->
    <script src="/static/assets/vendor/apexcharts/apexcharts.min.js"></script>
    <script src="/static/assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/static/assets/vendor/chart.js/chart.umd.js"></script>
    <script src="/static/assets/vendor/echarts/echarts.min.js"></script>
    <script src="/static/assets/vendor/quill/quill.js"></script>
    <script src="/static/assets/vendor/simple-datatables/simple-datatables.js"></script>
    <script src="/static/assets/vendor/tinymce/tinymce.min.js"></script>
    <script src="/static/assets/vendor/php-email-form/validate.js"></script>

    <!-- Template Main JS File -->
    <script src="/static/assets/js/main.js"></script>

    <script>
        const id = "<%= req.params.id %>";
        fetch(`/api/usuarios/${id}`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('inputName').value = data.nome;
                document.getElementById('inputEmail').value = data.email;
                document.getElementById('inputPassword').value = data.senha;
            })
    </script>

</body>

</html>