<body>
    <main id="main" class="main">
        <div class="pagetitle">
            <h1 class="materia-nome"></h1>
            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Pág.inicial</a></li>
                    <li class="breadcrumb-item active materia-nome"></li>
                </ol>
            </nav>
        </div><!-- End Page Title -->
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Con<PERSON><PERSON><PERSON> da Matéria</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="mb-3"></div>
                                        <h6>Conteúdo</h6>
                                        <button id="novoConteudoButton" class="btn btn-primary" data-bs-toggle="modal"
                                            data-bs-target="#novoConteudo">Novo <PERSON>údo</button>
                                        <div class="mb-3"></div>
                                        <ul class="list-group" id="conteudo-lista">
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="mb-3"></div>
                                        <div>
                                            <h6>Atividades</h6>
                                            <button id="novaAtividadeButton" class="btn btn-primary"
                                                data-bs-toggle="modal" data-bs-target="#novoAtividade">Nova
                                                Atividade</button>
                                        </div>
                                        <div class="mb-3"></div>
                                        <ul class="list-group" id="atividades-lista">
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="mb-3"></div>
                                <h6>Alunos</h6>
                                <div class="card">
                                    <div class="card-body">
                                        <div id="alunos">
                                            <table class="table table-hover table-striped" id="tabela-alunos">
                                                <thead>
                                                    <tr>
                                                        <th scope="col">ID</th>
                                                        <th scope="col">Aluno</th>
                                                        <th scope="col">Ações</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</body>

<!-- Modals -->

<!-- Modal para novo conteúdo -->
<div class="modal fade" id="novoConteudo" tabindex="-1" aria-labelledby="novoConteudoLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="novoConteudoLabel">Novo Conteúdo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="titulo-conteudo" class="form-label">Título</label>
                    <input type="text" class="form-control" id="titulo-conteudo" name="titulo" required>
                </div>
                <div class="mb-3">
                    <label for="descricao-conteudo" class="form-label">Descrição</label>
                    <textarea class="tinymce-editor" id="descricao-conteudo" name="descricao"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary">Salvar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para nova atividade -->
<div class="modal fade" id="novoAtividade" tabindex="-1" aria-labelledby="novoAtividadeLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="novoAtividadeLabel">Nova Atividade</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="titulo-atividade" class="form-label">Título</label>
                    <input type="text" class="form-control" id="titulo-atividade" name="titulo" required>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="tipo-atividade" class="form-label">Tipo de Atividade</label>
                            <select class="form-select" id="tipo-atividade" name="tipo">
                                <option value="0" selected>Questionário</option>
                                <option value="1">Envio de Arquivo</option>
                                <option value="2">Texto Online</option>
                                <option value="3">Prova</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="data-entrega" class="form-label">Data de Entrega</label>
                            <input type="datetime-local" class="form-control" id="data-entrega" name="data_entrega">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="bimestre" class="form-label">Bimestre</label>
                            <select class="form-select" id="bimestre" name="bimestre" required>
                                <option value="1">1º Bimestre</option>
                                <option value="2">2º Bimestre</option>
                                <option value="3">3º Bimestre</option>
                                <option value="4">4º Bimestre</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="peso" class="form-label">Peso da Atividade</label>
                            <input type="number" class="form-control" id="peso" name="peso" min="0" max="100" required>
                            <small class="text-muted peso-maximo">O peso total das atividades não pode exceder 100</small>
                        </div>
                    </div>
                </div>
                <div class="mb-3" id="prova-div" style="display: none;">
                    <input type="checkbox" class="form-check-input" id="check-prova" name="check-prova">
                    <label class="form-check-label" for="check-prova">Alunos podem Editar?</label>
                </div>
                <div class="mb-3">
                    <label for="anexo-atividade" class="form-label">Anexo (opcional)</label>
                    <input type="file" class="form-control" id="anexo-atividade" name="anexo_atividade">
                </div>
                <div class="mb-3">
                    <label for="descricao-atividade" class="form-label">Pergunta</label>
                    <textarea class="tinymce-editor" id="descricao-atividade" name="descricao"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary">Salvar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para visualizar conteúdo -->
<div class="modal fade" id="verConteudo" tabindex="-1" aria-labelledby="verConteudoLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="verConteudoLabel">Conteúdo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="titulo-conteudo" class="form-label">Título</label>
                    <input type="text" class="form-control" id="titulo-conteudo" name="titulo" disabled>
                </div>
                <div class="mb-3">
                    <label for="descricao-conteudo" class="form-label">Descrição</label>
                    <textarea class="tinymce-editor" id="descricao-conteudo" name="descricao" disabled></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para visualizar atividade -->
<div class="modal fade" id="verAtividade" tabindex="-1" aria-labelledby="verAtividadeLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="verAtividadeLabel">Atividade</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="titulo-atividade" class="form-label">Título</label>
                    <input type="text" class="form-control" id="titulo-atividade" name="titulo" disabled>
                </div>
                <div class="mb-3">
                    <label for="tipo-atividade" class="form-label">Tipo de Atividade</label>
                    <select class="form-select" id="tipo-atividade" name="tipo" disabled>
                        <option value="0">Questionário</option>
                        <option value="1">Envio de Arquivo</option>
                        <option value="2">Texto Online</option>
                        <option value="3">Prova</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="descricao-atividade" class="form-label">Pergunta</label>
                    <textarea class="tinymce-editor" id="descricao-ver-atividade" name="descricao" disabled></textarea>
                </div>
                <div class="mb-3">
                    <label for="data-entrega" class="form-label">Data de Entrega</label>
                    <input type="datetime-local" class="form-control" id="data-entrega" name="data_entrega" disabled>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>

<div>
    <div class="modal fade" id="editarAtividade" tabindex="-1" aria-labelledby="editarAtividadeLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editarAtividadeLabel">Editar Atividade</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="titulo-atividade" class="form-label">Título</label>
                        <input type="text" class="form-control" id="titulo-atividade" name="titulo" required>
                    </div>
                    <div class="mb-3">
                        <label for="tipo-atividade" class="form-label">Tipo de Atividade</label>
                        <select class="form-select" id="tipo-atividade" name="tipo">
                            <option value="0">Questionário</option>
                            <option value="1">Envio de Arquivo</option>
                            <option value="2">Texto Online</option>
                            <option value="3">Prova</option>
                        </select>
                        <input type="hidden" name="id" id="id-atividade">
                    </div>
                    <div class="mb-3">
                        <label for="descricao-atividade" class="form-label">Pergunta</label>
                        <textarea class="tinymce-editor" id="descricao-editar-atividade" name="descricao"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="data-entrega" class="form-label">Data de Entrega</label>
                        <input type="datetime-local" class="form-control" id="data-entrega" name="data_entrega">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary">Salvar</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para visualizar e corrigir respostas -->

<div>
    <div class="modal fade" id="verResposta" tabindex="-1" aria-labelledby="verRespostaLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="verRespostaLabel">Resposta</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="atividade-titulo" class="form-label">Atividade</label>
                        <h3 id="atividade-titulo"></h3>
                    </div>
                    <div class="mb-3">
                        <label for="resposta" class="form-label">Resposta</label>
                        <textarea class="tinymce-editor" id="resposta" name="resposta" disabled></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="anexo-resposta" class="form-label">Anexo</label>
                        <a id="anexo-resposta-link" href="" download class="btn btn-primary" style="display: none;">Download do Anexo</a>
                    </div>
                    <div class="mb-3">
                        <label for="nota" class="form-label">Nota</label>
                        <input type="number" class="form-control" id="nota" name="nota" min="0" max="10" step="0.1">
                    </div>
                    <div class="mb-3">
                        <label for="comentario-professor" class="form-label">Comentário do Professor</label>
                        <textarea class="tinymce-editor" id="comentario-professor" name="comentario-professor"></textarea>
                    </div>
                    <input type="hidden" name="resposta-id" id="resposta-id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary">Salvar</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para visualizar todas as respostas do aluno por atividade -->

<div>
    <div class="modal fade" id="verTodasRespostas" tabindex="-1" aria-labelledby="verTodasRespostasLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="verTodasRespostasLabel">Respostas do Aluno</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <table class="table table-hover table-striped">
                        <thead>
                            <tr>
                                <th>Atividade</th>
                                <th>Data de Envio</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="respostas-body">
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>
</div>



</div>




</div>





</div>







<script src="/js/professor_materia.js"></script>
