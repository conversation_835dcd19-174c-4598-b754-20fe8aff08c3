{"version": 3, "file": "quill.bubble.css", "mappings": ";;;;;;;AAWA,cACE,qBAAY,CACZ,sCAAsB,CACtB,cAAW,CACX,WAAQ,CACR,QAAQ,CACR,iBAAU,CAGV,sCACE,iBAAY,CAKZ,gIACE,cAAQ,CAEd,cACE,cAAM,CACN,UAAQ,CACR,iBAAY,CACZ,iBAAU,CACV,OAAK,CACL,gBACE,QAAQ,CACR,SAAS,CAEb,WACE,qBAAY,CACZ,mFAAe,CACf,gBAAa,CACb,WAAQ,CACR,YAAS,CACT,eAAY,CACZ,iBAAS,CACT,UAAU,CACV,eAAe,CACf,eAAY,CACZ,oBAAa,CACb,oBAAW,CACX,eACE,WAAQ,CAEV,oJACE,QAAQ,CACR,SAAS,CAEmB,8HAC1B,iFAAa,EACiB,kIAC9B,mFAAe,EACnB,iBACE,wBAAiB,CACnB,cACE,qBAAQ,CACR,eAAS,CACX,cACE,kBAAc,CAChB,cACE,oBAAiB,CACjB,kBAAc,CACd,iBAAU,CAEV,8BACE,oBAAS,CACT,kBAAgB,CAChB,iBAAc,CACd,gBAAY,CACZ,kBAAa,CACb,WAAO,CAIT,sFACE,UAAO,CAEX,gDACE,eAAS,CACX,iDACE,eAAS,CACX,mDACE,eAAS,CAGmB,sDAC1B,0EAAa,EACiB,0DAC9B,4EAAe,EAEnB,iCACE,wBAAmB,CACnB,iDACE,qCAAS,CAEX,6CACE,wBAAmB,CACnB,6DACE,yCAAS,CAGmB,kEAC1B,mEAAa,EACiB,sEAC9B,qEAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,yCAAS,CAGmB,kEAC1B,4DAAa,EACiB,sEAC9B,8DAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,qCAAS,CAGmB,kEAC1B,qDAAa,EACiB,sEAC9B,uDAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,yCAAS,CAGmB,kEAC1B,8CAAa,EACiB,sEAC9B,gDAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,yCAAS,CAGmB,kEAC1B,uCAAa,EACiB,sEAC9B,yCAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,qCAAS,CAGmB,kEAC1B,gCAAa,EACiB,sEAC9B,kCAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,yCAAS,CAGmB,kEAC1B,yBAAa,EACiB,sEAC9B,2BAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,yCAAS,CAGmB,kEAC1B,kBAAa,EACiB,sEAC9B,oBAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,qCAAS,CASb,+CACE,gBAAiB,CACnB,iDACE,kBAAuB,CACzB,wDACE,iBAAkB,CACpB,0DACE,mBAAwB,CAP1B,+CACE,gBAAiB,CACnB,iDACE,kBAAuB,CACzB,wDACE,iBAAkB,CACpB,0DACE,mBAAwB,CAP1B,+CACE,gBAAiB,CACnB,iDACE,mBAAuB,CACzB,wDACE,iBAAkB,CACpB,0DACE,oBAAwB,CAP1B,+CACE,iBAAiB,CACnB,iDACE,mBAAuB,CACzB,wDACE,kBAAkB,CACpB,0DACE,oBAAwB,CAP1B,+CACE,iBAAiB,CACnB,iDACE,mBAAuB,CACzB,wDACE,kBAAkB,CACpB,0DACE,oBAAwB,CAP1B,+CACE,iBAAiB,CACnB,iDACE,mBAAuB,CACzB,wDACE,kBAAkB,CACpB,0DACE,oBAAwB,CAP1B,+CACE,iBAAiB,CACnB,iDACE,mBAAuB,CACzB,wDACE,kBAAkB,CACpB,0DACE,oBAAwB,CAP1B,+CACE,iBAAiB,CACnB,iDACE,mBAAuB,CACzB,wDACE,kBAAkB,CACpB,0DACE,oBAAwB,CAP1B,+CACE,iBAAiB,CACnB,iDACE,mBAAuB,CACzB,wDACE,kBAAkB,CACpB,0DACE,oBAAwB,CAE5B,+BACE,mBAAe,CACf,+CACE,gBAAa,CACb,mBAAiB,CACjB,eAAY,CAEhB,iBACE,kBAAc,CACd,UAAO,CACP,oBACE,YAAS,CAEb,oCACE,qBAAa,CAEf,qBACE,aAAS,CACT,cAAW,CACb,qCACE,aAAQ,CACV,oCACE,iBAAQ,CAEV,wBACE,qBAAkB,CACpB,sBACE,wBAAkB,CACpB,yBACE,qBAAkB,CACpB,yBACE,qBAAkB,CACpB,wBACE,wBAAkB,CACpB,uBACE,qBAAkB,CACpB,yBACE,qBAAkB,CAEpB,2BACE,UAAO,CACT,yBACE,aAAO,CACT,4BACE,UAAO,CACT,4BACE,UAAO,CACT,2BACE,aAAO,CACT,0BACE,UAAO,CACT,4BACE,UAAO,CAET,0BACE,yCAAoB,CACtB,8BACE,wCAAmB,CAErB,0BACE,eAAW,CACb,0BACE,eAAW,CACb,yBACE,eAAW,CAEb,6BACE,aAAW,CACX,kBAAY,CAEd,4BACE,iBAAY,CACd,6BACE,kBAAY,CACd,2BACE,gBAAY,CAEd,kBACE,iBAAU,CAEd,4BACE,qBAAO,CACP,8BAAS,CACT,iBAAY,CACZ,SAAM,CACN,mBAAgB,CAChB,iBAAU,CACV,UAAO,CC1MP,yDACE,UAAO,CACP,UAAS,CACT,aAAS,CAEX,2DACE,eAAY,CACZ,WAAQ,CACR,cAAQ,CACR,oBAAS,CACT,UAAO,CACP,WAAQ,CACR,eAAS,CACT,UAAgE,CAEhE,mEACE,UAAO,CACP,WAAQ,CAEV,qFACE,YAAS,CAEb,iGACE,YAAS,CAEX,ylBAGE,UAAO,CACP,0jDACE,SAAM,CACR,0jDACE,WAAQ,CAGS,wBAEnB,uGACE,UAAO,CACP,sQACE,SAAM,CACR,sQACE,WAAQ,EAEhB,WACE,qBAAY,CACZ,aACE,qBAAY,CAEd,sBACE,YAAS,CACX,iDACE,iBAAY,CAEd,uBACE,iBAAU,CACV,0BAAW,CACX,yBACE,cAAQ,CACR,oBAAiB,CACrB,+BACE,2BAAW,CAEb,uBAKE,oBAAS,CACT,qBAAgB,CALhB,6BACE,UAAO,CACP,UAAS,CACT,aAAS,CAIb,sBACE,SAAM,CACN,WAAQ,CACR,oBAAgB,CAChB,qBAAiB,CACjB,cAAc,CAChB,4BACE,SAAM,CACN,WAAQ,CACR,oBAAmB,CACnB,cAAc,CAEhB,kDACE,SAAM,CAER,qBACE,SAAM,CACR,oBACE,iBAAW,CACb,kDACE,cAAc,CAChB,2BACE,UAAS,CAGT,wCACE,YAAS,CAEX,kDACE,cAAS,CACX,mDACE,YAAS,CAGX,yBACE,aAAW,CACb,yBACE,eAAW,CACb,yBACE,gBAAW,CACb,yBACE,aAAW,CACb,yBACE,eAAW,CACb,yBACE,eAAW,CACb,wBACE,yBAAiB,CACnB,iCACE,0BAAa,CACb,iBAAe,CACf,cAAY,CACZ,iBAAc,CAChB,0EACE,wBAAkB,CAClB,iBAAe,CACjB,+CACE,iBAAe,CACf,cAAY,CACZ,gBAAS,CACX,2BACE,aAAW,CACX,eAAS,CACX,+CACE,wBAAkB,CAClB,aAAO,CACP,gBAAU,CACZ,0BACE,cAAW,CAEf,sBACE,UAAO,CACP,oBAAS,CACT,UAAO,CACP,cAAW,CACX,eAAa,CACb,WAAQ,CACR,iBAAU,CACV,qBAAgB,CAClB,4BACE,cAAQ,CACR,oBAAS,CACT,WAAQ,CACR,gBAAc,CACd,iBAAe,CACf,iBAAU,CACV,UAAO,CACP,oCACE,oBAAS,CACT,gBAAa,CACjB,8BACE,qBAAkB,CAClB,YAAS,CACT,cAAW,CACX,eAAS,CACT,iBAAU,CACV,kBAAa,CACb,8CACE,cAAQ,CACR,aAAS,CACT,kBAAgB,CAChB,eAAa,CAEf,mDACE,UAAO,CACP,SAAS,CACT,4DACE,SAAM,CACR,8DACE,WAAQ,CACZ,qDACE,aAAS,CACT,eAAY,CACZ,QAAK,CACL,SAAS,CAEb,uDACE,UAAuB,CACvB,yFACE,eAAS,CACT,iGACE,SAAO,CAEX,8CACE,aAAS,CACX,2CACE,WAAQ,CACR,UAAO,CACP,eAAS,CAEX,+CACE,eAAS,CACT,WAAsF,CACxF,4CACE,4BAAQ,CACR,UAAO,CACP,WAAQ,CACR,UAAQ,CACR,SAAS,CACT,UAAO,CAGT,qEACE,iBAAU,CACV,eAAY,CACZ,OAAO,CACP,OAAK,CACL,UAAO,CAKP,ihBACE,wBAAS,CAEf,gCACE,UAAO,CACP,iHAEE,gBAAS,CAET,iJAEE,mBAAsB,CAFxB,iJAEE,mBAAsB,CAFxB,iJAEE,mBAAsB,CAFxB,iJAEE,mBAAsB,CAFxB,iJAEE,mBAAsB,CAFxB,iJAEE,mBAAsB,CAC1B,wEACE,aAAW,CACb,wEACE,eAAW,CACb,wEACE,gBAAW,CACb,wEACE,aAAW,CACb,wEACE,eAAW,CACb,wEACE,eAAW,CAEf,8BACE,WAAO,CACP,6GAEE,oBAAS,CACX,iJAEE,eAAS,CACX,yJAEE,mBAAS,CACX,wEACE,yCAAoB,CACtB,4EACE,wCAAmB,CAEvB,8BACE,UAAO,CACP,6GAEE,gBAAS,CACX,iJAEE,eAAS,CACX,iJAEE,eAAS,CACX,+IAEE,cAAS,CACX,wEACE,cAAW,CACb,wEACE,cAAW,CACb,uEACE,cAAW,CAGb,0DACE,qBAAkB,CAEpB,qDACE,qBAAkB,CAExB,yBACE,iBAAU,CACV,gCACE,SAAO,CACP,OAAK,CChTL,mCACE,qBAAQ,CACV,+CACE,gBAAa,CAGf,gCACE,UAAQ,CACV,0GACE,iBAAc,CCVlB,uBACE,qBAAkB,CAClB,kBAAe,CACf,UAAO,CACT,6BACE,iCAAa,CACb,kCAAc,CACd,WAAS,CACT,aAAS,CACT,QAAM,CACN,gBAAkB,CAClB,iBAAU,CACZ,uDACE,4BAAe,CACf,QAAU,CACZ,iDACE,yBAAY,CACZ,WAAa,CAGb,qDACE,aAAS,CACX,8CACE,iBAAY,CAEhB,8BACE,YAAS,CACT,+CACE,sBAAY,CACZ,WAAQ,CACR,UAAO,CACP,cAAW,CACX,WAAQ,CACR,YAAS,CACT,iBAAS,CACT,iBAAU,CACV,UAAO,CACT,gCAME,QAAK,CACL,iBAAU,CACV,UAAO,CAPP,uCACE,UAAO,CACP,eAAS,CACT,cAAW,CACX,gBAAa,CChCnB,2DACE,iBAAU,CACV,kBAAa,CACf,mEACE,qBAAkB,CAClB,kBAAe,CACf,QAAK,CACL,cAAW,CACX,UAAO,CACP,kBAAS,CACT,kBAAa,CACb,eAAU,CACV,gBAAS,CACT,oBAAiB,CACjB,SAAS,CACX,kEACE,yBAAY,CACZ,iCAAa,CACb,kCAAc,CACd,KAAK,CACL,WAAS,CACT,QAAQ,CACR,OAAO,CACT,qIACE,MAAM,CACN,eAAa,CACb,iBAAU,CACV,+BAAW,CACX,mCAAY,CACZ,iBAAY,CACd,iJACE,kBAAY", "sources": ["webpack://Quill/./src/assets/core.styl", "webpack://Quill/./src/assets/base.styl", "webpack://Quill/./src/assets/bubble/toolbar.styl", "webpack://Quill/./src/assets/bubble/tooltip.styl", "webpack://Quill/./src/assets/bubble.styl"], "sourcesContent": ["// Styles necessary for Quill\n\nLIST_STYLE = decimal lower-alpha lower-roman\nLIST_STYLE_WIDTH = 1.2em\nLIST_STYLE_MARGIN = 0.3em\nLIST_STYLE_OUTER_WIDTH = LIST_STYLE_MARGIN + LIST_STYLE_WIDTH\nMAX_INDENT = 9\n\nresets(arr)\n  unquote('list-' + join(' list-', arr))\n\n.ql-container\n  box-sizing: border-box\n  font-family: Helvetica, Arial, sans-serif\n  font-size: 13px\n  height: 100%\n  margin: 0px\n  position: relative\n\n.ql-container.ql-disabled\n  .ql-tooltip\n    visibility: hidden\n\n.ql-container:not(.ql-disabled)\n  li[data-list=checked],\n  li[data-list=unchecked]\n    > .ql-ui\n      cursor: pointer\n\n.ql-clipboard\n  left: -100000px\n  height: 1px\n  overflow-y: hidden\n  position: absolute\n  top: 50%\n  p\n    margin: 0\n    padding: 0\n\n.ql-editor\n  box-sizing: border-box\n  counter-reset: resets(0..MAX_INDENT)\n  line-height: 1.42\n  height: 100%\n  outline: none\n  overflow-y: auto\n  padding: 12px 15px\n  tab-size: 4\n  -moz-tab-size: 4\n  text-align: left\n  white-space: pre-wrap\n  word-wrap: break-word\n  > *\n    cursor: text\n\n  p, ol, pre, blockquote, h1, h2, h3, h4, h5, h6\n    margin: 0\n    padding: 0\n  p, h1, h2, h3, h4, h5, h6\n    @supports (counter-set: none)\n      counter-set: resets(0..MAX_INDENT)\n    @supports not (counter-set: none)\n      counter-reset: resets(0..MAX_INDENT)\n  table\n    border-collapse: collapse\n  td\n    border: 1px solid #000\n    padding: 2px 5px\n  ol\n    padding-left: 1.5em\n  li\n    list-style-type: none\n    padding-left: LIST_STYLE_OUTER_WIDTH\n    position: relative\n\n    > .ql-ui:before\n      display: inline-block\n      margin-left: -1*LIST_STYLE_OUTER_WIDTH\n      margin-right: LIST_STYLE_MARGIN\n      text-align: right\n      white-space: nowrap\n      width: LIST_STYLE_WIDTH\n\n  li[data-list=checked],\n  li[data-list=unchecked]\n    > .ql-ui\n      color: #777\n\n  li[data-list=bullet] > .ql-ui:before\n    content: '\\2022'\n  li[data-list=checked] > .ql-ui:before\n    content: '\\2611'\n  li[data-list=unchecked] > .ql-ui:before\n    content: '\\2610'\n\n  li[data-list]\n    @supports (counter-set: none)\n      counter-set: resets(1..MAX_INDENT)\n    @supports not (counter-set: none)\n      counter-reset: resets(1..MAX_INDENT)\n\n  li[data-list=ordered]\n    counter-increment: list-0\n    > .ql-ui:before\n      content: unquote('counter(list-0, ' + LIST_STYLE[0] + ')') '. '\n  for num in (1..MAX_INDENT)\n    li[data-list=ordered].ql-indent-{num}\n      counter-increment: unquote('list-' + num)\n      > .ql-ui:before\n        content: unquote('counter(list-' + num + ', ' + LIST_STYLE[num%3] + ')') '. '\n    if (num < MAX_INDENT)\n      li[data-list].ql-indent-{num}\n        @supports (counter-set: none)\n          counter-set: resets((num+1)..MAX_INDENT)\n        @supports not (counter-set: none)\n          counter-reset: resets((num+1)..MAX_INDENT)\n\n  for num in (1..MAX_INDENT)\n    .ql-indent-{num}:not(.ql-direction-rtl)\n      padding-left: (3*num)em\n    li.ql-indent-{num}:not(.ql-direction-rtl)\n      padding-left: (3*num + LIST_STYLE_OUTER_WIDTH)em\n    .ql-indent-{num}.ql-direction-rtl.ql-align-right\n      padding-right: (3*num)em\n    li.ql-indent-{num}.ql-direction-rtl.ql-align-right\n      padding-right: (3*num + LIST_STYLE_OUTER_WIDTH)em\n\n  li.ql-direction-rtl\n    padding-right: LIST_STYLE_OUTER_WIDTH\n    > .ql-ui:before\n      margin-left: LIST_STYLE_MARGIN\n      margin-right: -1*LIST_STYLE_OUTER_WIDTH\n      text-align: left\n\n  table\n    table-layout: fixed\n    width: 100%\n    td\n      outline: none\n\n  .ql-code-block-container\n    font-family: monospace\n\n  .ql-video\n    display: block\n    max-width: 100%\n  .ql-video.ql-align-center\n    margin: 0 auto\n  .ql-video.ql-align-right\n    margin: 0 0 0 auto\n\n  .ql-bg-black\n    background-color: rgb(0,0,0)\n  .ql-bg-red\n    background-color: rgb(230,0,0)\n  .ql-bg-orange\n    background-color: rgb(255,153,0)\n  .ql-bg-yellow\n    background-color: rgb(255,255,0)\n  .ql-bg-green\n    background-color: rgb(0,138,0)\n  .ql-bg-blue\n    background-color: rgb(0,102,204)\n  .ql-bg-purple\n    background-color: rgb(153,51,255)\n\n  .ql-color-white\n    color: rgb(255,255,255)\n  .ql-color-red\n    color: rgb(230,0,0)\n  .ql-color-orange\n    color: rgb(255,153,0)\n  .ql-color-yellow\n    color: rgb(255,255,0)\n  .ql-color-green\n    color: rgb(0,138,0)\n  .ql-color-blue\n    color: rgb(0,102,204)\n  .ql-color-purple\n    color: rgb(153,51,255)\n\n  .ql-font-serif\n    font-family: Georgia, Times New Roman, serif\n  .ql-font-monospace\n    font-family: Monaco, Courier New, monospace\n\n  .ql-size-small\n    font-size: 0.75em\n  .ql-size-large\n    font-size: 1.5em\n  .ql-size-huge\n    font-size: 2.5em\n\n  .ql-direction-rtl\n    direction: rtl\n    text-align: inherit\n\n  .ql-align-center\n    text-align: center\n  .ql-align-justify\n    text-align: justify\n  .ql-align-right\n    text-align: right\n\n  .ql-ui\n    position: absolute\n\n.ql-editor.ql-blank::before\n  color: rgba(0,0,0,0.6)\n  content: attr(data-placeholder)\n  font-style: italic\n  left: 15px\n  pointer-events: none\n  position: absolute\n  right: 15px\n", "// Styles shared between snow and bubble\n\ncontrolHeight = 24px\ninputPaddingWidth = 5px\ninputPaddingHeight = 3px\n\ncolorItemMargin = 2px\ncolorItemSize = 16px\ncolorItemsPerRow = 7\n\n\n.ql-{themeName}.ql-toolbar, .ql-{themeName} .ql-toolbar\n  &:after\n    clear: both\n    content: ''\n    display: table\n\n  button\n    background: none\n    border: none\n    cursor: pointer\n    display: inline-block\n    float: left\n    height: controlHeight\n    padding: inputPaddingHeight inputPaddingWidth\n    width: controlHeight + (inputPaddingWidth - inputPaddingHeight)*2\n\n    svg\n      float: left\n      height: 100%\n\n    &:active:hover\n      outline: none\n\n  input.ql-image[type=file]\n    display: none\n\n  button:hover, button:focus, button.ql-active,\n  .ql-picker-label:hover, .ql-picker-label.ql-active,\n  .ql-picker-item:hover, .ql-picker-item.ql-selected\n    color: activeColor\n    .ql-fill, .ql-stroke.ql-fill\n      fill: activeColor\n    .ql-stroke, .ql-stroke-miter\n      stroke: activeColor\n\n// Fix for iOS not losing hover on touch\n@media (pointer: coarse)\n  .ql-{themeName}.ql-toolbar, .ql-{themeName} .ql-toolbar\n    button:hover:not(.ql-active)\n      color: inactiveColor\n      .ql-fill, .ql-stroke.ql-fill\n        fill: inactiveColor\n      .ql-stroke, .ql-stroke-miter\n        stroke: inactiveColor\n\n.ql-{themeName}\n  box-sizing: border-box\n  *\n    box-sizing: border-box\n\n  .ql-hidden\n    display: none\n  .ql-out-bottom, .ql-out-top\n    visibility: hidden\n\n  .ql-tooltip\n    position: absolute\n    transform: translateY(10px)\n    a\n      cursor: pointer\n      text-decoration: none\n  .ql-tooltip.ql-flip\n    transform: translateY(-10px)\n\n  .ql-formats\n    &:after\n      clear: both\n      content: ''\n      display: table\n    display: inline-block\n    vertical-align: middle\n\n  .ql-stroke\n    fill: none\n    stroke: inactiveColor\n    stroke-linecap: round\n    stroke-linejoin: round\n    stroke-width: 2\n  .ql-stroke-miter\n    fill: none\n    stroke: inactiveColor\n    stroke-miterlimit: 10\n    stroke-width: 2\n\n  .ql-fill, .ql-stroke.ql-fill\n    fill: inactiveColor\n\n  .ql-empty\n    fill: none\n  .ql-even\n    fill-rule: evenodd\n  .ql-thin, .ql-stroke.ql-thin\n    stroke-width: 1\n  .ql-transparent\n    opacity: 0.4\n\n  .ql-direction\n    svg:last-child\n      display: none\n  .ql-direction.ql-active\n    svg:last-child\n      display: inline\n    svg:first-child\n      display: none\n\n  .ql-editor\n    h1\n      font-size: 2em\n    h2\n      font-size: 1.5em\n    h3\n      font-size: 1.17em\n    h4\n      font-size: 1em\n    h5\n      font-size: 0.83em\n    h6\n      font-size: 0.67em\n    a\n      text-decoration: underline\n    blockquote\n      border-left: 4px solid #ccc\n      margin-bottom: 5px\n      margin-top: 5px\n      padding-left: 16px\n    code, .ql-code-block-container\n      background-color: #f0f0f0\n      border-radius: 3px\n    .ql-code-block-container\n      margin-bottom: 5px\n      margin-top: 5px\n      padding: 5px 10px\n    code\n      font-size: 85%\n      padding: 2px 4px\n    .ql-code-block-container\n      background-color: #23241f\n      color: #f8f8f2\n      overflow: visible\n    img\n      max-width: 100%\n\n  .ql-picker\n    color: inactiveColor\n    display: inline-block\n    float: left\n    font-size: 14px\n    font-weight: 500\n    height: controlHeight\n    position: relative\n    vertical-align: middle\n  .ql-picker-label\n    cursor: pointer\n    display: inline-block\n    height: 100%\n    padding-left: 8px\n    padding-right: 2px\n    position: relative\n    width: 100%\n    &::before\n      display: inline-block\n      line-height: 22px\n  .ql-picker-options\n    background-color: backgroundColor\n    display: none\n    min-width: 100%\n    padding: 4px 8px\n    position: absolute\n    white-space: nowrap\n    .ql-picker-item\n      cursor: pointer\n      display: block\n      padding-bottom: 5px\n      padding-top: 5px\n  .ql-picker.ql-expanded\n    .ql-picker-label\n      color: borderColor\n      z-index: 2\n      .ql-fill\n        fill: borderColor\n      .ql-stroke\n        stroke: borderColor\n    .ql-picker-options\n      display: block\n      margin-top: -1px\n      top: 100%\n      z-index: 1\n\n  .ql-color-picker, .ql-icon-picker\n    width: controlHeight + 4\n    .ql-picker-label\n      padding: 2px 4px\n      svg\n        right: 4px\n  .ql-icon-picker\n    .ql-picker-options\n      padding: 4px 0px\n    .ql-picker-item\n      height: controlHeight\n      width: controlHeight\n      padding: 2px 4px\n  .ql-color-picker\n    .ql-picker-options\n      padding: inputPaddingHeight inputPaddingWidth\n      width: (colorItemSize + 2*colorItemMargin) * colorItemsPerRow + 2*inputPaddingWidth + 2  // +2 for the border\n    .ql-picker-item\n      border: 1px solid transparent\n      float: left\n      height: colorItemSize\n      margin: colorItemMargin\n      padding: 0px\n      width: colorItemSize\n\n  .ql-picker:not(.ql-color-picker):not(.ql-icon-picker)\n    svg\n      position: absolute\n      margin-top: -9px\n      right: 0\n      top: 50%\n      width: 18px\n\n  .ql-picker.ql-header, .ql-picker.ql-font, .ql-picker.ql-size\n    .ql-picker-label[data-label]:not([data-label='']),\n    .ql-picker-item[data-label]:not([data-label=''])\n      &::before\n        content: attr(data-label)\n\n  .ql-picker.ql-header\n    width: 98px\n    .ql-picker-label::before,\n    .ql-picker-item::before\n      content: 'Normal'\n    for num in (1..6)\n      .ql-picker-label[data-value=\\\"{num}\\\"]::before,\n      .ql-picker-item[data-value=\\\"{num}\\\"]::before\n        content: 'Heading ' + num\n    .ql-picker-item[data-value=\"1\"]::before\n      font-size: 2em\n    .ql-picker-item[data-value=\"2\"]::before\n      font-size: 1.5em\n    .ql-picker-item[data-value=\"3\"]::before\n      font-size: 1.17em\n    .ql-picker-item[data-value=\"4\"]::before\n      font-size: 1em\n    .ql-picker-item[data-value=\"5\"]::before\n      font-size: 0.83em\n    .ql-picker-item[data-value=\"6\"]::before\n      font-size: 0.67em\n\n  .ql-picker.ql-font\n    width: 108px\n    .ql-picker-label::before,\n    .ql-picker-item::before\n      content: 'Sans Serif'\n    .ql-picker-label[data-value=serif]::before,\n    .ql-picker-item[data-value=serif]::before\n      content: 'Serif'\n    .ql-picker-label[data-value=monospace]::before,\n    .ql-picker-item[data-value=monospace]::before\n      content: 'Monospace'\n    .ql-picker-item[data-value=serif]::before\n      font-family: Georgia, Times New Roman, serif\n    .ql-picker-item[data-value=monospace]::before\n      font-family: Monaco, Courier New, monospace\n\n  .ql-picker.ql-size\n    width: 98px\n    .ql-picker-label::before,\n    .ql-picker-item::before\n      content: 'Normal'\n    .ql-picker-label[data-value=small]::before,\n    .ql-picker-item[data-value=small]::before\n      content: 'Small'\n    .ql-picker-label[data-value=large]::before,\n    .ql-picker-item[data-value=large]::before\n      content: 'Large'\n    .ql-picker-label[data-value=huge]::before,\n    .ql-picker-item[data-value=huge]::before\n      content: 'Huge'\n    .ql-picker-item[data-value=small]::before\n      font-size: 10px\n    .ql-picker-item[data-value=large]::before\n      font-size: 18px\n    .ql-picker-item[data-value=huge]::before\n      font-size: 32px\n\n  .ql-color-picker.ql-background\n    .ql-picker-item\n      background-color: #fff\n  .ql-color-picker.ql-color\n    .ql-picker-item\n      background-color: #000\n\n.ql-code-block-container\n  position: relative\n  .ql-ui\n    right: 5px\n    top: 5px\n", "arrowWidth = 6px\n\n.ql-bubble\n  .ql-toolbar\n    .ql-formats\n      margin: 8px 12px 8px 0px\n    .ql-formats:first-child\n      margin-left: 12px\n\n  .ql-color-picker\n    svg\n      margin: 1px\n    .ql-picker-item.ql-selected, .ql-picker-item:hover\n      border-color: activeColor\n", "arrowWidth = 6px\n\n.ql-bubble\n  .ql-tooltip\n    background-color: backgroundColor\n    border-radius: 25px\n    color: textColor\n  .ql-tooltip-arrow\n    border-left: arrowWidth solid transparent\n    border-right: arrowWidth solid transparent\n    content: \" \"\n    display: block\n    left: 50%\n    margin-left: -1 * arrowWidth\n    position: absolute\n  .ql-tooltip:not(.ql-flip) .ql-tooltip-arrow\n    border-bottom: arrowWidth solid backgroundColor\n    top: -1 * arrowWidth\n  .ql-tooltip.ql-flip .ql-tooltip-arrow\n    border-top: arrowWidth solid backgroundColor\n    bottom: -1 * arrowWidth\n\n  .ql-tooltip.ql-editing\n    .ql-tooltip-editor\n      display: block\n    .ql-formats\n      visibility: hidden\n\n  .ql-tooltip-editor\n    display: none\n    input[type=text]\n      background: transparent\n      border: none\n      color: textColor\n      font-size: 13px\n      height: 100%\n      outline: none\n      padding: 10px 20px\n      position: absolute\n      width: 100%\n    a\n      &:before\n        color: inactiveColor\n        content: \"\\00D7\"\n        font-size: 16px\n        font-weight: bold\n      top: 10px\n      position: absolute\n      right: 20px\n", "themeName = 'bubble'\nactiveColor = #fff\nborderColor = #777\nbackgroundColor = #444\ninactiveColor = #ccc\nshadowColor = #ddd\ntextColor = #fff\n\n@import './core'\n@import './base'\n@import './bubble/*'\n\n.ql-container.ql-bubble:not(.ql-disabled)\n  a:not(.ql-close)\n    position: relative\n    white-space: nowrap\n  a:not(.ql-close)::before\n    background-color: #444\n    border-radius: 15px\n    top: -5px\n    font-size: 12px\n    color: #fff\n    content: attr(href)\n    font-weight: normal\n    overflow: hidden\n    padding: 5px 15px\n    text-decoration: none\n    z-index: 1\n  a:not(.ql-close)::after\n    border-top: 6px solid #444\n    border-left: 6px solid transparent\n    border-right: 6px solid transparent\n    top: 0\n    content: \" \"\n    height: 0\n    width: 0\n  a:not(.ql-close)::before, a:not(.ql-close)::after\n    left: 0\n    margin-left: 50%\n    position: absolute\n    transform: translate(-50%, -100%)\n    transition: visibility 0s ease 200ms\n    visibility: hidden\n  a:not(.ql-close):hover::before, a:not(.ql-close):hover::after\n    visibility: visible\n"], "names": [], "sourceRoot": ""}