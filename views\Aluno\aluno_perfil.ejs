<!DOCTYPE html>
<html lang="pt-br">

<head>
  <style>
    .info-item {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 5px;
      border-left: 4px solid #0d6efd;
    }

    .info-item strong {
      color: #495057;
      display: block;
      margin-bottom: 5px;
    }

    .info-item p {
      margin: 0;
      color: #212529;
      font-weight: 500;
    }
  </style>
</head>
<body>

  <main id="main" class="main">

    <div class="pagetitle">
      <h1>Perfil do Aluno</h1>
      <nav>
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="/system/alunos">Dashboard</a></li>
          <li class="breadcrumb-item active">Dados do Perfil</li>
        </ol>
      </nav>
    </div><!-- End Page Title -->

    <section class="section dashboard">
      <div class="row">

        <!-- Left side columns -->
        <div class="col-lg-8">
          <div class="row">

            <!-- <PERSON><PERSON> do Aluno Card -->
            <div class="col-12">
              <div class="card info-card">
                <div class="card-body">
                  <h5 class="card-title">Dados do Aluno</h5>

                  <div class="row">
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>Nome:</strong>
                        <p class="nome_aluno">João da Silva</p>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>CPF:</strong>
                        <p class="cpf_aluno">123.456.789-00</p>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>Data de Nascimento:</strong>
                        <p class="data_nascimento">15/03/2005</p>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>Email:</strong>
                        <p class="email_aluno"><EMAIL></p>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>Turma:</strong>
                        <p class="turma_nome">3°A - 2024</p>
                      </div>
                    </div>
                    
                  </div>
                </div>
              </div>
            </div><!-- End Dados do Aluno Card -->

            <!-- Dados do Responsável Card -->
            <div class="col-12">
              <div class="card info-card">
                <div class="card-body">
                  <h5 class="card-title">Dados do Responsável</h5>

                  <div class="row">
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>Nome:</strong>
                        <p class="nome_responsavel">Maria da Silva</p>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>CPF:</strong>
                        <p class="cpf_responsavel">987.654.321-00</p>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>Telefone:</strong>
                        <p class="telefone_responsavel">(18) 99999-8888</p>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>Email:</strong>
                        <p><EMAIL></p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div><!-- End Dados do Responsável Card -->

          </div>
        </div><!-- End Left side columns -->

        <!-- Right side columns -->
        <div class="col-lg-4">

          <!-- Editar Dados Card -->
          <div class="card">
            <div class="card-body">
              <h5 class="card-title">Editar Dados</h5>

              <form id="form-editar-perfil" class="row g-3">
                <div class="col-12">
                  <label for="nome" class="form-label">Nome do Aluno</label>
                  <input type="text" class="form-control nome_aluno" id="nome" name="nome" value="João da Silva">
                </div>

                <div class="col-12">
                  <label for="email" class="form-label">Email</label>
                  <input type="email" class="form-control email_aluno" id="email" name="email" value="<EMAIL>">
                </div>

                <div class="col-12">
                  <label for="responsavel" class="form-label">Nome do Responsável</label>
                  <input type="text" class="form-control nome_responsavel" id="responsavel" name="responsavel" value="Maria da Silva">
                </div>

                <div class="col-12">
                  <label for="telefone" class="form-label">Telefone do Responsável</label>
                  <input type="tel" class="form-control telefone_responsavel" id="telefone" name="telefone" value="(18) 99999-8888">
                </div>
                <div class="col-12">
                  <label for="senha" class="form-label">Senha</label>
                  <input type="password" class="form-control senha_aluno" id="senha" name="senha" value="">
                </div>
                <div class="col-12">
                  <label for="senha" class="form-label">Senha Atual para salvar alterações</label>
                  <input type="password" class="form-control senha_aluno_atual" id="senha_atual" name="senha_atual" value="">
                </div>
                <div class="text-center">
                  <button type="submit" class="btn btn-primary">Salvar Alterações</button>
                  <button type="reset" class="btn btn-secondary">Cancelar</button>
                </div>
              </form>

            </div>
          </div><!-- End Editar Dados Card -->

          <!-- Estatísticas Rápidas -->
          <div class="card">
            <div class="card-body">
              <h5 class="card-title">Estatísticas</h5>

              <div class="d-flex align-items-center mb-3">
                <div class="card-icon rounded-circle d-flex align-items-center justify-content-center me-3" style="background-color: #e3f2fd; color: #1976d2;">
                  <i class="bi bi-book"></i>
                </div>
                <div>
                  <h6 class="mb-0">Disciplinas</h6>
                  <small class="text-muted">12 matérias</small>
                </div>
              </div>

              <div class="d-flex align-items-center mb-3">
                <div class="card-icon rounded-circle d-flex align-items-center justify-content-center me-3" style="background-color: #e8f5e8; color: #388e3c;">
                  <i class="bi bi-check-circle"></i>
                </div>
                <div>
                  <h6 class="mb-0">Atividades</h6>
                  <small class="text-muted">8 concluídas</small>
                </div>
              </div>

              <div class="d-flex align-items-center">
                <div class="card-icon rounded-circle d-flex align-items-center justify-content-center me-3" style="background-color: #fff3e0; color: #f57c00;">
                  <i class="bi bi-graph-up"></i>
                </div>
                <div>
                  <h6 class="mb-0">Atividades para realizar</h6>
                  <small class="text-muted">8.5</small>
                </div>
              </div>

            </div>
          </div><!-- End Estatísticas Card -->

        </div><!-- End Right side columns -->

      </div>
    </section>

  </main><!-- End #main -->

  <!-- ======= Footer ======= -->
  <footer id="footer" class="footer">
    <div class="copyright">
      &copy; Copyright <strong><span>NiceAdmin</span></strong>. All Rights Reserved
      &copy; Copyright <strong><span>Devminds</span></strong>. All Rights Reserved
      &copy; Copyright <strong><span>Aurora Boreal</span></strong>. All Rights Reserved
    </div>
  </footer><!-- End Footer -->

  <a href="#" class="back-to-top d-flex align-items-center justify-content-center"><i
      class="bi bi-arrow-up-short"></i></a>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/apexcharts/apexcharts.min.js"></script>
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/chart.js/chart.umd.js"></script>
  <script src="assets/vendor/echarts/echarts.min.js"></script>
  <script src="assets/vendor/quill/quill.js"></script>
  <script src="assets/vendor/simple-datatables/simple-datatables.js"></script>
  <script src="assets/vendor/tinymce/tinymce.min.js"></script>
  <script src="assets/vendor/php-email-form/validate.js"></script>

  <!-- Template Main JS File -->
  <script src="assets/js/main.js"></script>

  <!-- Script para o formulário de edição -->
  <script>
    document.getElementById('form-editar-perfil').addEventListener('submit', function(e) {
      e.preventDefault();

      // Aqui você pode adicionar a lógica para enviar os dados para o servidor
      // Por enquanto, vamos apenas mostrar uma mensagem de sucesso

      alert('Dados salvos com sucesso!');

      // Opcional: recarregar a página ou atualizar os dados exibidos
      // location.reload();
    });
  </script>

  <script src="/js/aluno_perfil.js"></script>

</body>

</html>
