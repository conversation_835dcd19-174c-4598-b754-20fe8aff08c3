<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Dados do Aluno</title>
  <style>
    * {
      box-sizing: border-box;
    }

    body {
      font-family: Arial, sans-serif;
      background-color: #121212;
      color: white;
      margin: 0;
      padding: 20px;
    }

    h1 {
      text-align: center;
      color: #00ff88;
      margin-bottom: 40px;
    }

    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      justify-content: center;
    }

    .box {
      background-color: #1e1e1e;
      border: 2px solid #00ff88;
      border-radius: 10px;
      padding: 20px;
      width: 45%;
      min-width: 300px;
    }

    .box h2 {
      color: #00ff88;
      margin-bottom: 10px;
    }

    .box p {
      margin: 5px 0;
    }

    .edit-box {
      width: 100%;
      background-color: #252525;
      margin-top: 20px;
      border-radius: 10px;
      padding: 20px;
    }

    .edit-box h2 {
      margin-bottom: 10px;
      color: #ffdd00;
    }

    .edit-box label {
      display: block;
      margin-top: 10px;
    }

    .edit-box input {
      width: 100%;
      padding: 8px;
      margin-top: 4px;
      border-radius: 5px;
      border: 1px solid #ccc;
    }

    .edit-box button {
      margin-top: 15px;
      padding: 10px 20px;
      background-color: #00ff88;
      color: black;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }

    @media (max-width: 768px) {
      .box {
        width: 100%;
      }

      h1 {
        font-size: 24px;
      }
    }
  </style>
</head>
<body>

  <h1>Informações do Aluno e Responsável</h1>

  <div class="container">
    <div class="box">
      <h2>Dados do Aluno</h2>
      <p><strong>Nome:</strong> <span class="nome_aluno"></span></p>
      <p><strong>CPF:</strong> <span class="cpf_aluno"></span></p>
      <p><strong>Data de Nascimento:</strong> <span class="data_nascimento"></span></p>
      <p><strong>Email:</strong> <span class="email_aluno"></span></p>
      <p><strong>Turma ID:</strong> <span class="turma_id"></span></p>
      <p><strong>Turma Nome:</strong> <span class="turma_nome"></span></p>
    </div>

    <div class="box">
      <h2>Dados do Responsável</h2>
      <p><strong>Nome:</strong> <span class="nome_responsavel"></span></p>
      <p><strong>CPF:</strong> <span class="cpf_responsavel"></span></p>
      <p><strong>Telefone:</strong> <span class="telefone_responsavel"></span></p>
    </div>
  </div>

  <div class="edit-box">
    <h2>Editar Dados</h2>
    <form onsubmit="handleSubmit(event)">
      <label for="nome">Nome do Aluno:</label>
      <input type="text" class="nome_aluno" name="nome" placeholder="João da Silva"/>

      <label for="email">Email:</label>
      <input type="email" class="email_aluno" name="email" placeholder="<EMAIL>"/>

      <label for="responsavel">Nome do Responsável:</label>
      <input type="text" class="nome_responsavel" name="responsavel" placeholder="Maria da Silva"/>

      <button type="submit">Salvar Alterações</button>
    </form>
  </div>

  <script>
    function handleSubmit(event) {
      event.preventDefault();
      alert("Dados salvos com sucesso!");
    }
  </script>
</body>

<script src="/js/aluno_perfil.js"></script>

</html>
