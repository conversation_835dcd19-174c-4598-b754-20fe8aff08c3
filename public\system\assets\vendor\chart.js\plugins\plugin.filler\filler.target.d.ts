/**
 * @typedef { import('../../core/core.controller.js').default } Chart
 * @typedef { import('../../core/core.scale.js').default } Scale
 * @typedef { import('../../elements/element.point.js').default } PointElement
 */
export function _getTarget(source: any): any;
export type Chart = import('../../core/core.controller.js').default;
export type Scale = import('../../core/core.scale.js').default;
export type PointElement = import('../../elements/element.point.js').default;
