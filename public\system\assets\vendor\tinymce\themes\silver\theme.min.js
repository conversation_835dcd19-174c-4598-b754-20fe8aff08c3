/**
 * TinyMCE version 7.2.1 (2024-07-03)
 */
!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,s=e=>t=>e===t,r=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=s(null),d=n("boolean"),u=s(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=e=>()=>e(),y=(e,t)=>(...o)=>e(t.apply(null,o)),x=e=>()=>e,w=e=>e,S=(e,t)=>e===t;function k(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const C=e=>t=>!e(t),O=e=>()=>{throw new Error(e)},_=e=>e(),T=x(!1),E=x(!0);class A{constructor(e,t){this.tag=e,this.value=t}static some(e){return new A(!0,e)}static none(){return A.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?A.some(e(this.value)):A.none()}bind(e){return this.tag?e(this.value):A.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:A.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?A.some(e):A.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}A.singletonNone=new A(!1);const M=Array.prototype.slice,D=Array.prototype.indexOf,B=Array.prototype.push,I=(e,t)=>D.call(e,t),F=(e,t)=>I(e,t)>-1,R=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},N=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},z=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const s=M.call(e,n,n+t);o.push(s)}return o},L=(e,t)=>{const o=e.length,n=new Array(o);for(let s=0;s<o;s++){const o=e[s];n[s]=t(o,s)}return n},V=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},H=(e,t)=>{const o=[],n=[];for(let s=0,r=e.length;s<r;s++){const r=e[s];(t(r,s)?o:n).push(r)}return{pass:o,fail:n}},P=(e,t)=>{const o=[];for(let n=0,s=e.length;n<s;n++){const s=e[n];t(s,n)&&o.push(s)}return o},U=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),W=(e,t,o)=>(V(e,((e,n)=>{o=t(o,e,n)})),o),j=(e,t)=>((e,t,o)=>{for(let n=0,s=e.length;n<s;n++){const s=e[n];if(t(s,n))return A.some(s);if(o(s,n))break}return A.none()})(e,t,T),$=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return A.some(o);return A.none()},G=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);B.apply(t,e[o])}return t},q=(e,t)=>G(L(e,t)),Y=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},X=e=>{const t=M.call(e,0);return t.reverse(),t},K=(e,t)=>P(e,(e=>!F(t,e))),J=(e,t)=>{const o={};for(let n=0,s=e.length;n<s;n++){const s=e[n];o[String(s)]=t(s,n)}return o},Q=e=>[e],Z=(e,t)=>{const o=M.call(e,0);return o.sort(t),o},ee=(e,t)=>t>=0&&t<e.length?A.some(e[t]):A.none(),te=e=>ee(e,0),oe=e=>ee(e,e.length-1),ne=p(Array.from)?Array.from:e=>M.call(e),se=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return A.none()},re=Object.keys,ae=Object.hasOwnProperty,ie=(e,t)=>{const o=re(e);for(let n=0,s=o.length;n<s;n++){const s=o[n];t(e[s],s)}},le=(e,t)=>ce(e,((e,o)=>({k:o,v:t(e,o)}))),ce=(e,t)=>{const o={};return ie(e,((e,n)=>{const s=t(e,n);o[s.k]=s.v})),o},de=e=>(t,o)=>{e[o]=t},ue=(e,t,o,n)=>{ie(e,((e,s)=>{(t(e,s)?o:n)(e,s)}))},me=(e,t)=>{const o={};return ue(e,t,de(o),b),o},ge=(e,t)=>{const o=[];return ie(e,((e,n)=>{o.push(t(e,n))})),o},pe=(e,t)=>{const o=re(e);for(let n=0,s=o.length;n<s;n++){const s=o[n],r=e[s];if(t(r,s,e))return A.some(r)}return A.none()},he=e=>ge(e,w),fe=(e,t)=>be(e,t)?A.from(e[t]):A.none(),be=(e,t)=>ae.call(e,t),ve=(e,t)=>be(e,t)&&void 0!==e[t]&&null!==e[t],ye=(e,t,o=S)=>e.exists((e=>o(e,t))),xe=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},we=(e,t,o)=>e.isSome()&&t.isSome()?A.some(o(e.getOrDie(),t.getOrDie())):A.none(),Se=(e,t)=>null!=e?A.some(t(e)):A.none(),ke=(e,t)=>e?A.some(t):A.none(),Ce=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,Oe=(e,t)=>Te(e,t)?((e,t)=>e.substring(t))(e,t.length):e,_e=(e,t,o=0,n)=>{const s=e.indexOf(t,o);return-1!==s&&(!!u(n)||s+t.length<=n)},Te=(e,t)=>Ce(e,t,0),Ee=(e,t)=>Ce(e,t,e.length-t.length),Ae=(Mo=/^\s+|\s+$/g,e=>e.replace(Mo,"")),Me=e=>e.length>0,De=e=>!Me(e),Be=e=>void 0!==e.style&&p(e.style.getPropertyValue),Ie=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},Fe=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return Ie(o.childNodes[0])},Re=(e,t)=>{const o=(t||document).createElement(e);return Ie(o)},Ne=(e,t)=>{const o=(t||document).createTextNode(e);return Ie(o)},ze=Ie,Le="undefined"!=typeof window?window:Function("return this;")(),Ve=(e,t)=>((e,t)=>{let o=null!=t?t:Le;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t),He=Object.getPrototypeOf,Pe=e=>{const t=Ve("ownerDocument.defaultView",e);return a(e)&&((e=>((e,t)=>{const o=((e,t)=>Ve(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(He(e).constructor.name))},Ue=e=>e.dom.nodeName.toLowerCase(),We=e=>t=>(e=>e.dom.nodeType)(t)===e,je=e=>$e(e)&&Pe(e.dom),$e=We(1),Ge=We(3),qe=We(9),Ye=We(11),Xe=e=>t=>$e(t)&&Ue(t)===e,Ke=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Je=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Qe=(e,t)=>{const o=void 0===t?document:t.dom;return Je(o)?A.none():A.from(o.querySelector(e)).map(ze)},Ze=(e,t)=>e.dom===t.dom,et=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},tt=e=>ze(e.dom.ownerDocument),ot=e=>qe(e)?e:tt(e),nt=e=>ze(ot(e).dom.documentElement),st=e=>ze(ot(e).dom.defaultView),rt=e=>A.from(e.dom.parentNode).map(ze),at=e=>A.from(e.dom.parentElement).map(ze),it=e=>A.from(e.dom.offsetParent).map(ze),lt=e=>L(e.dom.childNodes,ze),ct=(e,t)=>{const o=e.dom.childNodes;return A.from(o[t]).map(ze)},dt=e=>ct(e,0),ut=(e,t)=>({element:e,offset:t}),mt=(e,t)=>{const o=lt(e);return o.length>0&&t<o.length?ut(o[t],0):ut(e,t)},gt=e=>Ye(e)&&g(e.dom.host),pt=p(Element.prototype.attachShadow)&&p(Node.prototype.getRootNode),ht=x(pt),ft=pt?e=>ze(e.dom.getRootNode()):ot,bt=e=>gt(e)?e:ze(ot(e).dom.body),vt=e=>{const t=ft(e);return gt(t)?A.some(t):A.none()},yt=e=>ze(e.dom.host),xt=e=>{const t=Ge(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return vt(ze(t)).fold((()=>o.body.contains(t)),(n=xt,s=yt,e=>n(s(e))));var n,s},wt=()=>St(ze(document)),St=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return ze(t)},kt=(e,t,o)=>{if(!(r(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},Ct=(e,t,o)=>{kt(e.dom,t,o)},Ot=(e,t)=>{const o=e.dom;ie(t,((e,t)=>{kt(o,t,e)}))},_t=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},Tt=(e,t)=>A.from(_t(e,t)),Et=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},At=(e,t)=>{e.dom.removeAttribute(t)},Mt=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Be(e)&&e.style.setProperty(t,o)},Dt=(e,t)=>{Be(e)&&e.style.removeProperty(t)},Bt=(e,t,o)=>{const n=e.dom;Mt(n,t,o)},It=(e,t)=>{const o=e.dom;ie(t,((e,t)=>{Mt(o,t,e)}))},Ft=(e,t)=>{const o=e.dom;ie(t,((e,t)=>{e.fold((()=>{Dt(o,t)}),(e=>{Mt(o,t,e)}))}))},Rt=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||xt(e)?n:Nt(o,t)},Nt=(e,t)=>Be(e)?e.style.getPropertyValue(t):"",zt=(e,t)=>{const o=e.dom,n=Nt(o,t);return A.from(n).filter((e=>e.length>0))},Lt=e=>{const t={},o=e.dom;if(Be(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},Vt=(e,t,o)=>{const n=Re(e);return Bt(n,t,o),zt(n,t).isSome()},Ht=(e,t)=>{const o=e.dom;Dt(o,t),ye(Tt(e,"style").map(Ae),"")&&At(e,"style")},Pt=e=>e.dom.offsetWidth,Ut=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=Rt(o,e);return parseFloat(t)||0}return n},n=(e,t)=>W(t,((t,o)=>{const n=Rt(e,o),s=void 0===n?0:parseInt(n,10);return isNaN(s)?t:t+s}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Be(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const s=n(e,o);return t>s?t-s:0}}},Wt=Ut("height",(e=>{const t=e.dom;return xt(e)?t.getBoundingClientRect().height:t.offsetHeight})),jt=e=>Wt.get(e),$t=e=>Wt.getOuter(e),Gt=(e,t)=>({left:e,top:t,translate:(o,n)=>Gt(e+o,t+n)}),qt=Gt,Yt=(e,t)=>void 0!==e?e:void 0!==t?t:0,Xt=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,s=t.documentElement;if(o===e.dom)return qt(o.offsetLeft,o.offsetTop);const r=Yt(null==n?void 0:n.pageYOffset,s.scrollTop),a=Yt(null==n?void 0:n.pageXOffset,s.scrollLeft),i=Yt(s.clientTop,o.clientTop),l=Yt(s.clientLeft,o.clientLeft);return Kt(e).translate(a-l,r-i)},Kt=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?qt(o.offsetLeft,o.offsetTop):xt(e)?(e=>{const t=e.getBoundingClientRect();return qt(t.left,t.top)})(t):qt(0,0)},Jt=Ut("width",(e=>e.dom.offsetWidth)),Qt=e=>Jt.get(e),Zt=e=>Jt.getOuter(e),eo=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},to=()=>oo(0,0),oo=(e,t)=>({major:e,minor:t}),no={nu:oo,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?to():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return oo(n(1),n(2))})(e,o)},unknown:to},so=(e,t)=>{const o=String(t).toLowerCase();return j(e,(e=>e.search(o)))},ro=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ao=e=>t=>_e(t,e),io=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>_e(e,"edge/")&&_e(e,"chrome")&&_e(e,"safari")&&_e(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,ro],search:e=>_e(e,"chrome")&&!_e(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>_e(e,"msie")||_e(e,"trident")},{name:"Opera",versionRegexes:[ro,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:ao("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:ao("firefox")},{name:"Safari",versionRegexes:[ro,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(_e(e,"safari")||_e(e,"mobile/"))&&_e(e,"applewebkit")}],lo=[{name:"Windows",search:ao("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>_e(e,"iphone")||_e(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:ao("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:ao("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:ao("linux"),versionRegexes:[]},{name:"Solaris",search:ao("sunos"),versionRegexes:[]},{name:"FreeBSD",search:ao("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:ao("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],co={browsers:x(io),oses:x(lo)},uo="Edge",mo="Chromium",go="Opera",po="Firefox",ho="Safari",fo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(uo),isChromium:n(mo),isIE:n("IE"),isOpera:n(go),isFirefox:n(po),isSafari:n(ho)}},bo=()=>fo({current:void 0,version:no.unknown()}),vo=fo,yo=(x(uo),x(mo),x("IE"),x(go),x(po),x(ho),"Windows"),xo="Android",wo="Linux",So="macOS",ko="Solaris",Co="FreeBSD",Oo="ChromeOS",_o=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(yo),isiOS:n("iOS"),isAndroid:n(xo),isMacOS:n(So),isLinux:n(wo),isSolaris:n(ko),isFreeBSD:n(Co),isChromeOS:n(Oo)}},To=()=>_o({current:void 0,version:no.unknown()}),Eo=_o,Ao=(x(yo),x("iOS"),x(xo),x(wo),x(So),x(ko),x(Co),x(Oo),e=>window.matchMedia(e).matches);var Mo;let Do=eo((()=>((e,t,o)=>{const n=co.browsers(),s=co.oses(),r=t.bind((e=>((e,t)=>se(t.brands,(t=>{const o=t.brand.toLowerCase();return j(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:no.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>so(e,t).map((e=>{const o=no.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(bo,vo),a=((e,t)=>so(e,t).map((e=>{const o=no.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(s,e).fold(To,Eo),i=((e,t,o,n)=>{const s=e.isiOS()&&!0===/ipad/i.test(o),r=e.isiOS()&&!s,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=s||!r&&a&&n("(min-device-width:768px)"),c=r||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:x(s),isiPhone:x(r),isTablet:x(l),isPhone:x(c),isTouch:x(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:x(d),isDesktop:x(u)}})(a,r,e,o);return{browser:r,os:a,deviceType:i}})(navigator.userAgent,A.from(navigator.userAgentData),Ao)));const Bo=()=>Do(),Io=e=>{const t=ze((e=>{if(ht()&&g(e.target)){const t=ze(e.target);if($e(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return te(t)}}return A.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),s=y(n,o);return((e,t,o,n,s,r,a)=>({target:e,x:t,y:o,stop:n,prevent:s,kill:r,raw:a}))(t,e.clientX,e.clientY,o,n,s,e)},Fo=(e,t,o,n,s)=>{const r=((e,t)=>o=>{e(o)&&t(Io(o))})(o,n);return e.dom.addEventListener(t,r,s),{unbind:k(Ro,e,t,r,s)}},Ro=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},No=(e,t)=>{rt(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},zo=(e,t)=>{const o=(e=>A.from(e.dom.nextSibling).map(ze))(e);o.fold((()=>{rt(e).each((e=>{Vo(e,t)}))}),(e=>{No(e,t)}))},Lo=(e,t)=>{dt(e).fold((()=>{Vo(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},Vo=(e,t)=>{e.dom.appendChild(t.dom)},Ho=(e,t)=>{V(t,(t=>{Vo(e,t)}))},Po=e=>{e.dom.textContent="",V(lt(e),(e=>{Uo(e)}))},Uo=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Wo=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return qt(o,n)},jo=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},$o=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Go=e=>{const t=void 0===e?window:e,o=t.document,n=Wo(ze(o));return(e=>{const t=void 0===e?window:e;return Bo().browser.isFirefox()?A.none():A.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,s=e.clientHeight;return $o(n.left,n.top,o,s)}),(e=>$o(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},qo=()=>ze(document),Yo=(e,t)=>e.view(t).fold(x([]),(t=>{const o=e.owner(t),n=Yo(e,o);return[t].concat(n)}));var Xo=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?A.none():A.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(ze)},owner:e=>tt(e)});const Ko=e=>{const t=qo(),o=Wo(t),n=((e,t)=>{const o=t.owner(e),n=Yo(t,o);return A.some(n)})(e,Xo);return n.fold(k(Xt,e),(t=>{const n=Kt(e),s=U(t,((e,t)=>{const o=Kt(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return qt(s.left+n.left+o.left,s.top+n.top+o.top)}))},Jo=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Qo=e=>{const t=Xt(e),o=Zt(e),n=$t(e);return Jo(t.left,t.top,o,n)},Zo=e=>{const t=Ko(e),o=Zt(e),n=$t(e);return Jo(t.left,t.top,o,n)},en=(e,t)=>{const o=Math.max(e.x,t.x),n=Math.max(e.y,t.y),s=Math.min(e.right,t.right),r=Math.min(e.bottom,t.bottom);return Jo(o,n,s-o,r-n)},tn=()=>Go(window),on=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},nn=e=>{const t=on(A.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(A.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(A.some(e))}}},sn=()=>nn((e=>e.unbind())),rn=()=>{const e=nn(b);return{...e,on:t=>e.get().each(t)}};var an=tinymce.util.Tools.resolve("tinymce.ThemeManager");const ln=e=>{const t=t=>t(e),o=x(e),n=()=>s,s={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:E,isError:T,map:t=>dn.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>A.some(e)};return s},cn=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:T,isError:E,map:t,mapError:t=>dn.error(t(e)),bind:t,exists:T,forall:E,getOr:w,or:w,getOrThunk:_,orThunk:_,getOrDie:O(String(e)),each:b,toOptional:A.none};return o},dn={value:ln,error:cn,fromOption:(e,t)=>e.fold((()=>cn(t)),ln)};var un;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(un||(un={}));const mn=(e,t,o)=>e.stype===un.Error?t(e.serror):o(e.svalue),gn=e=>({stype:un.Value,svalue:e}),pn=e=>({stype:un.Error,serror:e}),hn=gn,fn=pn,bn=mn,vn=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),yn=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},xn=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const s=t[n];for(const t in s)be(s,t)&&(o[t]=e(o[t],s[t]))}return o},wn=xn(((e,t)=>i(e)&&i(t)?wn(e,t):t)),Sn=xn(((e,t)=>t)),kn=e=>({tag:"defaultedThunk",process:e}),Cn=e=>kn(x(e)),On=e=>({tag:"mergeWithThunk",process:e}),_n=e=>{const t=(e=>{const t=[],o=[];return V(e,(e=>{mn(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,y(fn,G)(o)):hn(t.values);var o},Tn=e=>a(e)&&re(e).length>100?" removed due to size":JSON.stringify(e,null,2),En=(e,t)=>fn([{path:e,getErrorInfo:t}]),An=e=>({extract:(t,o)=>((e,t)=>e.stype===un.Error?t(e.serror):e)(e(o),(e=>((e,t)=>En(e,x(t)))(t,e))),toString:x("val")}),Mn=An(hn),Dn=(e,t,o,n)=>n(fe(e,t).getOrThunk((()=>o(e)))),Bn=(e,t,o,n,s)=>{const r=e=>s.extract(t.concat([n]),e),a=e=>e.fold((()=>hn(A.none())),(e=>((e,t)=>e.stype===un.Value?{stype:un.Value,svalue:t(e.svalue)}:e)(s.extract(t.concat([n]),e),A.some)));switch(e.tag){case"required":return((e,t,o,n)=>fe(t,o).fold((()=>((e,t,o)=>En(e,(()=>'Could not find valid *required* value for "'+t+'" in '+Tn(o))))(e,o,t)),n))(t,o,n,r);case"defaultedThunk":return Dn(o,n,e.process,r);case"option":return((e,t,o)=>o(fe(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(fe(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return Dn(o,n,x({}),(t=>{const n=wn(e.process(o),t);return r(n)}))}},In=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),Fn=e=>re(me(e,g)),Rn=e=>{const t=Nn(e),o=U(e,((e,t)=>yn(t,(t=>wn(e,{[t]:!0})),x(e))),{});return{extract:(e,n)=>{const s=d(n)?[]:Fn(n),r=P(s,(e=>!ve(o,e)));return 0===r.length?t.extract(e,n):((e,t)=>En(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,r)},toString:t.toString}},Nn=e=>({extract:(t,o)=>((e,t,o)=>{const n={},s=[];for(const r of o)yn(r,((o,r,a,i)=>{const l=Bn(a,e,t,o,i);bn(l,(e=>{s.push(...e)}),(e=>{n[r]=e}))}),((e,o)=>{n[e]=o(t)}));return s.length>0?fn(s):hn(n)})(t,o,e),toString:()=>{const t=L(e,(e=>yn(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),zn=e=>({extract:(t,o)=>{const n=L(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return _n(n)},toString:()=>"array("+e.toString()+")"}),Ln=(e,t)=>{const o=void 0!==t?t:w;return{extract:(t,n)=>{const s=[];for(const r of e){const e=r.extract(t,n);if(e.stype===un.Value)return{stype:un.Value,svalue:o(e.svalue)};s.push(e)}return _n(s)},toString:()=>"oneOf("+L(e,(e=>e.toString())).join(", ")+")"}},Vn=(e,t)=>({extract:(o,n)=>{const s=re(n),r=((t,o)=>zn(An(e)).extract(t,o))(o,s);return((e,t)=>e.stype===un.Value?t(e.svalue):e)(r,(e=>{const s=L(e,(e=>vn(e,e,{tag:"required",process:{}},t)));return Nn(s).extract(o,n)}))},toString:()=>"setOf("+t.toString()+")"}),Hn=y(zn,Nn),Pn=x(Mn),Un=(e,t)=>An((o=>{const n=typeof o;return e(o)?hn(o):fn(`Expected type: ${t} but got: ${n}`)})),Wn=Un(h,"number"),jn=Un(r,"string"),$n=Un(d,"boolean"),Gn=Un(p,"function"),qn=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>qn(e[t])));default:return!1}},Yn=An((e=>qn(e)?hn(e):fn("Expected value to be acceptable for sending via postMessage"))),Xn=(e,t)=>({extract:(o,n)=>fe(n,e).fold((()=>((e,t)=>En(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>fe(o,n).fold((()=>((e,t,o)=>En(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+Tn(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+re(t)}),Kn=e=>An((t=>e(t).fold(fn,hn))),Jn=(e,t)=>Vn((t=>e(t).fold(pn,gn)),t),Qn=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===un.Error?{stype:un.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),mn(n,dn.error,dn.value);var n},Zn=e=>e.fold((e=>{throw new Error(ts(e))}),w),es=(e,t,o)=>Zn(Qn(e,t,o)),ts=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:x("... (only showing first ten failures)")}]):e;return L(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+Tn(e.input),os=(e,t)=>Xn(e,le(t,Nn)),ns=(e,t)=>((e,t)=>{const o=eo(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,t),ss=vn,rs=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),as=e=>Kn((t=>F(e,t)?dn.value(t):dn.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),is=e=>ss(e,e,{tag:"required",process:{}},Pn()),ls=(e,t)=>ss(e,e,{tag:"required",process:{}},t),cs=e=>ls(e,Wn),ds=e=>ls(e,jn),us=(e,t)=>ss(e,e,{tag:"required",process:{}},as(t)),ms=e=>ls(e,Gn),gs=(e,t)=>ss(e,e,{tag:"required",process:{}},Nn(t)),ps=(e,t)=>ss(e,e,{tag:"required",process:{}},Hn(t)),hs=(e,t)=>ss(e,e,{tag:"required",process:{}},zn(t)),fs=e=>ss(e,e,{tag:"option",process:{}},Pn()),bs=(e,t)=>ss(e,e,{tag:"option",process:{}},t),vs=e=>bs(e,Wn),ys=e=>bs(e,jn),xs=(e,t)=>bs(e,as(t)),ws=e=>bs(e,Gn),Ss=(e,t)=>bs(e,zn(t)),ks=(e,t)=>bs(e,Nn(t)),Cs=(e,t)=>ss(e,e,Cn(t),Pn()),Os=(e,t,o)=>ss(e,e,Cn(t),o),_s=(e,t)=>Os(e,t,Wn),Ts=(e,t)=>Os(e,t,jn),Es=(e,t,o)=>Os(e,t,as(o)),As=(e,t)=>Os(e,t,$n),Ms=(e,t)=>Os(e,t,Gn),Ds=(e,t,o)=>Os(e,t,zn(o)),Bs=(e,t,o)=>Os(e,t,Nn(o)),Is=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return V(e,((n,s)=>{const r=re(n);if(1!==r.length)throw new Error("one and only one name per case");const a=r[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,o)},match:e=>{const n=re(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!Y(t,(e=>F(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o};Is([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const Fs=(e,t)=>((e,t)=>({[e]:t}))(e,t),Rs=e=>(e=>{const t={};return V(e,(e=>{t[e.key]=e.value})),t})(e),Ns=e=>p(e)?e:T,zs=(e,t,o)=>{let n=e.dom;const s=Ns(o);for(;n.parentNode;){n=n.parentNode;const e=ze(n),o=t(e);if(o.isSome())return o;if(s(e))break}return A.none()},Ls=(e,t,o)=>{const n=t(e),s=Ns(o);return n.orThunk((()=>s(e)?A.none():zs(e,t,s)))},Vs=(e,t)=>Ze(e.element,t.event.target),Hs={can:E,abort:T,run:b},Ps=e=>{if(!ve(e,"can")&&!ve(e,"abort")&&!ve(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...Hs,...e}},Us=x,Ws=Us("touchstart"),js=Us("touchmove"),$s=Us("touchend"),Gs=Us("touchcancel"),qs=Us("mousedown"),Ys=Us("mousemove"),Xs=Us("mouseout"),Ks=Us("mouseup"),Js=Us("mouseover"),Qs=Us("focusin"),Zs=Us("focusout"),er=Us("keydown"),tr=Us("keyup"),or=Us("input"),nr=Us("change"),sr=Us("click"),rr=Us("transitioncancel"),ar=Us("transitionend"),ir=Us("transitionstart"),lr=Us("selectstart"),cr=e=>x("alloy."+e),dr={tap:cr("tap")},ur=cr("focus"),mr=cr("blur.post"),gr=cr("paste.post"),pr=cr("receive"),hr=cr("execute"),fr=cr("focus.item"),br=dr.tap,vr=cr("longpress"),yr=cr("sandbox.close"),xr=cr("typeahead.cancel"),wr=cr("system.init"),Sr=cr("system.touchmove"),kr=cr("system.touchend"),Cr=cr("system.scroll"),Or=cr("system.resize"),_r=cr("system.attached"),Tr=cr("system.detached"),Er=cr("system.dismissRequested"),Ar=cr("system.repositionRequested"),Mr=cr("focusmanager.shifted"),Dr=cr("slotcontainer.visibility"),Br=cr("system.external.element.scroll"),Ir=cr("change.tab"),Fr=cr("dismiss.tab"),Rr=cr("highlight"),Nr=cr("dehighlight"),zr=(e,t)=>{Pr(e,e.element,t,{})},Lr=(e,t,o)=>{Pr(e,e.element,t,o)},Vr=e=>{zr(e,hr())},Hr=(e,t,o)=>{Pr(e,t,o,{})},Pr=(e,t,o,n)=>{const s={target:t,...n};e.getSystem().triggerEvent(o,t,s)},Ur=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},Wr=e=>Rs(e),jr=(e,t)=>({key:e,value:Ps({abort:t})}),$r=e=>({key:e,value:Ps({run:(e,t)=>{t.event.prevent()}})}),Gr=(e,t)=>({key:e,value:Ps({run:t})}),qr=(e,t,o)=>({key:e,value:Ps({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),Yr=e=>t=>({key:e,value:Ps({run:(e,o)=>{Vs(e,o)&&t(e,o)}})}),Xr=(e,t,o)=>((e,t)=>Gr(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{Ur(t,t.element,e,n)}))})))(e,t.partUids[o]),Kr=(e,t)=>Gr(e,((e,o)=>{const n=o.event,s=e.getSystem().getByDom(n.target).getOrThunk((()=>Ls(n.target,(t=>e.getSystem().getByDom(t).toOptional()),T).getOr(e)));t(e,s,o)})),Jr=e=>Gr(e,((e,t)=>{t.cut()})),Qr=e=>Gr(e,((e,t)=>{t.stop()})),Zr=(e,t)=>Yr(e)(t),ea=Yr(_r()),ta=Yr(Tr()),oa=Yr(wr()),na=(ti=hr(),e=>Gr(ti,e)),sa=e=>L(e,(e=>Ee(e,"/*")?e.substring(0,e.length-2):e)),ra=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:sa(r)}),e},aa=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),ia=(e,t,o)=>oa(((n,s)=>{o(n,e,t)})),la=e=>({key:e,value:void 0}),ca=(e,t,o,n,s,r,a)=>{const i=e=>ve(e,o)?e[o]():A.none(),l=le(s,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),s=n.indexOf(")")+1,r=n.indexOf("("),a=n.substring(r+1,s-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:sa(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...s)=>{const r=[n].concat(s);return n.config({name:x(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(r,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...le(r,((e,t)=>ra(e,t))),...l,revoke:k(la,o),config:t=>{const n=es(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:eo((()=>es(o+"-config",e,t))),initialConfig:t,state:a}}},schema:x(t),exhibit:(e,t)=>we(i(e),fe(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>aa({}))),name:x(o),handlers:e=>i(e).map((e=>fe(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},da={init:()=>ua({readState:x("No State required")})},ua=e=>e,ma=e=>Rs(e),ga=Rn([is("fields"),is("name"),Cs("active",{}),Cs("apis",{}),Cs("state",da),Cs("extra",{})]),pa=e=>{const t=es("Creating behaviour: "+e.name,ga,e);return((e,t,o,n,s,r)=>{const a=Rn(e),i=ks(t,[("config",l=e,bs("config",Rn(l)))]);var l;return ca(a,i,t,o,n,s,r)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},ha=Rn([is("branchKey"),is("branches"),is("name"),Cs("active",{}),Cs("apis",{}),Cs("state",da),Cs("extra",{})]),fa=e=>{const t=es("Creating behaviour: "+e.name,ha,e);return((e,t,o,n,s,r)=>{const a=e,i=ks(t,[bs("config",e)]);return ca(a,i,t,o,n,s,r)})(os(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},ba=x(void 0),va=(e,t)=>{const o=_t(e,t);return void 0===o||""===o?[]:o.split(" ")},ya=e=>void 0!==e.dom.classList,xa=e=>va(e,"class"),wa=(e,t)=>((e,t,o)=>{const n=va(e,t).concat([o]);return Ct(e,t,n.join(" ")),!0})(e,"class",t),Sa=(e,t)=>((e,t,o)=>{const n=P(va(e,t),(e=>e!==o));return n.length>0?Ct(e,t,n.join(" ")):At(e,t),!1})(e,"class",t),ka=(e,t)=>{ya(e)?e.dom.classList.add(t):wa(e,t)},Ca=e=>{0===(ya(e)?e.dom.classList:xa(e)).length&&At(e,"class")},Oa=(e,t)=>{ya(e)?e.dom.classList.remove(t):Sa(e,t),Ca(e)},_a=(e,t)=>ya(e)&&e.dom.classList.contains(t),Ta=(e,t)=>{V(t,(t=>{ka(e,t)}))},Ea=(e,t)=>{V(t,(t=>{Oa(e,t)}))},Aa=e=>ya(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):xa(e),Ma=(e,t,o,n,s)=>{const r=e=>e+"px";return{position:e,left:t.map(r),top:o.map(r),right:n.map(r),bottom:s.map(r)}},Da=(e,t)=>{Ft(e,(e=>({...e,position:A.some(e.position)}))(t))},Ba=e=>(ye(zt(e,"position"),"fixed")?A.none():it(e)).orThunk((()=>{const t=Re("span");return rt(e).bind((e=>{Vo(e,t);const o=it(t);return Uo(t),o}))})),Ia=e=>Ba(e).map(Xt).getOrThunk((()=>qt(0,0))),Fa=(e,t)=>{const o=e.element;ka(o,t.transitionClass),Oa(o,t.fadeOutClass),ka(o,t.fadeInClass),t.onShow(e)},Ra=(e,t)=>{const o=e.element;ka(o,t.transitionClass),Oa(o,t.fadeInClass),ka(o,t.fadeOutClass),t.onHide(e)},Na=(e,t)=>e.y>=t.y,za=(e,t)=>e.bottom<=t.bottom,La=(e,t,o)=>({location:"top",leftX:t,topY:o.bounds.y-e.y}),Va=(e,t,o)=>({location:"bottom",leftX:t,bottomY:e.bottom-o.bounds.bottom}),Ha=e=>e.box.x-e.win.x,Pa=(e,t,o)=>o.getInitialPos().map((o=>{const n=((e,t)=>{const o=t.optScrollEnv.fold(x(e.bounds.y),(t=>t.scrollElmTop+(e.bounds.y-t.currentScrollTop)));return qt(e.bounds.x,o)})(o,t);return{box:Jo(n.left,n.top,Qt(e),jt(e)),location:o.location}})),Ua=(e,t,o,n,s)=>{const r=((e,t)=>{const o=t.optScrollEnv.fold(x(e.y),(t=>e.y+t.currentScrollTop-t.scrollElmTop));return qt(e.x,o)})(t,o),a=Jo(r.left,r.top,t.width,t.height);n.setInitialPos({style:Lt(e),position:Rt(e,"position")||"static",bounds:a,location:s.location})},Wa=(e,t,o)=>o.getInitialPos().bind((n=>{var s;switch(o.clearInitialPos(),n.position){case"static":return A.some({morph:"static"});case"absolute":const o=Ba(e).getOr(wt()),r=Qo(o),a=null!==(s=o.dom.scrollTop)&&void 0!==s?s:0;return A.some({morph:"absolute",positionCss:Ma("absolute",fe(n.style,"left").map((e=>t.x-r.x)),fe(n.style,"top").map((e=>t.y-r.y+a)),fe(n.style,"right").map((e=>r.right-t.right)),fe(n.style,"bottom").map((e=>r.bottom-t.bottom)))});default:return A.none()}})),ja=e=>{switch(e.location){case"top":return A.some({morph:"fixed",positionCss:Ma("fixed",A.some(e.leftX),A.some(e.topY),A.none(),A.none())});case"bottom":return A.some({morph:"fixed",positionCss:Ma("fixed",A.some(e.leftX),A.none(),A.none(),A.some(e.bottomY))});default:return A.none()}},$a=(e,t,o)=>{const n=e.element;return ye(zt(n,"position"),"fixed")?((e,t,o)=>((e,t,o)=>Pa(e,t,o).filter((({box:e})=>((e,t,o)=>Y(e,(e=>{switch(e){case"bottom":return za(t,o.bounds);case"top":return Na(t,o.bounds)}})))(o.getModes(),e,t))).bind((({box:t})=>Wa(e,t,o))))(e,t,o).orThunk((()=>t.optScrollEnv.bind((n=>Pa(e,t,o))).bind((({box:e,location:o})=>{const n=tn(),s=Ha({win:n,box:e}),r="top"===o?La(n,s,t):Va(n,s,t);return ja(r)})))))(n,t,o):((e,t,o)=>{const n=Qo(e),s=tn(),r=((e,t,o)=>{const n=t.win,s=t.box,r=Ha(t);return se(e,(e=>{switch(e){case"bottom":return za(s,o.bounds)?A.none():A.some(Va(n,r,o));case"top":return Na(s,o.bounds)?A.none():A.some(La(n,r,o));default:return A.none()}})).getOr({location:"no-dock"})})(o.getModes(),{win:s,box:n},t);return"top"===r.location||"bottom"===r.location?(Ua(e,n,t,o,r),ja(r)):A.none()})(n,t,o)},Ga=(e,t,o)=>{o.setDocked(!1),V(["left","right","top","bottom","position"],(t=>Ht(e.element,t))),t.onUndocked(e)},qa=(e,t,o,n)=>{const s="fixed"===n.position;o.setDocked(s),Da(e.element,n),(s?t.onDocked:t.onUndocked)(e)},Ya=(e,t,o,n,s=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((r=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(r,n.bounds);a!==o.isVisible()&&(o.setVisible(a),s&&!a?(Ta(e.element,[t.fadeOutClass]),t.onHide(e)):(a?Fa:Ra)(e,t))}))}))},Xa=(e,t,o,n,s)=>{Ya(e,t,o,n,!0),qa(e,t,o,s.positionCss)},Ka=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);Ya(e,t,o,n),$a(e,n,o).each((s=>{((e,t,o,n,s)=>{switch(s.morph){case"static":return Ga(e,t,o);case"absolute":return qa(e,t,o,s.positionCss);case"fixed":Xa(e,t,o,n,s)}})(e,t,o,n,s)}))})(e,t,o)},Ja=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1);const s=t.lazyViewport(e);((e,t,o)=>{const n=e.element;return Pa(n,t,o).bind((({box:e})=>Wa(n,e,o)))})(e,s,o).each((n=>{switch(n.morph){case"static":Ga(e,t,o);break;case"absolute":qa(e,t,o,n.positionCss)}})),o.setVisible(!0),t.contextual.each((t=>{Ea(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),Ka(e,t,o)})(e,t,o)},Qa=e=>(t,o,n)=>{const s=o.lazyViewport(t);((e,t,o,n)=>{const s=Qo(e),r=tn(),a=n(r,Ha({win:r,box:s}),t);return"bottom"===a.location||"top"===a.location?(((e,t,o,n,s)=>{n.getInitialPos().fold((()=>Ua(e,t,o,n,s)),(()=>b))})(e,s,t,o,a),ja(a)):A.none()})(t.element,s,n,e).each((e=>{Xa(t,o,n,s,e)}))},Za=Qa(La),ei=Qa(Va);var ti,oi=Object.freeze({__proto__:null,refresh:Ka,reset:Ja,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n),forceDockToTop:Za,forceDockToBottom:ei}),ni=Object.freeze({__proto__:null,events:(e,t)=>Wr([Zr(ar(),((o,n)=>{e.contextual.each((e=>{_a(o.element,e.transitionClass)&&(Ea(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),Gr(Cr(),((o,n)=>{Ka(o,e,t)})),Gr(Br(),((o,n)=>{Ka(o,e,t)})),Gr(Or(),((o,n)=>{Ja(o,e,t)}))])});const si=e=>e.dom.innerHTML,ri=(e,t)=>{const o=tt(e).dom,n=ze(o.createDocumentFragment()),s=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,lt(ze(o))})(t,o);Ho(n,s),Po(e),Vo(e,n)},ai=(e,t)=>ze(e.dom.cloneNode(t)),ii=e=>(e=>{if(gt(e))return"#shadow-root";{const t=(e=>ai(e,!1))(e);return(e=>{const t=Re("div"),o=ze(e.dom.cloneNode(!0));return Vo(t,o),si(t)})(t)}})(e);var li;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(li||(li={}));const ci=on({}),di=["alloy/data/Fields","alloy/debugging/Debugging"],ui=(e,t,o)=>((e,t,o)=>{switch(fe(ci.get(),e).orThunk((()=>{const t=re(ci.get());return se(t,(t=>e.indexOf(t)>-1?A.some(ci.get()[t]):A.none()))})).getOr(li.NORMAL)){case li.NORMAL:return o(mi());case li.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const s=(new Date).getTime();F(["mousemove","mouseover","mouseout",wr()],e)||console.log(e,{event:e,time:s-n,target:t.dom,sequence:L(o,(e=>F(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+ii(e.target)+")":e.outcome))})}}})(e,t),s=o(n);return n.write(),s}case li.STOP:return!0}})(e,t,o),mi=x({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),gi=x([is("menu"),is("selectedMenu")]),pi=x([is("item"),is("selectedItem")]);x(Nn(pi().concat(gi())));const hi=x(Nn(pi())),fi=gs("initSize",[is("numColumns"),is("numRows")]),bi=()=>gs("markers",[is("backgroundMenu")].concat(gi()).concat(pi())),vi=e=>gs("markers",L(e,is)),yi=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");j(t,(e=>e.indexOf("alloy")>0&&!R(di,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),ss(t,t,o,Kn((e=>dn.value(((...t)=>e.apply(void 0,t))))))),xi=e=>yi(0,e,Cn(b)),wi=e=>yi(0,e,Cn(A.none)),Si=e=>yi(0,e,{tag:"required",process:{}}),ki=e=>yi(0,e,{tag:"required",process:{}}),Ci=(e,t)=>rs(e,x(t)),Oi=e=>rs(e,w),_i=x(fi);var Ti=[ks("contextual",[ds("fadeInClass"),ds("fadeOutClass"),ds("transitionClass"),ms("lazyContext"),xi("onShow"),xi("onShown"),xi("onHide"),xi("onHidden")]),Ms("lazyViewport",(()=>({bounds:tn(),optScrollEnv:A.none()}))),Ds("modes",["top","bottom"],jn),xi("onDocked"),xi("onUndocked")];const Ei=pa({fields:Ti,name:"docking",active:ni,apis:oi,state:Object.freeze({__proto__:null,init:e=>{const t=on(!1),o=on(!0),n=rn(),s=on(e.modes);return ua({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:s.get,setModes:s.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${s.get().join(",")}`})}})}),Ai=Wr([((e,t)=>({key:e,value:Ps({can:(e,t)=>{const o=t.event,n=o.originator,s=o.target;return!((e,t,o)=>Ze(t,e.element)&&!Ze(t,o))(e,n,s)||(console.warn(ur()+" did not get interpreted by the desired target. \nOriginator: "+ii(n)+"\nTarget: "+ii(s)+"\nCheck the "+ur()+" event handlers"),!1)}})}))(ur())]);var Mi=Object.freeze({__proto__:null,events:Ai});let Di=0;const Bi=e=>{const t=(new Date).getTime(),o=Math.floor(1e9*Math.random());return Di++,e+"_"+o+Di+String(t)},Ii=x("alloy-id-"),Fi=x("data-alloy-id"),Ri=Ii(),Ni=Fi(),zi=(e,t)=>{Object.defineProperty(e.dom,Ni,{value:t,writable:!0})},Li=e=>{const t=$e(e)?e.dom[Ni]:null;return A.from(t)},Vi=e=>Bi(e),Hi=w,Pi=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+ii(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:x("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:T}},Ui=Pi(),Wi=Bi("alloy-premade"),ji=e=>(Object.defineProperty(e.element.dom,Wi,{value:e.uid,writable:!0}),Fs(Wi,e)),$i=e=>fe(e,Wi),Gi=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:sa(r.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),qi=(e,t)=>{const o={};return ie(e,((e,n)=>{ie(e,((e,s)=>{const r=fe(o,s).getOr([]);o[s]=r.concat([t(n,e)])}))})),o},Yi=e=>e.cHandler,Xi=(e,t)=>({name:e,handler:t}),Ki=(e,t)=>{const o={};return V(e,(e=>{o[e.name()]=e.handlers(t)})),o},Ji=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const s=Z(o,((o,s)=>{const r=o[t],a=s[t],i=n.indexOf(r),l=n.indexOf(a);if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===l)throw new Error("The ordering for "+e+" does not have an entry for "+a+".\nOrder specified: "+JSON.stringify(n,null,2));return i<l?-1:l<i?1:0}));return dn.value(s)}catch(e){return dn.error([e])}})("Event: "+o,"name",e,n).map((e=>(e=>{const t=((e,t)=>(...t)=>W(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=((e,t)=>(...t)=>W(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{V(e,(e=>{e.run.apply(void 0,t)}))}}})(L(e,(e=>e.handler))))):((e,t)=>dn.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(L(t,(e=>e.name)),null,2)]))(o,e)},Qi=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return V(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,dn.error(G(n))):((e,t)=>0===e.length?dn.value(t):dn.value(wn(t,Sn.apply(void 0,e))))(o.values,t);var n})(ge(e,((e,o)=>(1===e.length?dn.value(e[0].handler):Ji(e,t,o)).map((n=>{const s=(e=>{const t=(e=>p(e)?{can:E,abort:T,run:e}:e)(e);return(e,o,...n)=>{const s=[e,o].concat(n);t.abort.apply(void 0,s)?o.stop():t.can.apply(void 0,s)&&t.run.apply(void 0,s)}})(n),r=e.length>1?P(t[o],(t=>R(e,(e=>e.name===t)))).join(" > "):e[0].name;return Fs(o,((e,t)=>({handler:e,purpose:t}))(s,r))})))),{}),Zi="alloy.base.behaviour",el=Nn([ss("dom","dom",{tag:"required",process:{}},Nn([is("tag"),Cs("styles",{}),Cs("classes",[]),Cs("attributes",{}),fs("value"),fs("innerHtml")])),is("components"),is("uid"),Cs("events",{}),Cs("apis",{}),ss("eventOrder","eventOrder",(bl={[hr()]:["disabling",Zi,"toggling","typeaheadevents"],[ur()]:[Zi,"focusing","keying"],[wr()]:[Zi,"disabling","toggling","representing"],[or()]:[Zi,"representing","streaming","invalidating"],[Tr()]:[Zi,"representing","item-events","toolbar-button-events","tooltipping"],[qs()]:["focusing",Zi,"item-type-events"],[Ws()]:["focusing",Zi,"item-type-events"],[Js()]:["item-type-events","tooltipping"],[pr()]:["receiving","reflecting","tooltipping"]},On(x(bl))),Pn()),fs("domModification")]),tl=e=>e.events,ol=e=>e.dom.value,nl=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},sl=(e,t,o)=>{o.fold((()=>Vo(e,t)),(e=>{Ze(e,t)||(No(e,t),Uo(e))}))},rl=(e,t,o)=>{const n=L(t,o),s=lt(e);return V(s.slice(n.length),Uo),n},al=(e,t,o,n)=>{const s=ct(e,t),r=n(o,s),a=((e,t,o)=>ct(e,t).map((e=>{if(o.exists((t=>!Ze(t,e)))){const t=o.map(Ue).getOr("span"),n=Re(t);return No(e,n),n}return e})))(e,t,s);return sl(e,r.element,a),r},il=(e,t)=>{const o=re(e),n=re(t),s=K(n,o),r=((e,o)=>{const n={},s={};return ue(e,((e,o)=>!be(t,o)||e!==t[o]),de(n),de(s)),{t:n,f:s}})(e).t;return{toRemove:s,toSet:r}},ll=(e,t)=>{const o=t.filter((t=>Ue(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>be(e.dom,Wi))(t))).bind((t=>((e,t)=>{try{const o=((e,t)=>{const{class:o,style:n,...s}=(e=>W(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:r,toRemove:a}=il(e.attributes,s),i=Lt(t),{toSet:l,toRemove:c}=il(e.styles,i),d=Aa(t),u=K(d,e.classes),m=K(e.classes,d);return V(a,(e=>At(t,e))),Ot(t,r),Ta(t,m),Ea(t,u),V(c,(e=>Ht(t,e))),It(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{rl(e,t,((t,o)=>{const n=ct(e,o);return sl(e,t,n),t}))})(t,o)}),(e=>{ri(t,e)})),(()=>{const o=t,n=e.value.getOrUndefined();n!==ol(o)&&nl(o,null!=n?n:"")})(),t})(e,t);return A.some(o)}catch(e){return A.none()}})(e,t))).getOrThunk((()=>(e=>{const t=Re(e.tag);Ot(t,e.attributes),Ta(t,e.classes),It(t,e.styles),e.innerHtml.each((e=>ri(t,e)));const o=e.domChildren;return Ho(t,o),e.value.each((e=>{nl(t,e)})),t})(e)));return zi(o,e.uid),o},cl=e=>{const t=(e=>{const t=fe(e,"behaviours").getOr({});return q(re(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=L(t,(e=>ks(e.name(),[is("config"),Cs("state",da)]))),n=Qn("component.behaviours",Nn(o),e.behaviours).fold((t=>{throw new Error(ts(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),w);return{list:t,data:le(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return x(t)}))}})(e,t))(e,t)},dl=(e,t)=>{const o=()=>m,n=on(Ui),s=Zn((e=>Qn("custom.definition",el,e))(e)),r=cl(e),a=(e=>e.list)(r),i=(e=>e.data)(r),l=((e,t,o)=>{const n={...(s=e).dom,uid:s.uid,domChildren:L(s.components,(e=>e.element))};var s;const r=(e=>e.domModification.fold((()=>aa({})),aa))(e),a={"alloy.base.modification":r},i=t.length>0?((e,t,o,n)=>{const s={...t};V(o,(t=>{s[t.name()]=t.exhibit(e,n)}));const r=qi(s,((e,t)=>({name:e,modification:t}))),a=e=>U(e,((e,t)=>({...t.modification,...e})),{}),i=U(r.classes,((e,t)=>t.modification.concat(e)),[]),l=a(r.attributes),c=a(r.styles);return aa({classes:i,attributes:l,styles:c})})(o,a,t,n):r;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(s,a,i),c=ll(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":tl(e)};return((e,t,o,n)=>{const s=((e,t,o)=>{const n={...o,...Ki(t,e)};return qi(n,Xi)})(e,o,n);return Qi(s,t)})(o,e.eventOrder,t,n).getOrDie()})(s,a,i),u=on(s.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>s.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(Pi(o))},element:c,syncComponents:()=>{const e=lt(c),t=q(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},ul=e=>{const t=Ne(e);return ml({element:t})},ml=e=>{const t=es("external.component",Rn([is("element"),fs("uid")]),e),o=on(Pi()),n=t.uid.getOrThunk((()=>Vi("external")));zi(t.element,n);const s={uid:n,getSystem:o.get,config:A.none,hasConfigured:T,connect:e=>{o.set(e)},disconnect:()=>{o.set(Pi((()=>s)))},getApis:()=>({}),element:t.element,spec:e,readState:x("No state"),syncComponents:b,components:x([]),events:{}};return ji(s)},gl=Vi,pl=(e,t)=>$i(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=Hi(e),s=((e,t)=>{const o=fe(e,"components").getOr([]);return t.fold((()=>L(o,hl)),(e=>L(o,((t,o)=>pl(t,ct(e,o))))))})(n,t),r={...n,events:{...Mi,...o},components:s};return dn.value(dl(r,t))})((e=>be(e,"uid"))(e)?e:{uid:gl(""),...e},t).getOrDie())),hl=e=>pl(e,A.none()),fl=ji;var bl,vl=(e,t,o,n,s)=>e(o,n)?A.some(o):p(s)&&s(o)?A.none():t(o,n,s);const yl=(e,t,o)=>{let n=e.dom;const s=p(o)?o:T;for(;n.parentNode;){n=n.parentNode;const e=ze(n);if(t(e))return A.some(e);if(s(e))break}return A.none()},xl=(e,t,o)=>vl(((e,t)=>t(e)),yl,e,t,o),wl=(e,t)=>j(e.dom.childNodes,(e=>t(ze(e)))).map(ze),Sl=(e,t,o)=>xl(e,t,o).isSome(),kl=(e,t,o)=>yl(e,(e=>Ke(e,t)),o),Cl=(e,t)=>((e,o)=>{const n=e.dom;return n.parentNode?wl(ze(n.parentNode),(o=>!Ze(e,o)&&Ke(o,t))):A.none()})(e),Ol=(e,t)=>wl(e,(e=>Ke(e,t))),_l=(e,t)=>Qe(t,e),Tl=(e,t,o)=>vl(((e,t)=>Ke(e,t)),kl,e,t,o),El="aria-controls",Al=()=>{const e=Bi(El);return{id:e,link:t=>{Ct(t,El,e)},unlink:e=>{At(e,El)}}},Ml=(e,t)=>Sl(t,(t=>Ze(t,e.element)),T)||((e,t)=>(e=>xl(e,(e=>{if(!$e(e))return!1;const t=_t(e,"id");return void 0!==t&&t.indexOf(El)>-1})).bind((e=>{const t=_t(e,"id"),o=ft(e);return _l(o,`[${El}="${t}"]`)})))(t).exists((t=>Ml(e,t))))(e,t),Dl=(e,t,o,n,s,r,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:s,restriction:r,label:`${a}-${s}`,alwaysFit:i}),Bl=Is([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Il=Bl.southeast,Fl=Bl.southwest,Rl=Bl.northeast,Nl=Bl.northwest,zl=Bl.south,Ll=Bl.north,Vl=Bl.east,Hl=Bl.west,Pl=(e,t,o,n)=>{const s=e+t;return s>n?o:s<o?n:s},Ul=(e,t,o)=>Math.min(Math.max(e,t),o),Wl=(e,t)=>J(["left","right","top","bottom"],(o=>fe(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),jl="layout",$l=e=>e.x,Gl=(e,t)=>e.x+e.width/2-t.width/2,ql=(e,t)=>e.x+e.width-t.width,Yl=(e,t)=>e.y-t.height,Xl=e=>e.y+e.height,Kl=(e,t)=>e.y+e.height/2-t.height/2,Jl=(e,t,o)=>Dl($l(e),Xl(e),o.southeast(),Il(),"southeast",Wl(e,{left:1,top:3}),jl),Ql=(e,t,o)=>Dl(ql(e,t),Xl(e),o.southwest(),Fl(),"southwest",Wl(e,{right:0,top:3}),jl),Zl=(e,t,o)=>Dl($l(e),Yl(e,t),o.northeast(),Rl(),"northeast",Wl(e,{left:1,bottom:2}),jl),ec=(e,t,o)=>Dl(ql(e,t),Yl(e,t),o.northwest(),Nl(),"northwest",Wl(e,{right:0,bottom:2}),jl),tc=(e,t,o)=>Dl(Gl(e,t),Yl(e,t),o.north(),Ll(),"north",Wl(e,{bottom:2}),jl),oc=(e,t,o)=>Dl(Gl(e,t),Xl(e),o.south(),zl(),"south",Wl(e,{top:3}),jl),nc=(e,t,o)=>Dl((e=>e.x+e.width)(e),Kl(e,t),o.east(),Vl(),"east",Wl(e,{left:0}),jl),sc=(e,t,o)=>Dl(((e,t)=>e.x-t.width)(e,t),Kl(e,t),o.west(),Hl(),"west",Wl(e,{right:1}),jl),rc=()=>[Jl,Ql,Zl,ec,oc,tc,nc,sc],ac=()=>[Ql,Jl,ec,Zl,oc,tc,nc,sc],ic=()=>[Zl,ec,Jl,Ql,tc,oc],lc=()=>[ec,Zl,Ql,Jl,tc,oc],cc=()=>[Jl,Ql,Zl,ec,oc,tc],dc=()=>[Ql,Jl,ec,Zl,oc,tc];var uc=Object.freeze({__proto__:null,events:e=>Wr([Gr(pr(),((t,o)=>{const n=e.channels,s=re(n),r=o,a=((e,t)=>t.universal?e:P(e,(e=>F(t.channels,e))))(s,r);V(a,(e=>{const o=n[e],s=o.schema,a=es("channel["+e+"] data\nReceiver: "+ii(t.element),s,r.data);o.onReceive(t,a)}))}))])}),mc=[ls("channels",Jn(dn.value,Rn([Si("onReceive"),Cs("schema",Pn())])))];const gc=pa({fields:mc,name:"receiving",active:uc});var pc=Object.freeze({__proto__:null,exhibit:(e,t)=>aa({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const hc=(e,t=!1)=>e.dom.focus({preventScroll:t}),fc=e=>e.dom.blur(),bc=e=>{const t=ft(e).dom;return e.dom===t.activeElement},vc=(e=qo())=>A.from(e.dom.activeElement).map(ze),yc=e=>vc(ft(e)).filter((t=>e.dom.contains(t.dom))),xc=(e,t)=>{const o=ft(t),n=vc(o).bind((e=>{const o=t=>Ze(e,t);return o(t)?A.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const s=ze(e.childNodes[n]);if(t(s))return A.some(s);const r=o(e.childNodes[n]);if(r.isSome())return r}return A.none()};return o(e.dom)})(t,o)})),s=e(t);return n.each((e=>{vc(o).filter((t=>Ze(t,e))).fold((()=>{hc(e)}),b)})),s},wc=Is([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),Sc=(e,t,o,n,s,r)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=s-(i+a.width),d=r-(l+a.height),u=A.some(i),m=A.some(l),g=A.some(c),p=A.some(d),h=A.none();return t.direction.fold((()=>Ma(e,u,m,h,h)),(()=>Ma(e,h,m,g,h)),(()=>Ma(e,u,h,h,p)),(()=>Ma(e,h,h,g,p)),(()=>Ma(e,u,m,h,h)),(()=>Ma(e,u,h,h,p)),(()=>Ma(e,u,m,h,h)),(()=>Ma(e,h,m,g,h)))},kc=(e,t)=>e.fold((()=>{const e=t.rect;return Ma("absolute",A.some(e.x),A.some(e.y),A.none(),A.none())}),((e,o,n,s)=>Sc("absolute",t,e,o,n,s)),((e,o,n,s)=>Sc("fixed",t,e,o,n,s))),Cc=(e,t)=>{const o=k(Ko,t),n=e.fold(o,o,(()=>{const e=Wo();return Ko(t).translate(-e.left,-e.top)})),s=Zt(t),r=$t(t);return Jo(n.left,n.top,s,r)},Oc=(e,t)=>t.fold((()=>e.fold(tn,tn,Jo)),(t=>e.fold(x(t),x(t),(()=>{const o=_c(e,t.x,t.y);return Jo(o.left,o.top,t.width,t.height)})))),_c=(e,t,o)=>{const n=qt(t,o);return e.fold(x(n),x(n),(()=>{const e=Wo();return n.translate(-e.left,-e.top)}))};wc.none;const Tc=wc.relative,Ec=wc.fixed,Ac="data-alloy-placement",Mc=e=>Tt(e,Ac),Dc=Is([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),Bc=(e,t,o,n)=>{const s=e.bubble,r=s.offset,a=((e,t,o)=>{const n=(n,s)=>t[n].map((t=>{const r="top"===n||"bottom"===n,a=r?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,s)+a;return r?Ul(i,e.y,e.bottom):Ul(i,e.x,e.right)})).getOr(s),s=n("left",e.x),r=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return Jo(s,r,a-s,i-r)})(n,e.restriction,r),i=e.x+r.left,l=e.y+r.top,c=Jo(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=s&&i>=n&&i<=r,sizeInBounds:l<=s&&l>=o&&c<=r&&c>=n,visibleW:Math.min(d,a>=o?s-a:l-o),visibleH:Math.min(u,i>=n?r-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,s-l),u=Math.max(n,r-c),m=Ul(a,o,d),g=Ul(i,n,u),p=Math.min(m+l,s)-m,h=Math.min(g+c,r)-g;return Jo(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=x(t.bottom-o.y),s=x(o.bottom-t.y),r=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,s,s,n),a=x(t.right-o.x),i=x(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:r}})(e.direction,h,n),y={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:s.classesOn,off:s.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?Dc.fit(y):Dc.nofit(y,m,g,f)},Ic=E,Fc=(e,t,o)=>((e,t,o,n)=>Fo(e,t,o,n,!1))(e,t,Ic,o),Rc=(e,t,o)=>((e,t,o,n)=>Fo(e,t,o,n,!0))(e,t,Ic,o),Nc=Io,zc=["top","bottom","right","left"],Lc="data-alloy-transition-timer",Vc=(e,t,o,n,s,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,s,a);if(i||((e,t)=>((e,t)=>Y(t,(t=>_a(e,t))))(e,t.classes))(e,n)){Bt(e,"position",o.position);const a=Cc(t,e),l=kc(t,{...s,rect:a}),c=J(zc,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return pe(t,((t,n)=>!((e,t,o=S)=>we(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(Ft(e,c),i&&((e,t)=>{Ta(e,t.classes),Tt(e,Lc).each((t=>{clearTimeout(parseInt(t,10)),At(e,Lc)})),((e,t)=>{const o=sn(),n=sn();let s;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return Ze(t.target,e)&&De(n)&&F(zc,t.raw.propertyName)},i=r=>{if(m(r)||a(r)){o.clear(),n.clear();const a=null==r?void 0:r.raw.type;(m(a)||a===ar())&&(clearTimeout(s),At(e,Lc),Ea(e,t.classes))}},l=Fc(e,ir(),(t=>{a(t)&&(l.unbind(),o.set(Fc(e,ar(),i)),n.set(Fc(e,rr(),i)))})),c=(e=>{const t=t=>{const o=Rt(e,t).split(/\s*,\s*/);return P(o,Me)},o=e=>{if(r(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return Ee(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),s=t("transition-duration");return W(s,((e,t,s)=>{const r=o(n[s])+o(t);return Math.max(e,r)}),0)})(e);requestAnimationFrame((()=>{s=setTimeout(i,c+17),Ct(e,Lc,s)}))})(e,t)})(e,n),Pt(e))}else Ea(e,n.classes)},Hc=(e,t)=>{((e,t)=>{const o=Wt.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);Bt(e,"max-height",o+"px")})(e,Math.floor(t))},Pc=x(((e,t)=>{Hc(e,t),It(e,{"overflow-x":"hidden","overflow-y":"auto"})})),Uc=x(((e,t)=>{Hc(e,t)})),Wc=(e,t,o)=>void 0===e[t]?o:e[t],jc=(e,t,o,n)=>{const s=((e,t,o,n)=>{Ht(t,"max-height"),Ht(t,"max-width");const s={width:Zt(r=t),height:$t(r)};var r;return((e,t,o,n,s,r)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,s,e,r),g=Bc(m,a,i,r);return g.fold(x(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:Dc.nofit(l,c,d,u)))};return W(t,((e,t)=>{const o=k(l,t);return e.fold(x(e),o)}),Dc.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:Il(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(w,w)})(t,n.preference,e,s,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=kc(o.origin,t);o.transition.each((s=>{Vc(e,o.origin,n,s,t,o.lastPlacement)})),Da(e,n)})(t,s,n),((e,t)=>{((e,t)=>{Ct(e,Ac,t)})(e,t.placement)})(t,s),((e,t)=>{const o=t.classes;Ea(e,o.off),Ta(e,o.on)})(t,s),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,s,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,s,n),{layout:s.layout,placement:s.placement}},$c=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],Gc=(e,t,o,n=1)=>{const s=e*n,r=t*n,a=e=>fe(o,e).getOr([]),i=(e,t,o)=>{const n=K($c,o);return{offset:qt(e,t),classesOn:q(o,a),classesOff:q(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(s,r,["top","alignLeft","inset"]),insetNorthwest:()=>i(-s,r,["top","alignRight","inset"]),insetNorth:()=>i(-s/2,r,["top","alignCentre","inset"]),insetSoutheast:()=>i(s,-r,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-s,-r,["bottom","alignRight","inset"]),insetSouth:()=>i(-s/2,-r,["bottom","alignCentre","inset"]),insetEast:()=>i(-s,-r/2,["valignCentre","right","inset"]),insetWest:()=>i(s,-r/2,["valignCentre","left","inset"])}},qc=()=>Gc(0,0,{}),Yc=w,Xc=(e,t)=>o=>"rtl"===Kc(o)?t:e,Kc=e=>"rtl"===Rt(e,"direction")?"rtl":"ltr";var Jc;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(Jc||(Jc={}));const Qc="data-alloy-vertical-dir",Zc=e=>Sl(e,(e=>$e(e)&&_t(e,"data-alloy-vertical-dir")===Jc.BottomToTop)),ed=()=>ks("layouts",[is("onLtr"),is("onRtl"),fs("onBottomLtr"),fs("onBottomRtl")]),td=(e,t,o,n,s,r,a)=>{const i=a.map(Zc).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(s):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(r):c.getOr(n);return Xc(d,u)(e)};var od=[is("hotspot"),fs("bubble"),Cs("overrides",{}),ed(),Ci("placement",((e,t,o)=>{const n=t.hotspot,s=Cc(o,n.element),r=td(e.element,t,cc(),dc(),ic(),lc(),A.some(t.hotspot.element));return A.some(Yc({anchorBox:s,bubble:t.bubble.getOr(qc()),overrides:t.overrides,layouts:r}))}))],nd=[is("x"),is("y"),Cs("height",0),Cs("width",0),Cs("bubble",qc()),Cs("overrides",{}),ed(),Ci("placement",((e,t,o)=>{const n=_c(o,t.x,t.y),s=Jo(n.left,n.top,t.width,t.height),r=td(e.element,t,rc(),ac(),rc(),ac(),A.none());return A.some(Yc({anchorBox:s,bubble:t.bubble,overrides:t.overrides,layouts:r}))}))];const sd=Is([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),rd=e=>e.fold(w,((e,t,o)=>e.translate(-t,-o))),ad=e=>e.fold(w,w),id=e=>W(e,((e,t)=>e.translate(t.left,t.top)),qt(0,0)),ld=e=>{const t=L(e,ad);return id(t)},cd=sd.screen,dd=sd.absolute,ud=(e,t,o)=>{const n=tt(e.element),s=Wo(n),r=((e,t,o)=>{const n=st(o.root).dom;return A.from(n.frameElement).map(ze).filter((t=>{const o=tt(t),n=tt(e.element);return Ze(o,n)})).map(Xt)})(e,0,o).getOr(s);return dd(r,s.left,s.top)},md=(e,t,o,n)=>{const s=cd(qt(e,t));return A.some(((e,t,o)=>({point:e,width:t,height:o}))(s,o,n))},gd=(e,t,o,n,s)=>e.map((e=>{const r=[t,e.point],a=(i=()=>ld(r),l=()=>ld(r),c=()=>(e=>{const t=L(e,rd);return id(t)})(r),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?ic():cc(),m=o.showAbove?lc():dc(),g=td(s,o,u,m,u,m,A.none());var p,h,f,b;return Yc({anchorBox:d,bubble:o.bubble.getOr(qc()),overrides:o.overrides,layouts:g})}));var pd=[is("node"),is("root"),fs("bubble"),ed(),Cs("overrides",{}),Cs("showAbove",!1),Ci("placement",((e,t,o)=>{const n=ud(e,0,t);return t.node.filter(xt).bind((s=>{const r=s.dom.getBoundingClientRect(),a=md(r.left,r.top,r.width,r.height),i=t.node.getOr(e.element);return gd(a,n,t,o,i)}))}))];const hd=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),fd=Is([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),bd=(fd.before,fd.on,fd.after,e=>e.fold(w,w,w)),vd=Is([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),yd={domRange:vd.domRange,relative:vd.relative,exact:vd.exact,exactFromRange:e=>vd.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>ze(e.startContainer),relative:(e,t)=>bd(e),exact:(e,t,o,n)=>e}))(e);return st(t)},range:hd},xd=(e,t,o)=>{const n=e.document.createRange();var s;return s=n,t.fold((e=>{s.setStartBefore(e.dom)}),((e,t)=>{s.setStart(e.dom,t)}),(e=>{s.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},wd=(e,t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},Sd=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),kd=Is([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Cd=(e,t,o)=>t(ze(o.startContainer),o.startOffset,ze(o.endContainer),o.endOffset),Od=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:x(e),rtl:A.none}),relative:(t,o)=>({ltr:eo((()=>xd(e,t,o))),rtl:eo((()=>A.some(xd(e,o,t))))}),exact:(t,o,n,s)=>({ltr:eo((()=>wd(e,t,o,n,s))),rtl:eo((()=>A.some(wd(e,n,s,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>kd.rtl(ze(e.endContainer),e.endOffset,ze(e.startContainer),e.startOffset))).getOrThunk((()=>Cd(0,kd.ltr,o))):Cd(0,kd.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},rtl:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(n.dom,s),r.setEnd(t.dom,o),r}});kd.ltr,kd.rtl;const _d=(e,t,o)=>P(((e,t)=>{const o=p(t)?t:T;let n=e.dom;const s=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=ze(e);if(s.push(t),!0===o(t))break;n=e}return s})(e,o),t),Td=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Je(o)?[]:L(o.querySelectorAll(e),ze)})(t,e),Ed=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return A.some(hd(ze(t.startContainer),t.startOffset,ze(o.endContainer),o.endOffset))}return A.none()},Ad=e=>{if(null===e.anchorNode||null===e.focusNode)return Ed(e);{const t=ze(e.anchorNode),o=ze(e.focusNode);return((e,t,o,n)=>{const s=((e,t,o,n)=>{const s=tt(e).dom.createRange();return s.setStart(e.dom,t),s.setEnd(o.dom,n),s})(e,t,o,n),r=Ze(e,o)&&t===n;return s.collapsed&&!r})(t,e.anchorOffset,o,e.focusOffset)?A.some(hd(t,e.anchorOffset,o,e.focusOffset)):Ed(e)}},Md=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?A.some(o).map(Sd):A.none()})(Od(e,t)),Dd=((e,t)=>{const o=t=>e(t)?A.from(t.dom.nodeValue):A.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(Ge),Bd=(e,t)=>({element:e,offset:t}),Id=(e,t)=>Ge(e)?Bd(e,t):((e,t)=>{const o=lt(e);if(0===o.length)return Bd(e,t);if(t<o.length)return Bd(o[t],0);{const e=o[o.length-1],t=Ge(e)?(e=>Dd.get(e))(e).length:lt(e).length;return Bd(e,t)}})(e,t),Fd=e=>void 0!==e.foffset,Rd=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>A.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(Ad))(e)))().map((e=>{if(Fd(e)){const t=Id(e.start,e.soffset),o=Id(e.finish,e.foffset);return yd.range(t.element,t.offset,o.element,o.offset)}return e}));var Nd=[fs("getSelection"),is("root"),fs("bubble"),ed(),Cs("overrides",{}),Cs("showAbove",!1),Ci("placement",((e,t,o)=>{const n=st(t.root).dom,s=ud(e,0,t),r=Rd(n,t).bind((e=>{if(Fd(e)){const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?A.some(t).map(Sd):A.none()})(Od(e,t)))(n,yd.exactFromRange(e)).orThunk((()=>{const t=Ne("\ufeff");No(e.start,t);const o=Md(n,yd.exact(t,0,t,1));return Uo(t),o}));return t.bind((e=>md(e.left,e.top,e.width,e.height)))}{const t=le(e,(e=>e.dom.getBoundingClientRect())),o={left:Math.min(t.firstCell.left,t.lastCell.left),right:Math.max(t.firstCell.right,t.lastCell.right),top:Math.min(t.firstCell.top,t.lastCell.top),bottom:Math.max(t.firstCell.bottom,t.lastCell.bottom)};return md(o.left,o.top,o.right-o.left,o.bottom-o.top)}})),a=Rd(n,t).bind((e=>Fd(e)?$e(e.start)?A.some(e.start):at(e.start):A.some(e.firstCell))).getOr(e.element);return gd(r,s,t,o,a)}))];const zd="link-layout",Ld=e=>e.x+e.width,Vd=(e,t)=>e.x-t.width,Hd=(e,t)=>e.y-t.height+e.height,Pd=e=>e.y,Ud=(e,t,o)=>Dl(Ld(e),Pd(e),o.southeast(),Il(),"southeast",Wl(e,{left:0,top:2}),zd),Wd=(e,t,o)=>Dl(Vd(e,t),Pd(e),o.southwest(),Fl(),"southwest",Wl(e,{right:1,top:2}),zd),jd=(e,t,o)=>Dl(Ld(e),Hd(e,t),o.northeast(),Rl(),"northeast",Wl(e,{left:0,bottom:3}),zd),$d=(e,t,o)=>Dl(Vd(e,t),Hd(e,t),o.northwest(),Nl(),"northwest",Wl(e,{right:1,bottom:3}),zd),Gd=()=>[Ud,Wd,jd,$d],qd=()=>[Wd,Ud,$d,jd];var Yd=[is("item"),ed(),Cs("overrides",{}),Ci("placement",((e,t,o)=>{const n=Cc(o,t.item.element),s=td(e.element,t,Gd(),qd(),Gd(),qd(),A.none());return A.some(Yc({anchorBox:n,bubble:qc(),overrides:t.overrides,layouts:s}))}))],Xd=os("type",{selection:Nd,node:pd,hotspot:od,submenu:Yd,makeshift:nd});const Kd=[hs("classes",jn),Es("mode","all",["all","layout","placement"])],Jd=[Cs("useFixed",T),fs("getBounds")],Qd=[ls("anchor",Xd),ks("transition",Kd)],Zd=(e,t,o,n,s,r)=>{const a=es("placement.info",Nn(Qd),s),i=a.anchor,l=n.element,c=o.get(n.uid);xc((()=>{Bt(l,"position","fixed");const s=zt(l,"visibility");Bt(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return Ec(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=Xt(e.element),o=e.element.dom.getBoundingClientRect();return Tc(t.left,t.top,o.width,o.height)})(e);i.placement(e,i,d).each((e=>{const s=r.orThunk((()=>t.getBounds.map(_))),i=((e,t,o,n,s,r)=>((e,t,o,n,s,r,a,i)=>{const l=Wc(a,"maxHeightFunction",Pc()),c=Wc(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:Oc(u,r),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:s,transition:i};return jc(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(t.anchorBox,e),n.element,t.bubble,t.layouts,s,o,t.overrides,r))(d,e,s,n,c,a.transition);o.set(n.uid,i)})),s.fold((()=>{Ht(l,"visibility")}),(e=>{Bt(l,"visibility",e)})),zt(l,"left").isNone()&&zt(l,"top").isNone()&&zt(l,"right").isNone()&&zt(l,"bottom").isNone()&&ye(zt(l,"position"),"fixed")&&Ht(l,"position")}),l)};var eu=Object.freeze({__proto__:null,position:(e,t,o,n,s)=>{const r=A.none();Zd(e,t,o,n,s,r)},positionWithinBounds:Zd,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const s=n.element;V(["position","left","right","top","bottom"],(e=>Ht(s,e))),(e=>{At(e,Ac)})(s),o.clear(n.uid)}});const tu=pa({fields:Jd,name:"positioning",active:pc,apis:eu,state:Object.freeze({__proto__:null,init:()=>{let e={};return ua({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>fe(e,t)})}})}),ou=e=>e.getSystem().isConnected(),nu=e=>{zr(e,Tr());const t=e.components();V(t,nu)},su=e=>{const t=e.components();V(t,su),zr(e,_r())},ru=(e,t)=>{e.getSystem().addToWorld(t),xt(e.element)&&su(t)},au=e=>{nu(e),e.getSystem().removeFromWorld(e)},iu=(e,t)=>{Vo(e.element,t.element)},lu=(e,t)=>{cu(e,t,Vo)},cu=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),xt(e.element)&&su(t),e.syncComponents()},du=e=>{nu(e),Uo(e.element),e.getSystem().removeFromWorld(e)},uu=e=>{const t=rt(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));du(e),t.each((e=>{e.syncComponents()}))},mu=e=>{const t=e.components();V(t,du),Po(e.element),e.syncComponents()},gu=(e,t)=>{hu(e,t,Vo)},pu=(e,t)=>{hu(e,t,zo)},hu=(e,t,o)=>{o(e,t.element);const n=lt(t.element);V(n,(e=>{t.getByDom(e).each(su)}))},fu=e=>{const t=lt(e.element);V(t,(t=>{e.getByDom(t).each(nu)})),Uo(e.element)},bu=(e,t,o,n)=>{o.get().each((t=>{mu(e)}));const s=t.getAttachPoint(e);lu(s,e);const r=e.getSystem().build(n);return lu(e,r),o.set(r),r},vu=(e,t,o,n)=>{const s=bu(e,t,o,n);return t.onOpen(e,s),s},yu=(e,t,o)=>{o.get().each((n=>{mu(e),uu(e),t.onClose(e,n),o.clear()}))},xu=(e,t,o)=>o.isOpen(),wu=(e,t,o)=>{const n=t.getAttachPoint(e);Bt(e.element,"position",tu.getMode(n)),((e,t,o,n)=>{zt(e.element,t).fold((()=>{At(e.element,o)}),(t=>{Ct(e.element,o,t)})),Bt(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},Su=(e,t,o)=>{(e=>R(["top","left","right","bottom"],(t=>zt(e,t).isSome())))(e.element)||Ht(e.element,"position"),((e,t,o)=>{Tt(e.element,o).fold((()=>Ht(e.element,t)),(o=>Bt(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var ku=Object.freeze({__proto__:null,cloak:wu,decloak:Su,open:vu,openWhileCloaked:(e,t,o,n,s)=>{wu(e,t),vu(e,t,o,n),s(),Su(e,t)},close:yu,isOpen:xu,isPartOf:(e,t,o,n)=>xu(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>bu(e,t,o,n)))}),Cu=Object.freeze({__proto__:null,events:(e,t)=>Wr([Gr(yr(),((o,n)=>{yu(o,e,t)}))])}),Ou=[xi("onOpen"),xi("onClose"),is("isPartOf"),is("getAttachPoint"),Cs("cloakVisibilityAttr","data-precloak-visibility")],_u=Object.freeze({__proto__:null,init:()=>{const e=rn(),t=x("not-implemented");return ua({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const Tu=pa({fields:Ou,name:"sandboxing",active:Cu,apis:ku,state:_u}),Eu=x("dismiss.popups"),Au=x("reposition.popups"),Mu=x("mouse.released"),Du=Rn([Cs("isExtraPart",T),ks("fireEventInstead",[Cs("event",Er())])]),Bu=e=>{const t=es("Dismissal",Du,e);return{[Eu()]:{schema:Rn([is("target")]),onReceive:(e,o)=>{Tu.isOpen(e)&&(Tu.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>Tu.close(e)),(t=>zr(e,t.event))))}}}},Iu=Rn([ks("fireEventInstead",[Cs("event",Ar())]),ms("doReposition")]),Fu=e=>{const t=es("Reposition",Iu,e);return{[Au()]:{onReceive:e=>{Tu.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>zr(e,t.event)))}}}},Ru=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},Nu=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var zu=Object.freeze({__proto__:null,onLoad:Ru,onUnload:Nu,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),Lu=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[ea(((o,n)=>{Ru(o,e,t)})),ta(((o,n)=>{Nu(o,e,t)}))]:[ia(e,t,Ru)];return Wr(o)}});const Vu=()=>{const e=on(null);return ua({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},Hu=()=>{const e=on({}),t=on({});return ua({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>fe(e.get(),o).orThunk((()=>fe(t.get(),o))),update:o=>{const n=e.get(),s=t.get(),r={},a={};V(o,(e=>{r[e.value]=e,fe(e,"meta").each((t=>{fe(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...r}),t.set({...s,...a})},clear:()=>{e.set({}),t.set({})}})};var Pu=Object.freeze({__proto__:null,memory:Vu,dataset:Hu,manual:()=>ua({readState:b}),init:e=>e.store.manager.state(e)});const Uu=(e,t,o,n)=>{const s=t.store;o.update([n]),s.setValue(e,n),t.onSetValue(e,n)};var Wu=[fs("initialValue"),is("getFallbackEntry"),is("getDataKey"),is("setValue"),Ci("manager",{setValue:Uu,getValue:(e,t,o)=>{const n=t.store,s=n.getDataKey(e);return o.lookup(s).getOrThunk((()=>n.getFallbackEntry(s)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{Uu(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:Hu})],ju=[is("getValue"),Cs("setValue",b),fs("initialValue"),Ci("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:da.init})],$u=[fs("initialValue"),Ci("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:Vu})],Gu=[Os("store",{mode:"memory"},os("mode",{memory:$u,manual:ju,dataset:Wu})),xi("onSetValue"),Cs("resetOnDom",!1)];const qu=pa({fields:Gu,name:"representing",active:Lu,apis:zu,extra:{setValueFrom:(e,t)=>{const o=qu.getValue(t);qu.setValue(e,o)}},state:Pu}),Yu=(e,t)=>Bs(e,{},L(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,ss(o,o,{tag:"option",process:{}},An((e=>fn("The field: "+o+" is forbidden. "+n))));var o,n})).concat([rs("dump",w)])),Xu=e=>e.dump,Ku=(e,t)=>({...ma(t),...e.dump}),Ju=Yu,Qu=Ku,Zu="placeholder",em=Is([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),tm=e=>be(e,"uiType"),om=(e,t,o,n)=>((e,t,o,n)=>tm(o)&&o.uiType===Zu?((e,t,o,n)=>e.exists((e=>e!==o.owner))?em.single(!0,x(o)):fe(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+re(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):em.single(!1,x(o)))(e,0,o,n).fold(((s,r)=>{const a=tm(o)?r(t,o.config,o.validated):r(t),i=fe(a,"components").getOr([]),l=q(i,(o=>om(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(tm(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(w)(e)}return n(t)})),nm=em.single,sm=em.multiple,rm=x(Zu),am=Is([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),im=Cs("factory",{sketch:w}),lm=Cs("schema",[]),cm=is("name"),dm=ss("pname","pname",kn((e=>"<alloy."+Bi(e.name)+">")),Pn()),um=rs("schema",(()=>[fs("preprocess")])),mm=Cs("defaults",x({})),gm=Cs("overrides",x({})),pm=Nn([im,lm,cm,dm,mm,gm]),hm=Nn([im,lm,cm,mm,gm]),fm=Nn([im,lm,cm,dm,mm,gm]),bm=Nn([im,um,cm,is("unit"),dm,mm,gm]),vm=e=>e.fold(A.some,A.none,A.some,A.some),ym=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},xm=(e,t)=>o=>{const n=es("Converting part type",t,o);return e(n)},wm=xm(am.required,pm),Sm=xm(am.external,hm),km=xm(am.optional,fm),Cm=xm(am.group,bm),Om=x("entirety");var _m=Object.freeze({__proto__:null,required:wm,external:Sm,optional:km,group:Cm,asNamedPart:vm,name:ym,asCommon:e=>e.fold(w,w,w,w),original:Om});const Tm=(e,t,o,n)=>wn(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),Em=(e,t)=>{const o={};return V(t,(t=>{vm(t).each((t=>{const n=Am(e,t.pname);o[t.name]=o=>{const s=es("Part: "+t.name+" in "+e,Nn(t.schema),o);return{...n,config:o,validated:s}}}))})),o},Am=(e,t)=>({uiType:rm(),owner:e,name:t}),Mm=(e,t,o)=>({uiType:rm(),owner:e,name:t,config:o,validated:{}}),Dm=e=>q(e,(e=>e.fold(A.none,A.some,A.none,A.none).map((e=>gs(e.name,e.schema.concat([Oi(Om())])))).toArray())),Bm=e=>L(e,ym),Im=(e,t,o)=>((e,t,o)=>{const n={},s={};return V(o,(e=>{e.fold((e=>{n[e.pname]=nm(!0,((t,o,n)=>e.factory.sketch(Tm(t,e,o,n))))}),(e=>{const o=t.parts[e.name];s[e.name]=x(e.factory.sketch(Tm(t,e,o[Om()]),o))}),(e=>{n[e.pname]=nm(!1,((t,o,n)=>e.factory.sketch(Tm(t,e,o,n))))}),(e=>{n[e.pname]=sm(!0,((t,o,n)=>{const s=t[e.name];return L(s,(o=>e.factory.sketch(wn(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:x(n),externals:x(s)}})(0,t,o),Fm=(e,t,o)=>((e,t,o,n)=>{const s=le(n,((e,t)=>((e,t)=>{let o=!1;return{name:x(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),r=((e,t,o,n)=>q(o,(o=>om(e,t,o,n))))(e,t,o,s);return ie(s,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),r})(A.some(e),t,t.components,o),Rm=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},Nm=(e,t,o)=>Rm(e,t,o).getOrDie("Could not find part: "+o),zm=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return V(o,(e=>{n[e]=x(r.getByUid(s[e]))})),n},Lm=(e,t)=>{const o=e.getSystem();return le(t.partUids,((e,t)=>x(o.getByUid(e))))},Vm=e=>re(e.partUids),Hm=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return V(o,(e=>{n[e]=x(r.getByUid(s[e]).getOrDie())})),n},Pm=(e,t)=>{const o=Bm(t);return Rs(L(o,(t=>({key:t,value:e+"-"+t}))))},Um=e=>ss("partUids","partUids",On((t=>Pm(t.uid,e))),Pn());var Wm=Object.freeze({__proto__:null,generate:Em,generateOne:Mm,schemas:Dm,names:Bm,substitutes:Im,components:Fm,defaultUids:Pm,defaultUidsSchema:Um,getAllParts:Lm,getAllPartNames:Vm,getPart:Rm,getPartOrDie:Nm,getParts:zm,getPartsOrDie:Hm});const jm=(e,t,o,n,s)=>{const r=((e,t)=>(e.length>0?[gs("parts",e)]:[]).concat([is("uid"),Cs("dom",{}),Cs("components",[]),Oi("originalSpec"),Cs("debug.sketcher",{})]).concat(t))(n,s);return es(e+" [SpecSchema]",Rn(r.concat(t)),o)},$m=(e,t,o,n,s)=>{const r=Gm(s),a=Dm(o),i=Um(o),l=jm(e,t,r,a,[i]),c=Im(0,l,o);return n(l,Fm(e,l,c.internals()),r,c.externals())},Gm=e=>(e=>be(e,"uid"))(e)?e:{...e,uid:Vi("uid")},qm=Rn([is("name"),is("factory"),is("configFields"),Cs("apis",{}),Cs("extraApis",{})]),Ym=Rn([is("name"),is("factory"),is("configFields"),is("partFields"),Cs("apis",{}),Cs("extraApis",{})]),Xm=e=>{const t=es("Sketcher for "+e.name,qm,e),o=le(t.apis,Gi),n=le(t.extraApis,((e,t)=>ra(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const s=Gm(n);return o(jm(e,t,s,[],[]),s)})(t.name,t.configFields,t.factory,e),...o,...n}},Km=e=>{const t=es("Sketcher for "+e.name,Ym,e),o=Em(t.name,t.partFields),n=le(t.apis,Gi),s=le(t.extraApis,((e,t)=>ra(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>$m(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...s}},Jm=e=>Xe("input")(e)&&"radio"!==_t(e,"type")||Xe("textarea")(e);var Qm=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const Zm=[is("find")],eg=pa({fields:Zm,name:"composing",apis:Qm}),tg=["input","button","textarea","select"],og=(e,t,o)=>{(t.disabled()?lg:cg)(e,t)},ng=(e,t)=>!0===t.useNative&&F(tg,Ue(e.element)),sg=e=>{Ct(e.element,"disabled","disabled")},rg=e=>{At(e.element,"disabled")},ag=e=>{Ct(e.element,"aria-disabled","true")},ig=e=>{Ct(e.element,"aria-disabled","false")},lg=(e,t,o)=>{t.disableClass.each((t=>{ka(e.element,t)})),(ng(e,t)?sg:ag)(e),t.onDisabled(e)},cg=(e,t,o)=>{t.disableClass.each((t=>{Oa(e.element,t)})),(ng(e,t)?rg:ig)(e),t.onEnabled(e)},dg=(e,t)=>ng(e,t)?(e=>Et(e.element,"disabled"))(e):(e=>"true"===_t(e.element,"aria-disabled"))(e);var ug=Object.freeze({__proto__:null,enable:cg,disable:lg,isDisabled:dg,onLoad:og,set:(e,t,o,n)=>{(n?lg:cg)(e,t)}}),mg=Object.freeze({__proto__:null,exhibit:(e,t)=>aa({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>Wr([jr(hr(),((t,o)=>dg(t,e))),ia(e,t,og)])}),gg=[Ms("disabled",T),Cs("useNative",!0),fs("disableClass"),xi("onDisabled"),xi("onEnabled")];const pg=pa({fields:gg,name:"disabling",active:mg,apis:ug}),hg=(e,t,o,n)=>{const s=Td(e.element,"."+t.highlightClass);V(s,(o=>{R(n,(e=>Ze(e.element,o)))||(Oa(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),zr(o,Nr())})))}))},fg=(e,t,o,n)=>{hg(e,t,0,[n]),bg(e,t,o,n)||(ka(n.element,t.highlightClass),t.onHighlight(e,n),zr(n,Rr()))},bg=(e,t,o,n)=>_a(n.element,t.highlightClass),vg=(e,t,o)=>_l(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),yg=(e,t,o)=>{const n=Td(e.element,"."+t.itemClass);return(n.length>0?A.some(n[n.length-1]):A.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},xg=(e,t,o,n)=>{const s=Td(e.element,"."+t.itemClass);return $(s,(e=>_a(e,t.highlightClass))).bind((t=>{const o=Pl(t,n,0,s.length-1);return e.getSystem().getByDom(s[o]).toOptional()}))},wg=(e,t,o)=>{const n=Td(e.element,"."+t.itemClass);return xe(L(n,(t=>e.getSystem().getByDom(t).toOptional())))};var Sg=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>hg(e,t,0,[]),dehighlight:(e,t,o,n)=>{bg(e,t,o,n)&&(Oa(n.element,t.highlightClass),t.onDehighlight(e,n),zr(n,Nr()))},highlight:fg,highlightFirst:(e,t,o)=>{vg(e,t).each((n=>{fg(e,t,o,n)}))},highlightLast:(e,t,o)=>{yg(e,t).each((n=>{fg(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const s=Td(e.element,"."+t.itemClass);return A.from(s[n]).fold((()=>dn.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{fg(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const s=wg(e,t);j(s,n).each((n=>{fg(e,t,o,n)}))},isHighlighted:bg,getHighlighted:(e,t,o)=>_l(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:vg,getLast:yg,getPrevious:(e,t,o)=>xg(e,t,0,-1),getNext:(e,t,o)=>xg(e,t,0,1),getCandidates:wg}),kg=[is("highlightClass"),is("itemClass"),xi("onHighlight"),xi("onDehighlight")];const Cg=pa({fields:kg,name:"highlighting",apis:Sg}),Og=[8],_g=[9],Tg=[13],Eg=[27],Ag=[32],Mg=[37],Dg=[38],Bg=[39],Ig=[40],Fg=(e,t,o)=>{const n=X(e.slice(0,t)),s=X(e.slice(t+1));return j(n.concat(s),o)},Rg=(e,t,o)=>{const n=X(e.slice(0,t));return j(n,o)},Ng=(e,t,o)=>{const n=e.slice(0,t),s=e.slice(t+1);return j(s.concat(n),o)},zg=(e,t,o)=>{const n=e.slice(t+1);return j(n,o)},Lg=e=>t=>{const o=t.raw;return F(e,o.which)},Vg=e=>t=>Y(e,(e=>e(t))),Hg=e=>!0===e.raw.shiftKey,Pg=e=>!0===e.raw.ctrlKey,Ug=C(Hg),Wg=(e,t)=>({matches:e,classification:t}),jg=(e,t,o)=>{t.exists((e=>o.exists((t=>Ze(t,e)))))||Lr(e,Mr(),{prevFocus:t,newFocus:o})},$g=()=>{const e=e=>yc(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const s=e(t);jg(t,n,s)}}},Gg=()=>{const e=e=>Cg.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Cg.highlight(t,e)}));const s=e(t);jg(t,n,s)}}};var qg;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}(qg||(qg={}));const Yg=(e,t,o,n,s)=>{const r=(e,t,o,n,s)=>{return(r=o(e,t,n,s),a=t.event,j(r,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,s)));var r,a},a={schema:()=>e.concat([Cs("focusManager",$g()),Os("focusInside","onFocus",Kn((e=>F(["onFocus","onEnterOrSpace","onApi"],e)?dn.value(e):dn.error("Invalid value for focusInside")))),Ci("handler",a),Ci("state",t),Ci("sendFocusIn",s)]),processKey:r,toEvents:(e,t)=>{const a=e.focusInside!==qg.OnFocusMode?A.none():s(e).map((o=>Gr(ur(),((n,s)=>{o(n,e,t),s.stop()})))),i=[Gr(er(),((n,a)=>{r(n,a,o,e,t).fold((()=>{((o,n)=>{const r=Lg(Ag.concat(Tg))(n.event);e.focusInside===qg.OnEnterOrSpaceMode&&r&&Vs(o,n)&&s(e).each((s=>{s(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),Gr(tr(),((o,s)=>{r(o,s,n,e,t).each((e=>{s.stop()}))}))];return Wr(a.toArray().concat(i))}};return a},Xg=e=>{const t=[fs("onEscape"),fs("onEnter"),Cs("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),Cs("firstTabstop",0),Cs("useTabstopAt",E),fs("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>Tl(t,e))).getOr(t);return jt(o)>0},n=(e,t)=>t.focusManager.get(e).bind((e=>Tl(e,t.selector))),s=(e,t,n)=>{((e,t)=>{const n=Td(e.element,t.selector),s=P(n,(e=>o(t,e)));return A.from(s[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},r=(e,t,s,r)=>{const a=Td(e.element,s.selector);return n(e,s).bind((t=>$(a,k(Ze,t)).bind((t=>((e,t,n,s,r)=>r(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(s,e))).fold((()=>s.cyclic?A.some(!0):A.none()),(t=>(s.focusManager.set(e,t),A.some(!0)))))(e,a,t,s,r)))))},a=(e,t,o)=>{const n=o.cyclic?Fg:Rg;return r(e,0,o,n)},i=(e,t,o)=>{const n=o.cyclic?Ng:zg;return r(e,0,o,n)},l=x([Wg(Vg([Hg,Lg(_g)]),a),Wg(Lg(_g),i),Wg(Vg([Ug,Lg(Tg)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),c=x([Wg(Lg(Eg),((e,t,o)=>o.onEscape.bind((o=>o(e,t))))),Wg(Lg(_g),((e,t,o)=>n(e,o).filter((e=>!o.useTabstopAt(e))).bind((n=>((e=>(e=>rt(e))(e).bind(dt).exists((t=>Ze(t,e))))(n)?a:i)(e,t,o)))))]);return Yg(t,da.init,l,c,(()=>A.some(s)))};var Kg=Xg(rs("cyclic",T)),Jg=Xg(rs("cyclic",E));const Qg=(e,t,o)=>Jm(o)&&Lg(Ag)(t.event)?A.none():((e,t,o)=>(Hr(e,o,hr()),A.some(!0)))(e,0,o),Zg=(e,t)=>A.some(!0),ep=[Cs("execute",Qg),Cs("useSpace",!1),Cs("useEnter",!0),Cs("useControlEnter",!1),Cs("useDown",!1)],tp=(e,t,o)=>o.execute(e,t,e.element);var op=Yg(ep,da.init,((e,t,o,n)=>{const s=o.useSpace&&!Jm(e.element)?Ag:[],r=o.useEnter?Tg:[],a=o.useDown?Ig:[],i=s.concat(r).concat(a);return[Wg(Lg(i),tp)].concat(o.useControlEnter?[Wg(Vg([Pg,Lg(Tg)]),tp)]:[])}),((e,t,o,n)=>o.useSpace&&!Jm(e.element)?[Wg(Lg(Ag),Zg)]:[]),(()=>A.none()));const np=()=>{const e=rn();return ua({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var sp=Object.freeze({__proto__:null,flatgrid:np,init:e=>e.state(e)});const rp=e=>(t,o,n,s)=>{const r=e(t.element);return cp(r,t,o,n,s)},ap=(e,t)=>{const o=Xc(e,t);return rp(o)},ip=(e,t)=>{const o=Xc(t,e);return rp(o)},lp=e=>(t,o,n,s)=>cp(e,t,o,n,s),cp=(e,t,o,n,s)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,s))).map((e=>(n.focusManager.set(t,e),!0))),dp=lp,up=lp,mp=lp,gp=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),pp=(e,t,o)=>{const n=Td(e,o);return((e,o)=>$(e,(e=>Ze(e,t))).map((t=>({index:t,candidates:e}))))(P(n,gp))},hp=(e,t)=>$(e,(e=>Ze(t,e))),fp=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?A.some(e[n]):A.none()})),bp=(e,t,o,n,s)=>fp(e,t,n,((t,r)=>{const a=t===o-1?e.length-t*n:n,i=Pl(r,s,0,a-1);return A.some({row:t,column:i})})),vp=(e,t,o,n,s)=>fp(e,t,n,((t,r)=>{const a=Pl(t,s,0,o-1),i=a===o-1?e.length-a*n:n,l=Ul(r,0,i-1);return A.some({row:a,column:l})})),yp=[is("selector"),Cs("execute",Qg),wi("onEscape"),Cs("captureTab",!1),_i()],xp=(e,t,o)=>{_l(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},wp=e=>(t,o,n,s)=>pp(t,o,n.selector).bind((t=>e(t.candidates,t.index,s.getNumRows().getOr(n.initSize.numRows),s.getNumColumns().getOr(n.initSize.numColumns)))),Sp=(e,t,o)=>o.captureTab?A.some(!0):A.none(),kp=wp(((e,t,o,n)=>bp(e,t,o,n,-1))),Cp=wp(((e,t,o,n)=>bp(e,t,o,n,1))),Op=wp(((e,t,o,n)=>vp(e,t,o,n,-1))),_p=wp(((e,t,o,n)=>vp(e,t,o,n,1))),Tp=x([Wg(Lg(Mg),ap(kp,Cp)),Wg(Lg(Bg),ip(kp,Cp)),Wg(Lg(Dg),dp(Op)),Wg(Lg(Ig),up(_p)),Wg(Vg([Hg,Lg(_g)]),Sp),Wg(Vg([Ug,Lg(_g)]),Sp),Wg(Lg(Ag.concat(Tg)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>Tl(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),Ep=x([Wg(Lg(Eg),((e,t,o)=>o.onEscape(e,t))),Wg(Lg(Ag),Zg)]);var Ap=Yg(yp,np,Tp,Ep,(()=>A.some(xp)));const Mp=(e,t,o,n,s)=>{const r=(e,t,o)=>s(e,t,n,0,o.length-1,o[t],(t=>{return n=o[t],"button"===Ue(n)&&"disabled"===_t(n,"disabled")?r(e,t,o):A.from(o[t]);var n}));return pp(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return r(t,t,o)}))},Dp=(e,t,o,n)=>Mp(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Ul(t+o,n,s);return i===e?A.from(r):a(i)})),Bp=(e,t,o,n)=>Mp(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Pl(t,o,n,s);return i===e?A.none():a(i)})),Ip=[is("selector"),Cs("getInitial",A.none),Cs("execute",Qg),wi("onEscape"),Cs("executeOnMove",!1),Cs("allowVertical",!0),Cs("allowHorizontal",!0),Cs("cycles",!0)],Fp=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>Tl(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),Rp=(e,t,o)=>{t.getInitial(e).orThunk((()=>_l(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},Np=(e,t,o)=>(o.cycles?Bp:Dp)(e,o.selector,t,-1),zp=(e,t,o)=>(o.cycles?Bp:Dp)(e,o.selector,t,1),Lp=e=>(t,o,n,s)=>e(t,o,n,s).bind((()=>n.executeOnMove?Fp(t,o,n):A.some(!0))),Vp=x([Wg(Lg(Ag),Zg),Wg(Lg(Eg),((e,t,o)=>o.onEscape(e,t)))]);var Hp=Yg(Ip,da.init,((e,t,o,n)=>{const s=[...o.allowHorizontal?Mg:[]].concat(o.allowVertical?Dg:[]),r=[...o.allowHorizontal?Bg:[]].concat(o.allowVertical?Ig:[]);return[Wg(Lg(s),Lp(ap(Np,zp))),Wg(Lg(r),Lp(ip(Np,zp))),Wg(Lg(Tg),Fp),Wg(Lg(Ag),Fp)]}),Vp,(()=>A.some(Rp)));const Pp=(e,t,o)=>A.from(e[t]).bind((e=>A.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),Up=(e,t,o,n)=>{const s=e[t].length,r=Pl(o,n,0,s-1);return Pp(e,t,r)},Wp=(e,t,o,n)=>{const s=Pl(o,n,0,e.length-1),r=e[s].length,a=Ul(t,0,r-1);return Pp(e,s,a)},jp=(e,t,o,n)=>{const s=e[t].length,r=Ul(o+n,0,s-1);return Pp(e,t,r)},$p=(e,t,o,n)=>{const s=Ul(o+n,0,e.length-1),r=e[s].length,a=Ul(t,0,r-1);return Pp(e,s,a)},Gp=[gs("selectors",[is("row"),is("cell")]),Cs("cycles",!0),Cs("previousSelector",A.none),Cs("execute",Qg)],qp=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return _l(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},Yp=(e,t)=>(o,n,s)=>{const r=s.cycles?e:t;return Tl(n,s.selectors.row).bind((e=>{const t=Td(e,s.selectors.cell);return hp(t,n).bind((t=>{const n=Td(o,s.selectors.row);return hp(n,e).bind((e=>{const o=((e,t)=>L(e,(e=>Td(e,t.selectors.cell))))(n,s);return r(o,e,t).map((e=>e.cell))}))}))}))},Xp=Yp(((e,t,o)=>Up(e,t,o,-1)),((e,t,o)=>jp(e,t,o,-1))),Kp=Yp(((e,t,o)=>Up(e,t,o,1)),((e,t,o)=>jp(e,t,o,1))),Jp=Yp(((e,t,o)=>Wp(e,o,t,-1)),((e,t,o)=>$p(e,o,t,-1))),Qp=Yp(((e,t,o)=>Wp(e,o,t,1)),((e,t,o)=>$p(e,o,t,1))),Zp=x([Wg(Lg(Mg),ap(Xp,Kp)),Wg(Lg(Bg),ip(Xp,Kp)),Wg(Lg(Dg),dp(Jp)),Wg(Lg(Ig),up(Qp)),Wg(Lg(Ag.concat(Tg)),((e,t,o)=>yc(e.element).bind((n=>o.execute(e,t,n)))))]),eh=x([Wg(Lg(Ag),Zg)]);var th=Yg(Gp,da.init,Zp,eh,(()=>A.some(qp)));const oh=[is("selector"),Cs("execute",Qg),Cs("moveOnTab",!1)],nh=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),sh=(e,t,o)=>{_l(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},rh=(e,t,o)=>Bp(e,o.selector,t,-1),ah=(e,t,o)=>Bp(e,o.selector,t,1),ih=x([Wg(Lg(Dg),mp(rh)),Wg(Lg(Ig),mp(ah)),Wg(Vg([Hg,Lg(_g)]),((e,t,o,n)=>o.moveOnTab?mp(rh)(e,t,o,n):A.none())),Wg(Vg([Ug,Lg(_g)]),((e,t,o,n)=>o.moveOnTab?mp(ah)(e,t,o,n):A.none())),Wg(Lg(Tg),nh),Wg(Lg(Ag),nh)]),lh=x([Wg(Lg(Ag),Zg)]);var ch=Yg(oh,da.init,ih,lh,(()=>A.some(sh)));const dh=[wi("onSpace"),wi("onEnter"),wi("onShiftEnter"),wi("onLeft"),wi("onRight"),wi("onTab"),wi("onShiftTab"),wi("onUp"),wi("onDown"),wi("onEscape"),Cs("stopSpaceKeyup",!1),fs("focusIn")];var uh=Yg(dh,da.init,((e,t,o)=>[Wg(Lg(Ag),o.onSpace),Wg(Vg([Ug,Lg(Tg)]),o.onEnter),Wg(Vg([Hg,Lg(Tg)]),o.onShiftEnter),Wg(Vg([Hg,Lg(_g)]),o.onShiftTab),Wg(Vg([Ug,Lg(_g)]),o.onTab),Wg(Lg(Dg),o.onUp),Wg(Lg(Ig),o.onDown),Wg(Lg(Mg),o.onLeft),Wg(Lg(Bg),o.onRight),Wg(Lg(Ag),o.onSpace)]),((e,t,o)=>[...o.stopSpaceKeyup?[Wg(Lg(Ag),Zg)]:[],Wg(Lg(Eg),o.onEscape)]),(e=>e.focusIn));const mh=Kg.schema(),gh=Jg.schema(),ph=Hp.schema(),hh=Ap.schema(),fh=th.schema(),bh=op.schema(),vh=ch.schema(),yh=uh.schema(),xh=fa({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:mh,cyclic:gh,flow:ph,flatgrid:hh,matrix:fh,execution:bh,menu:vh,special:yh}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,s)=>{(e=>ve(e,"setGridSize"))(o)?o.setGridSize(n,s):console.error("Layout does not support setGridSize")}},state:sp}),wh=(e,t)=>{xc((()=>{((e,t,o)=>{const n=e.components();(e=>{V(e.components(),(e=>Uo(e.element))),Po(e.element),e.syncComponents()})(e);const s=o(t),r=K(n,s);V(r,(t=>{nu(t),e.getSystem().removeFromWorld(t)})),V(s,(t=>{ou(t)?iu(e,t):(e.getSystem().addToWorld(t),iu(e,t),xt(e.element)&&su(t))})),e.syncComponents()})(e,t,(()=>L(t,e.getSystem().build)))}),e.element)},Sh=(e,t)=>{xc((()=>{((o,n,s)=>{const r=o.components(),a=q(n,(e=>$i(e).toArray()));V(r,(e=>{F(a,e)||au(e)}));const i=((e,t,o)=>rl(e,t,((t,n)=>al(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),l=K(r,i);V(l,(e=>{ou(e)&&au(e)})),V(i,(e=>{ou(e)||ru(o,e)})),o.syncComponents()})(e,t)}),e.element)},kh=(e,t,o,n)=>{au(t);const s=al(e.element,o,n,e.getSystem().buildOrPatch);ru(e,s),e.syncComponents()},Ch=(e,t,o)=>{const n=e.getSystem().build(o);cu(e,n,t)},Oh=(e,t,o,n)=>{uu(t),Ch(e,((e,t)=>((e,t,o)=>{ct(e,o).fold((()=>{Vo(e,t)}),(e=>{No(e,t)}))})(e,t,o)),n)},_h=(e,t)=>e.components(),Th=(e,t,o,n,s)=>{const r=_h(e);return A.from(r[n]).map((o=>(s.fold((()=>uu(o)),(s=>{(t.reuseDom?kh:Oh)(e,o,n,s)})),o)))};var Eh=Object.freeze({__proto__:null,append:(e,t,o,n)=>{Ch(e,Vo,n)},prepend:(e,t,o,n)=>{Ch(e,Lo,n)},remove:(e,t,o,n)=>{const s=_h(e),r=j(s,(e=>Ze(n.element,e.element)));r.each(uu)},replaceAt:Th,replaceBy:(e,t,o,n,s)=>{const r=_h(e);return $(r,n).bind((o=>Th(e,t,0,o,s)))},set:(e,t,o,n)=>(t.reuseDom?Sh:wh)(e,n),contents:_h});const Ah=pa({fields:[As("reuseDom",!0)],name:"replacing",apis:Eh}),Mh=(e,t)=>{const o=((e,t)=>{const o=Wr(t);return pa({fields:[is("enabled")],name:e,active:{events:x(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:x({}),initialConfig:{},state:da}}},Dh=(e,t)=>{t.ignore||(hc(e.element),t.onFocus(e))};var Bh=Object.freeze({__proto__:null,focus:Dh,blur:(e,t)=>{t.ignore||fc(e.element)},isFocused:e=>bc(e.element)}),Ih=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return aa(o)},events:e=>Wr([Gr(ur(),((t,o)=>{Dh(t,e),o.stop()}))].concat(e.stopMousedown?[Gr(qs(),((e,t)=>{t.event.prevent()}))]:[]))}),Fh=[xi("onFocus"),Cs("stopMousedown",!1),Cs("ignore",!1)];const Rh=pa({fields:Fh,name:"focusing",active:Ih,apis:Bh}),Nh=(e,t,o,n)=>{const s=o.get();o.set(n),((e,t,o)=>{t.toggleClass.each((t=>{o.get()?ka(e.element,t):Oa(e.element,t)}))})(e,t,o),((e,t,o)=>{const n=t.aria;n.update(e,n,o.get())})(e,t,o),s!==n&&t.onToggled(e,n)},zh=(e,t,o)=>{Nh(e,t,o,!o.get())},Lh=(e,t,o)=>{Nh(e,t,o,t.selected)};var Vh=Object.freeze({__proto__:null,onLoad:Lh,toggle:zh,isOn:(e,t,o)=>o.get(),on:(e,t,o)=>{Nh(e,t,o,!0)},off:(e,t,o)=>{Nh(e,t,o,!1)},set:Nh}),Hh=Object.freeze({__proto__:null,exhibit:()=>aa({}),events:(e,t)=>{const o=(n=e,s=t,r=zh,na((e=>{r(e,n,s)})));var n,s,r;const a=ia(e,t,Lh);return Wr(G([e.toggleOnExecute?[o]:[],[a]]))}});const Ph=(e,t,o)=>{Ct(e.element,"aria-expanded",o)};var Uh=[Cs("selected",!1),fs("toggleClass"),Cs("toggleOnExecute",!0),xi("onToggled"),Os("aria",{mode:"none"},os("mode",{pressed:[Cs("syncWithExpanded",!1),Ci("update",((e,t,o)=>{Ct(e.element,"aria-pressed",o),t.syncWithExpanded&&Ph(e,0,o)}))],checked:[Ci("update",((e,t,o)=>{Ct(e.element,"aria-checked",o)}))],expanded:[Ci("update",Ph)],selected:[Ci("update",((e,t,o)=>{Ct(e.element,"aria-selected",o)}))],none:[Ci("update",b)]}))];const Wh=pa({fields:Uh,name:"toggling",active:Hh,apis:Vh,state:(!1,{init:()=>{const e=on(false);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(false),readState:()=>e.get()}}})});const jh=()=>{const e=(e,t)=>{t.stop(),Vr(e)};return[Gr(sr(),e),Gr(br(),e),Jr(Ws()),Jr(qs())]},$h=e=>Wr(G([e.map((e=>na(((t,o)=>{e(t),o.stop()})))).toArray(),jh()])),Gh="alloy.item-hover",qh="alloy.item-focus",Yh="alloy.item-toggled",Xh=e=>{(yc(e.element).isNone()||Rh.isFocused(e))&&(Rh.isFocused(e)||Rh.focus(e),Lr(e,Gh,{item:e}))},Kh=e=>{Lr(e,qh,{item:e})},Jh=x(Gh),Qh=x(qh),Zh=x(Yh),ef=e=>e.role.fold((()=>e.toggling.map((e=>e.exclusive?"menuitemradio":"menuitemcheckbox")).getOr("menuitem")),w),tf=[is("data"),is("components"),is("dom"),Cs("hasSubmenu",!1),fs("toggling"),fs("role"),Ju("itemBehaviours",[Wh,Rh,xh,qu]),Cs("ignoreFocus",!1),Cs("domModification",{}),Ci("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:ef(e),...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:Qu(e.itemBehaviours,[e.toggling.fold(Wh.revoke,(t=>Wh.config(((e,t)=>({aria:{mode:t?"selected":"checked"},...me(e,((e,t)=>"exclusive"!==t)),onToggled:(t,o)=>{p(e.onToggled)&&e.onToggled(t,o),((e,t)=>{Lr(e,Yh,{item:e,state:t})})(t,o)}}))(t,e.role.exists((e=>"option"===e)))))),Rh.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{Kh(e)}}),xh.config({mode:"execution"}),qu.config({store:{mode:"memory",initialValue:e.data}}),Mh("item-type-events",[...jh(),Gr(Js(),Xh),Gr(fr(),Rh.focus)])]),components:e.components,eventOrder:e.eventOrder}))),Cs("eventOrder",{})],of=[is("dom"),is("components"),Ci("builder",(e=>({dom:e.dom,components:e.components,events:Wr([Qr(fr())])})))],nf=x("item-widget"),sf=x([wm({name:"widget",overrides:e=>({behaviours:ma([qu.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),rf=[is("uid"),is("data"),is("components"),is("dom"),Cs("autofocus",!1),Cs("ignoreFocus",!1),Ju("widgetBehaviours",[qu,Rh,xh]),Cs("domModification",{}),Um(sf()),Ci("builder",(e=>{const t=Im(nf(),e,sf()),o=Fm(nf(),e,t.internals()),n=t=>Rm(t,e,"widget").map((e=>(xh.focusIn(e),e))),s=(t,o)=>Jm(o.event.target)?A.none():e.autofocus?(o.setSource(t.element),A.none()):A.none();return{dom:e.dom,components:o,domModification:e.domModification,events:Wr([na(((e,t)=>{n(e).each((e=>{t.stop()}))})),Gr(Js(),Xh),Gr(fr(),((t,o)=>{e.autofocus?n(t):Rh.focus(t)}))]),behaviours:Qu(e.widgetBehaviours,[qu.config({store:{mode:"memory",initialValue:e.data}}),Rh.config({ignore:e.ignoreFocus,onFocus:e=>{Kh(e)}}),xh.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:ba(),onLeft:s,onRight:s,onEscape:(t,o)=>Rh.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),A.none()):A.none():(Rh.focus(t),A.some(!0))})])}}))],af=os("type",{widget:rf,item:tf,separator:of}),lf=x([Cm({factory:{sketch:e=>{const t=es("menu.spec item",af,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>be(t,"uid")?t:{...t,uid:Vi("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),cf=x([ys("role"),is("value"),is("items"),is("dom"),is("components"),Cs("eventOrder",{}),Yu("menuBehaviours",[Cg,qu,eg,xh]),Os("movement",{mode:"menu",moveOnTab:!0},os("mode",{grid:[_i(),Ci("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[Ci("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},previousSelector:t.previousSelector,focusManager:e.focusManager}))),is("rowSelector"),Cs("previousSelector",A.none)],menu:[Cs("moveOnTab",!0),Ci("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),ls("markers",hi()),Cs("fakeFocus",!1),Cs("focusManager",$g()),xi("onHighlight"),xi("onDehighlight"),Cs("showMenuRole",!0)]),df=x("alloy.menu-focus"),uf=Km({name:"Menu",configFields:cf(),partFields:lf(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Ku(e.menuBehaviours,[Cg.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight,onDehighlight:e.onDehighlight}),qu.config({store:{mode:"memory",initialValue:e.value}}),eg.config({find:A.some}),xh.config(e.movement.config(e,e.movement))]),events:Wr([Gr(Qh(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Cg.highlight(e,o),t.stop(),Lr(e,df(),{menu:e,item:o})}))})),Gr(Jh(),((e,t)=>{const o=t.event.item;Cg.highlight(e,o)})),Gr(Zh(),((e,t)=>{const{item:o,state:n}=t.event;n&&"menuitemradio"===_t(o.element,"role")&&((e,t)=>{const o=Td(e.element,'[role="menuitemradio"][aria-checked="true"]');V(o,(o=>{Ze(o,t.element)||e.getSystem().getByDom(o).each((e=>{Wh.off(e)}))}))})(e,o)}))]),components:t,eventOrder:e.eventOrder,...e.showMenuRole?{domModification:{attributes:{role:e.role.getOr("menu")}}}:{}})}),mf=(e,t,o,n)=>fe(o,n).bind((n=>fe(e,n).bind((n=>{const s=mf(e,t,o,n);return A.some([n].concat(s))})))).getOr([]),gf=e=>"prepared"===e.type?A.some(e.menu):A.none(),pf=()=>{const e=on({}),t=on({}),o=on({}),n=rn(),s=on({}),r=e=>a(e).bind(gf),a=e=>fe(t.get(),e),i=t=>fe(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(r,a,i,l)=>{n.set(r),e.set(i),t.set(a),s.set(l);const c=((e,t)=>{const o={};ie(e,((e,t)=>{V(e,(e=>{o[e]=t}))}));const n=t,s=ce(t,((e,t)=>({k:e,v:t}))),r=le(s,((e,t)=>[t].concat(mf(o,n,s,t))));return le(o,(e=>fe(r,e).getOr([e])))})(l,i);o.set(c)},expand:t=>fe(e.get(),t).map((e=>{const n=fe(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>fe(o.get(),e),collapse:e=>fe(o.get(),e).bind((e=>e.length>1?A.some(e.slice(1)):A.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=s.get();return K(re(t),e)},getPrimary:()=>n.get().bind(r),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,s)=>{const a=P(i(t).toArray(),(e=>r(e).isSome()));return fe(o.get(),t).bind((t=>{const o=X(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return A.none();t.push(n.getOrDie())}return A.some(t)})(q(o,((t,a)=>((t,o,n)=>r(t).bind((s=>(t=>pe(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:s,triggeringItem:e,triggeringPath:n}))))))))(t,s,o.slice(0,a+1)).fold((()=>ye(n.get(),t)?[]:[A.none()]),(e=>[A.some(e)])))))}))}}},hf=gf,ff=Bi("tiered-menu-item-highlight"),bf=Bi("tiered-menu-item-dehighlight");var vf;!function(e){e[e.HighlightMenuAndItem=0]="HighlightMenuAndItem",e[e.HighlightJustMenu=1]="HighlightJustMenu",e[e.HighlightNone=2]="HighlightNone"}(vf||(vf={}));const yf=x("collapse-item"),xf=Xm({name:"TieredMenu",configFields:[ki("onExecute"),ki("onEscape"),Si("onOpenMenu"),Si("onOpenSubmenu"),xi("onRepositionMenu"),xi("onCollapseMenu"),Cs("highlightOnOpen",vf.HighlightMenuAndItem),gs("data",[is("primary"),is("menus"),is("expansions")]),Cs("fakeFocus",!1),xi("onHighlightItem"),xi("onDehighlightItem"),xi("onHover"),bi(),is("dom"),Cs("navigateOnHover",!0),Cs("stayInDom",!1),Yu("tmenuBehaviours",[xh,Cg,eg,Ah]),Cs("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=rn(),n=pf(),s=e=>qu.getValue(e).value,r=t=>le(e.data.menus,((e,t)=>q(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=Cg.highlight,i=(t,o)=>{a(t,o),Cg.getHighlighted(o).orThunk((()=>Cg.getFirst(o))).each((n=>{e.fakeFocus?Cg.highlight(o,n):Hr(t,n.element,fr())}))},l=(e,t)=>xe(L(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?A.some(e.menu):A.none()))))),c=(t,o,n)=>{const s=l(o,o.otherMenus(n));V(s,(o=>{Ea(o.element,[e.markers.backgroundMenu]),e.stayInDom||Ah.remove(t,o)}))},d=(t,n)=>{const r=(t=>o.get().getOrThunk((()=>{const n={},r=Td(t.element,`.${e.markers.item}`),a=P(r,(e=>"true"===_t(e,"aria-haspopup")));return V(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=s(e);n[t]=e}))})),o.set(n),n})))(t);ie(r,((e,t)=>{const o=F(n,t);Ct(e.element,"aria-expanded",o)}))},u=(t,o,n)=>A.from(n[0]).bind((s=>o.lookupMenu(s).bind((s=>{if("notbuilt"===s.type)return A.none();{const r=s.menu,a=l(o,n.slice(1));return V(a,(t=>{ka(t.element,e.markers.backgroundMenu)})),xt(r.element)||Ah.append(t,fl(r)),Ea(r.element,[e.markers.backgroundMenu]),i(t,r),c(t,o,n),A.some(r)}}))));let m;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(m||(m={}));const g=(t,o,r=m.HighlightSubmenu)=>{if(o.hasConfigured(pg)&&pg.isDisabled(o))return A.some(o);{const a=s(o);return n.expand(a).bind((s=>(d(t,s),A.from(s[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const s=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,s),s}return o.menu})(t,a,i);return xt(l.element)||Ah.append(t,fl(l)),e.onOpenSubmenu(t,o,l,X(s)),r===m.HighlightSubmenu?(Cg.highlightFirst(l),u(t,n,s)):(Cg.dehighlightAll(l),A.some(o))})))))))}},p=(t,o)=>{const r=s(o);return n.collapse(r).bind((s=>(d(t,s),u(t,n,s).map((n=>(e.onCollapseMenu(t,o,n),n))))))},h=t=>(o,n)=>Tl(n.getSource(),`.${e.markers.item}`).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(E))))),f=Wr([Gr(df(),((e,t)=>{const o=t.event.item;n.lookupItem(s(o)).each((()=>{const o=t.event.menu;Cg.highlight(e,o);const r=s(t.event.item);n.refresh(r).each((t=>c(e,n,t)))}))})),na(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===s(o).indexOf("collapse-item")&&p(t,o),g(t,o,m.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),ea(((t,o)=>{(t=>{const o=((t,o,n)=>le(n,((n,s)=>{const r=()=>uf.sketch({...n,value:s,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:(e,t)=>{Lr(e,ff,{menuComp:e,itemComp:t})},onDehighlight:(e,t)=>{Lr(e,bf,{menuComp:e,itemComp:t})},focusManager:e.fakeFocus?Gg():$g()});return s===o?{type:"prepared",menu:t.getSystem().build(r())}:{type:"notbuilt",nbMenu:r}})))(t,e.data.primary,e.data.menus),s=r();return n.setContents(e.data.primary,o,e.data.expansions,s),n.getPrimary()})(t).each((o=>{Ah.append(t,fl(o)),e.onOpenMenu(t,o),e.highlightOnOpen===vf.HighlightMenuAndItem?i(t,o):e.highlightOnOpen===vf.HighlightJustMenu&&a(t,o)}))})),Gr(ff,((t,o)=>{e.onHighlightItem(t,o.event.menuComp,o.event.itemComp)})),Gr(bf,((t,o)=>{e.onDehighlightItem(t,o.event.menuComp,o.event.itemComp)})),...e.navigateOnHover?[Gr(Jh(),((t,o)=>{const r=o.event.item;((e,t)=>{const o=s(t);n.refresh(o).bind((t=>(d(e,t),u(e,n,t))))})(t,r),g(t,r,m.HighlightParent),e.onHover(t,r)}))]:[]]),v=e=>Cg.getHighlighted(e).bind(Cg.getHighlighted),y={collapseMenu:e=>{v(e).each((t=>{p(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{i(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>v(t).bind((e=>{const t=s(e),o=he(n.getMenus()),r=xe(L(o,hf));return n.getTriggeringPath(t,(e=>((e,t,o)=>se(t,(e=>{if(!e.getSystem().isConnected())return A.none();const t=Cg.getCandidates(e);return j(t,(e=>s(e)===o))})))(0,r,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>A.from(e.components()[0]).filter((e=>"menu"===_t(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Ku(e.tmenuBehaviours,[xh.config({mode:"special",onRight:h(((e,t)=>Jm(t.element)?A.none():g(e,t,m.HighlightSubmenu))),onLeft:h(((e,t)=>Jm(t.element)?A.none():p(e,t))),onEscape:h(((t,o)=>p(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{Hr(e,t.element,fr())}))}}),Cg.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),eg.config({find:e=>Cg.getHighlighted(e)}),Ah.config({})]),eventOrder:e.eventOrder,apis:y,events:f}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:Fs(e,t),expansions:{}}),collapseItem:e=>({value:Bi(yf()),meta:{text:e}})}}),wf=Xm({name:"InlineView",configFields:[is("lazySink"),xi("onShow"),xi("onHide"),ws("onEscape"),Yu("inlineBehaviours",[Tu,qu,gc]),ks("fireDismissalEventInstead",[Cs("event",Er())]),ks("fireRepositionEventInstead",[Cs("event",Ar())]),Cs("getRelated",A.none),Cs("isExtraPart",T),Cs("eventOrder",A.none)],factory:(e,t)=>{const o=(t,o,n,s)=>{const r=e.lazySink(t).getOrDie();Tu.openWhileCloaked(t,o,(()=>tu.positionWithinBounds(r,t,n,s()))),qu.setValue(t,A.some({mode:"position",config:n,getBounds:s}))},n=(t,o,n,s)=>{const r=((e,t,o,n,s)=>{const r=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>cc(),onRtl:()=>dc()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return xf.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightOnOpen:n.menu.highlightOnOpen,fakeFocus:n.menu.fakeFocus,onEscape:()=>(Tu.close(t),e.onEscape.map((e=>e(t))),A.some(!0)),onExecute:()=>A.some(!0),onOpenMenu:(e,t)=>{tu.positionWithinBounds(r().getOrDie(),t,o,s())},onOpenSubmenu:(e,t,o,n)=>{const s=r().getOrDie();tu.position(s,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=r().getOrDie();tu.positionWithinBounds(a,t,o,s()),V(n,(e=>{const t=i(e.triggeringPath);tu.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,s);Tu.open(t,r),qu.setValue(t,A.some({mode:"menu",menu:r}))},s=t=>{Tu.isOpen(t)&&qu.getValue(t).each((o=>{switch(o.mode){case"menu":Tu.getState(t).each(xf.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();tu.positionWithinBounds(n,t,o.config,o.getBounds())}}))},r={setContent:(e,t)=>{Tu.setContent(e,t)},showAt:(e,t,n)=>{const s=A.none;o(e,t,n,s)},showWithinBounds:o,showMenuAt:(e,t,o)=>{n(e,t,o,A.none)},showMenuWithinBounds:n,hide:e=>{Tu.isOpen(e)&&(qu.setValue(e,A.none()),Tu.close(e))},getContent:e=>Tu.getState(e),reposition:s,isOpen:Tu.isOpen};return{uid:e.uid,dom:e.dom,behaviours:Ku(e.inlineBehaviours,[Tu.config({isPartOf:(t,o,n)=>Ml(o,n)||((t,o)=>e.getRelated(t).exists((e=>Ml(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),qu.config({store:{mode:"memory",initialValue:A.none()}}),gc.config({channels:{...Bu({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...Fu({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:s})}})]),eventOrder:e.eventOrder,apis:r}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithinBounds:(e,t,o,n,s)=>{e.showWithinBounds(t,o,n,s)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,s)=>{e.showMenuWithinBounds(t,o,n,s)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}});var Sf,kf,Cf=tinymce.util.Tools.resolve("tinymce.util.Delay"),Of=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),_f=tinymce.util.Tools.resolve("tinymce.EditorManager"),Tf=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(Sf||(Sf={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(kf||(kf={}));const Ef=e=>t=>t.options.get(e),Af=e=>t=>A.from(e(t)),Mf=e=>{const t=Tf.deviceType.isPhone(),o=Tf.deviceType.isTablet()||t,n=e.options.register,s=e=>r(e)||!1===e,a=e=>r(e)||h(e);n("skin",{processor:e=>r(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:Of.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("font_size_input_default_unit",{processor:"string",default:"pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>r(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||r(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),N(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:kf.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("ui_mode",{processor:"string",default:"combined"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:s,default:"#top"}),n("anchor_bottom",{processor:s,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("promotion",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!Tf.deviceType.isTouch()}),n("sidebar_show",{processor:"string"}),n("help_accessibility",{processor:"boolean",default:e.hasPlugin("help")}),n("default_font_stack",{processor:"string[]",default:[]})},Df=Ef("readonly"),Bf=Ef("height"),If=Ef("width"),Ff=Af(Ef("min_width")),Rf=Af(Ef("min_height")),Nf=Af(Ef("max_width")),zf=Af(Ef("max_height")),Lf=Af(Ef("style_formats")),Vf=Ef("style_formats_merge"),Hf=Ef("style_formats_autohide"),Pf=Ef("content_langs"),Uf=Ef("removed_menuitems"),Wf=Ef("toolbar_mode"),jf=Ef("toolbar_groups"),$f=Ef("toolbar_location"),Gf=Ef("fixed_toolbar_container"),qf=Ef("fixed_toolbar_container_target"),Yf=Ef("toolbar_persist"),Xf=Ef("toolbar_sticky_offset"),Kf=Ef("menubar"),Jf=Ef("toolbar"),Qf=Ef("file_picker_callback"),Zf=Ef("file_picker_validator_handler"),eb=Ef("font_size_input_default_unit"),tb=Ef("file_picker_types"),ob=Ef("typeahead_urls"),nb=Ef("anchor_top"),sb=Ef("anchor_bottom"),rb=Ef("draggable_modal"),ab=Ef("statusbar"),ib=Ef("elementpath"),lb=Ef("branding"),cb=Ef("resize"),db=Ef("paste_as_text"),ub=Ef("sidebar_show"),mb=Ef("promotion"),gb=Ef("help_accessibility"),pb=Ef("default_font_stack"),hb=e=>!1===e.options.get("skin"),fb=e=>!1!==e.options.get("menubar"),bb=e=>{const t=e.options.get("skin_url");if(hb(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return _f.baseURL+"/skins/ui/"+t}},vb=e=>A.from(e.options.get("skin_url")),yb=e=>e.options.get("line_height_formats").split(" "),xb=e=>{const t=Jf(e),o=r(t),n=l(t)&&t.length>0;return!Sb(e)&&(n||o||!0===t)},wb=e=>{const t=N(9,(t=>e.options.get("toolbar"+(t+1)))),o=P(t,r);return ke(o.length>0,o)},Sb=e=>wb(e).fold((()=>{const t=Jf(e);return f(t,r)&&t.length>0}),E),kb=e=>$f(e)===kf.bottom,Cb=e=>{var t;if(!e.inline)return A.none();const o=null!==(t=Gf(e))&&void 0!==t?t:"";if(o.length>0)return _l(wt(),o);const n=qf(e);return g(n)?A.some(ze(n)):A.none()},Ob=e=>e.inline&&Cb(e).isSome(),_b=e=>Cb(e).getOrThunk((()=>bt(ft(ze(e.getElement()))))),Tb=e=>e.inline&&!fb(e)&&!xb(e)&&!Sb(e),Eb=e=>(e.options.get("toolbar_sticky")||e.inline)&&!Ob(e)&&!Tb(e),Ab=e=>!Ob(e)&&"split"===e.options.get("ui_mode"),Mb=e=>{const t=e.options.get("menu");return le(t,(e=>({...e,items:e.items})))};var Db=Object.freeze({__proto__:null,get ToolbarMode(){return Sf},get ToolbarLocation(){return kf},register:Mf,getSkinUrl:bb,getSkinUrlOption:vb,isReadOnly:Df,isSkinDisabled:hb,getHeightOption:Bf,getWidthOption:If,getMinWidthOption:Ff,getMinHeightOption:Rf,getMaxWidthOption:Nf,getMaxHeightOption:zf,getUserStyleFormats:Lf,shouldMergeStyleFormats:Vf,shouldAutoHideStyleFormats:Hf,getLineHeightFormats:yb,getContentLanguages:Pf,getRemovedMenuItems:Uf,isMenubarEnabled:fb,isMultipleToolbars:Sb,isToolbarEnabled:xb,isToolbarPersist:Yf,getMultipleToolbarsOption:wb,getUiContainer:_b,useFixedContainer:Ob,isSplitUiMode:Ab,getToolbarMode:Wf,isDraggableModal:rb,isDistractionFree:Tb,isStickyToolbar:Eb,getStickyToolbarOffset:Xf,getToolbarLocation:$f,isToolbarLocationBottom:kb,getToolbarGroups:jf,getMenus:Mb,getMenubar:Kf,getToolbar:Jf,getFilePickerCallback:Qf,getFilePickerTypes:tb,useTypeaheadUrls:ob,getAnchorTop:nb,getAnchorBottom:sb,getFilePickerValidatorHandler:Zf,getFontSizeInputDefaultUnit:eb,useStatusBar:ab,useElementPath:ib,promotionEnabled:mb,useBranding:lb,getResize:cb,getPasteAsText:db,getSidebarShow:ub,useHelpAccessibility:gb,getDefaultFontStack:pb});const Bb=["visible","hidden","clip"],Ib=e=>Ae(e).length>0&&!F(Bb,e),Fb=e=>{if(je(e)){const t=Rt(e,"overflow-x"),o=Rt(e,"overflow-y");return Ib(t)||Ib(o)}return!1},Rb=(e,t)=>Ab(e)?(e=>{const t=_d(e,Fb),o=0===t.length?vt(e).map(yt).map((e=>_d(e,Fb))).getOr([]):t;return te(o).map((e=>({element:e,others:o.slice(1)})))})(t):A.none(),Nb=e=>{const t=[...L(e.others,Qo),tn()];return((e,t)=>W(t,((e,t)=>en(e,t)),e))(Qo(e.element),t)},zb=Xm({name:"Button",factory:e=>{const t=$h(e.action),o=e.dom.tag,n=t=>fe(e.dom,"attributes").bind((e=>fe(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:Qu(e.buttonBehaviours,[Rh.config({}),xh.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:e.role.getOr(n("role").getOr("button"))}},eventOrder:e.eventOrder}},configFields:[Cs("uid",void 0),is("dom"),Cs("components",[]),Ju("buttonBehaviours",[Rh,xh]),fs("action"),fs("role"),Cs("eventOrder",{})]}),Lb=e=>{const t=Fe(e),o=lt(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return W(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),s=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),r=0===o.length?{}:{innerHtml:si(t)};return{tag:Ue(t),classes:s,attributes:n,...r}},Vb=e=>{const t=(e=>void 0!==e.uid)(e)&&ve(e,"uid")?e.uid:Vi("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}};var Hb=Object.freeze({__proto__:null,exhibit:(e,t)=>aa({attributes:Rs([{key:t.tabAttr,value:"true"}])})}),Pb=[Cs("tabAttr","data-alloy-tabstop")];const Ub=pa({fields:Pb,name:"tabstopping",active:Hb}),Wb=Bi("tooltip.exclusive"),jb=Bi("tooltip.show"),$b=Bi("tooltip.hide"),Gb=Bi("tooltip.immediateHide"),qb=Bi("tooltip.immediateShow"),Yb=(e,t,o)=>{e.getSystem().broadcastOn([Wb],{})};var Xb=Object.freeze({__proto__:null,hideAllExclusive:Yb,setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&Ah.set(e,n)}))}}),Kb=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{n.getSystem().isConnected()&&(uu(n),e.onHide(o,n),t.clearTooltip())})),t.clearTimer()},n=o=>{if(!t.isShowing()){Yb(o);const n=e.lazySink(o).getOrDie(),s=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:Wr("normal"===e.mode?[Gr(Js(),(e=>{zr(o,jb)})),Gr(Xs(),(e=>{zr(o,$b)}))]:[]),behaviours:ma([Ah.config({})])});t.setTooltip(s),lu(n,s),e.onShow(o,s),tu.position(n,s,{anchor:e.anchor(o)})}},s=o=>{t.getTooltip().each((t=>{const n=e.lazySink(o).getOrDie();tu.position(n,t,{anchor:e.anchor(o)})}))};return Wr(G([[Gr(jb,(o=>{t.resetTimer((()=>{n(o)}),e.delayForShow())})),Gr($b,(n=>{t.resetTimer((()=>{o(n)}),e.delayForHide())})),Gr(qb,(e=>{t.resetTimer((()=>{n(e)}),0)})),Gr(Gb,(e=>{t.resetTimer((()=>{o(e)}),0)})),Gr(pr(),((e,t)=>{const n=t;n.universal||F(n.channels,Wb)&&o(e)})),ta((e=>{o(e)}))],(()=>{switch(e.mode){case"normal":return[Gr(Qs(),(e=>{zr(e,qb)})),Gr(mr(),(e=>{zr(e,Gb)})),Gr(Js(),(e=>{zr(e,jb)})),Gr(Xs(),(e=>{zr(e,$b)}))];case"follow-highlight":return[Gr(Rr(),((e,t)=>{zr(e,jb)})),Gr(Nr(),(e=>{zr(e,$b)}))];case"children-normal":return[Gr(Qs(),((o,n)=>{yc(o.element).each((r=>{Ke(n.event.target,"[data-mce-tooltip]")&&t.getTooltip().fold((()=>{zr(o,qb)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),Gr(mr(),(e=>{yc(e.element).fold((()=>{zr(e,Gb)}),b)})),Gr(Js(),(o=>{_l(o.element,"[data-mce-tooltip]:hover").each((n=>{t.getTooltip().fold((()=>{zr(o,jb)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),Gr(Xs(),(e=>{_l(e.element,"[data-mce-tooltip]:hover").fold((()=>{zr(e,$b)}),b)}))];default:return[Gr(Qs(),((o,n)=>{yc(o.element).each((r=>{Ke(n.event.target,"[data-mce-tooltip]")&&t.getTooltip().fold((()=>{zr(o,qb)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),Gr(mr(),(e=>{yc(e.element).fold((()=>{zr(e,Gb)}),b)}))]}})()]))}}),Jb=[is("lazySink"),is("tooltipDom"),Cs("exclusive",!0),Cs("tooltipComponents",[]),Ms("delayForShow",x(300)),Ms("delayForHide",x(300)),Es("mode","normal",["normal","follow-highlight","children-keyboard-focus","children-normal"]),Cs("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:x([oc,tc,Jl,Zl,Ql,ec]),onRtl:x([oc,tc,Jl,Zl,Ql,ec])},bubble:Gc(0,-2,{})}))),xi("onHide"),xi("onShow")],Qb=Object.freeze({__proto__:null,init:()=>{const e=rn(),t=rn(),o=()=>{e.on(clearTimeout)},n=x("not-implemented");return ua({getTooltip:t.get,isShowing:t.isSet,setTooltip:t.set,clearTooltip:t.clear,clearTimer:o,resetTimer:(t,n)=>{o(),e.set(setTimeout(t,n))},readState:n})}});const Zb=pa({fields:Jb,name:"tooltipping",active:Kb,state:Qb,apis:Xb}),{entries:ev,setPrototypeOf:tv,isFrozen:ov,getPrototypeOf:nv,getOwnPropertyDescriptor:sv}=Object;let{freeze:rv,seal:av,create:iv}=Object,{apply:lv,construct:cv}="undefined"!=typeof Reflect&&Reflect;lv||(lv=function(e,t,o){return e.apply(t,o)}),rv||(rv=function(e){return e}),av||(av=function(e){return e}),cv||(cv=function(e,t){return new e(...t)});const dv=Sv(Array.prototype.forEach),uv=Sv(Array.prototype.pop),mv=Sv(Array.prototype.push),gv=Sv(String.prototype.toLowerCase),pv=Sv(String.prototype.toString),hv=Sv(String.prototype.match),fv=Sv(String.prototype.replace),bv=Sv(String.prototype.indexOf),vv=Sv(String.prototype.trim),yv=Sv(RegExp.prototype.test),xv=(wv=TypeError,function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return cv(wv,t)});var wv;function Sv(e){return function(t){for(var o=arguments.length,n=new Array(o>1?o-1:0),s=1;s<o;s++)n[s-1]=arguments[s];return lv(e,t,n)}}function kv(e,t,o){var n;o=null!==(n=o)&&void 0!==n?n:gv,tv&&tv(e,null);let s=t.length;for(;s--;){let n=t[s];if("string"==typeof n){const e=o(n);e!==n&&(ov(t)||(t[s]=e),n=e)}e[n]=!0}return e}function Cv(e){const t=iv(null);for(const[o,n]of ev(e))t[o]=n;return t}function Ov(e,t){for(;null!==e;){const o=sv(e,t);if(o){if(o.get)return Sv(o.get);if("function"==typeof o.value)return Sv(o.value)}e=nv(e)}return function(e){return console.warn("fallback value for",e),null}}const _v=rv(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Tv=rv(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Ev=rv(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Av=rv(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Mv=rv(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Dv=rv(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Bv=rv(["#text"]),Iv=rv(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),Fv=rv(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Rv=rv(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Nv=rv(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),zv=av(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Lv=av(/<%[\w\W]*|[\w\W]*%>/gm),Vv=av(/\${[\w\W]*}/gm),Hv=av(/^data-[\-\w.\u00B7-\uFFFF]/),Pv=av(/^aria-[\-\w]+$/),Uv=av(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Wv=av(/^(?:\w+script|data):/i),jv=av(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),$v=av(/^html$/i);var Gv=Object.freeze({__proto__:null,MUSTACHE_EXPR:zv,ERB_EXPR:Lv,TMPLIT_EXPR:Vv,DATA_ATTR:Hv,ARIA_ATTR:Pv,IS_ALLOWED_URI:Uv,IS_SCRIPT_OR_DATA:Wv,ATTR_WHITESPACE:jv,DOCTYPE_NAME:$v});const qv=()=>"undefined"==typeof window?null:window;var Yv=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:qv();const o=t=>e(t);if(o.version="3.0.5",o.removed=[],!t||!t.document||9!==t.document.nodeType)return o.isSupported=!1,o;const n=t.document,s=n.currentScript;let{document:r}=t;const{DocumentFragment:a,HTMLTemplateElement:i,Node:l,Element:c,NodeFilter:d,NamedNodeMap:u=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:m,DOMParser:g,trustedTypes:p}=t,h=c.prototype,f=Ov(h,"cloneNode"),b=Ov(h,"nextSibling"),v=Ov(h,"childNodes"),y=Ov(h,"parentNode");if("function"==typeof i){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let x,w="";const{implementation:S,createNodeIterator:k,createDocumentFragment:C,getElementsByTagName:O}=r,{importNode:_}=n;let T={};o.isSupported="function"==typeof ev&&"function"==typeof y&&S&&void 0!==S.createHTMLDocument;const{MUSTACHE_EXPR:E,ERB_EXPR:A,TMPLIT_EXPR:M,DATA_ATTR:D,ARIA_ATTR:B,IS_SCRIPT_OR_DATA:I,ATTR_WHITESPACE:F}=Gv;let{IS_ALLOWED_URI:R}=Gv,N=null;const z=kv({},[..._v,...Tv,...Ev,...Mv,...Bv]);let L=null;const V=kv({},[...Iv,...Fv,...Rv,...Nv]);let H=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),P=null,U=null,W=!0,j=!0,$=!1,G=!0,q=!1,Y=!1,X=!1,K=!1,J=!1,Q=!1,Z=!1,ee=!0,te=!1,oe=!0,ne=!1,se={},re=null;const ae=kv({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ie=null;const le=kv({},["audio","video","img","source","image","track"]);let ce=null;const de=kv({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ue="http://www.w3.org/1998/Math/MathML",me="http://www.w3.org/2000/svg",ge="http://www.w3.org/1999/xhtml";let pe=ge,he=!1,fe=null;const be=kv({},[ue,me,ge],pv);let ve;const ye=["application/xhtml+xml","text/html"];let xe,we=null;const Se=r.createElement("form"),ke=function(e){return e instanceof RegExp||e instanceof Function},Ce=function(e){if(!we||we!==e){if(e&&"object"==typeof e||(e={}),e=Cv(e),ve=ve=-1===ye.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,xe="application/xhtml+xml"===ve?pv:gv,N="ALLOWED_TAGS"in e?kv({},e.ALLOWED_TAGS,xe):z,L="ALLOWED_ATTR"in e?kv({},e.ALLOWED_ATTR,xe):V,fe="ALLOWED_NAMESPACES"in e?kv({},e.ALLOWED_NAMESPACES,pv):be,ce="ADD_URI_SAFE_ATTR"in e?kv(Cv(de),e.ADD_URI_SAFE_ATTR,xe):de,ie="ADD_DATA_URI_TAGS"in e?kv(Cv(le),e.ADD_DATA_URI_TAGS,xe):le,re="FORBID_CONTENTS"in e?kv({},e.FORBID_CONTENTS,xe):ae,P="FORBID_TAGS"in e?kv({},e.FORBID_TAGS,xe):{},U="FORBID_ATTR"in e?kv({},e.FORBID_ATTR,xe):{},se="USE_PROFILES"in e&&e.USE_PROFILES,W=!1!==e.ALLOW_ARIA_ATTR,j=!1!==e.ALLOW_DATA_ATTR,$=e.ALLOW_UNKNOWN_PROTOCOLS||!1,G=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,q=e.SAFE_FOR_TEMPLATES||!1,Y=e.WHOLE_DOCUMENT||!1,J=e.RETURN_DOM||!1,Q=e.RETURN_DOM_FRAGMENT||!1,Z=e.RETURN_TRUSTED_TYPE||!1,K=e.FORCE_BODY||!1,ee=!1!==e.SANITIZE_DOM,te=e.SANITIZE_NAMED_PROPS||!1,oe=!1!==e.KEEP_CONTENT,ne=e.IN_PLACE||!1,R=e.ALLOWED_URI_REGEXP||Uv,pe=e.NAMESPACE||ge,H=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ke(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(H.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ke(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(H.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(H.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),q&&(j=!1),Q&&(J=!0),se&&(N=kv({},[...Bv]),L=[],!0===se.html&&(kv(N,_v),kv(L,Iv)),!0===se.svg&&(kv(N,Tv),kv(L,Fv),kv(L,Nv)),!0===se.svgFilters&&(kv(N,Ev),kv(L,Fv),kv(L,Nv)),!0===se.mathMl&&(kv(N,Mv),kv(L,Rv),kv(L,Nv))),e.ADD_TAGS&&(N===z&&(N=Cv(N)),kv(N,e.ADD_TAGS,xe)),e.ADD_ATTR&&(L===V&&(L=Cv(L)),kv(L,e.ADD_ATTR,xe)),e.ADD_URI_SAFE_ATTR&&kv(ce,e.ADD_URI_SAFE_ATTR,xe),e.FORBID_CONTENTS&&(re===ae&&(re=Cv(re)),kv(re,e.FORBID_CONTENTS,xe)),oe&&(N["#text"]=!0),Y&&kv(N,["html","head","body"]),N.table&&(kv(N,["tbody"]),delete P.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw xv('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw xv('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');x=e.TRUSTED_TYPES_POLICY,w=x.createHTML("")}else void 0===x&&(x=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let o=null;const n="data-tt-policy-suffix";t&&t.hasAttribute(n)&&(o=t.getAttribute(n));const s="dompurify"+(o?"#"+o:"");try{return e.createPolicy(s,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+s+" could not be created."),null}}(p,s)),null!==x&&"string"==typeof w&&(w=x.createHTML(""));rv&&rv(e),we=e}},Oe=kv({},["mi","mo","mn","ms","mtext"]),_e=kv({},["foreignobject","desc","title","annotation-xml"]),Te=kv({},["title","style","font","a","script"]),Ee=kv({},Tv);kv(Ee,Ev),kv(Ee,Av);const Ae=kv({},Mv);kv(Ae,Dv);const Me=function(e){mv(o.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){e.remove()}},De=function(e,t){try{mv(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){mv(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!L[e])if(J||Q)try{Me(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Be=function(e){let t,o;if(K)e="<remove></remove>"+e;else{const t=hv(e,/^[\r\n\t ]+/);o=t&&t[0]}"application/xhtml+xml"===ve&&pe===ge&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const n=x?x.createHTML(e):e;if(pe===ge)try{t=(new g).parseFromString(n,ve)}catch(e){}if(!t||!t.documentElement){t=S.createDocument(pe,"template",null);try{t.documentElement.innerHTML=he?w:n}catch(e){}}const s=t.body||t.documentElement;return e&&o&&s.insertBefore(r.createTextNode(o),s.childNodes[0]||null),pe===ge?O.call(t,Y?"html":"body")[0]:Y?t.documentElement:s},Ie=function(e){return k.call(e.ownerDocument||e,e,d.SHOW_ELEMENT|d.SHOW_COMMENT|d.SHOW_TEXT,null,!1)},Fe=function(e){return"object"==typeof l?e instanceof l:e&&"object"==typeof e&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName},Re=function(e,t,n){T[e]&&dv(T[e],(e=>{e.call(o,t,n,we)}))},Ne=function(e){let t;if(Re("beforeSanitizeElements",e,null),(n=e)instanceof m&&("string"!=typeof n.nodeName||"string"!=typeof n.textContent||"function"!=typeof n.removeChild||!(n.attributes instanceof u)||"function"!=typeof n.removeAttribute||"function"!=typeof n.setAttribute||"string"!=typeof n.namespaceURI||"function"!=typeof n.insertBefore||"function"!=typeof n.hasChildNodes))return Me(e),!0;var n;const s=xe(e.nodeName);if(Re("uponSanitizeElement",e,{tagName:s,allowedTags:N}),e.hasChildNodes()&&!Fe(e.firstElementChild)&&(!Fe(e.content)||!Fe(e.content.firstElementChild))&&yv(/<[/\w]/g,e.innerHTML)&&yv(/<[/\w]/g,e.textContent))return Me(e),!0;if(!N[s]||P[s]){if(!P[s]&&Le(s)){if(H.tagNameCheck instanceof RegExp&&yv(H.tagNameCheck,s))return!1;if(H.tagNameCheck instanceof Function&&H.tagNameCheck(s))return!1}if(oe&&!re[s]){const t=y(e)||e.parentNode,o=v(e)||e.childNodes;if(o&&t)for(let n=o.length-1;n>=0;--n)t.insertBefore(f(o[n],!0),b(e))}return Me(e),!0}return e instanceof c&&!function(e){let t=y(e);t&&t.tagName||(t={namespaceURI:pe,tagName:"template"});const o=gv(e.tagName),n=gv(t.tagName);return!!fe[e.namespaceURI]&&(e.namespaceURI===me?t.namespaceURI===ge?"svg"===o:t.namespaceURI===ue?"svg"===o&&("annotation-xml"===n||Oe[n]):Boolean(Ee[o]):e.namespaceURI===ue?t.namespaceURI===ge?"math"===o:t.namespaceURI===me?"math"===o&&_e[n]:Boolean(Ae[o]):e.namespaceURI===ge?!(t.namespaceURI===me&&!_e[n])&&!(t.namespaceURI===ue&&!Oe[n])&&!Ae[o]&&(Te[o]||!Ee[o]):!("application/xhtml+xml"!==ve||!fe[e.namespaceURI]))}(e)?(Me(e),!0):"noscript"!==s&&"noembed"!==s&&"noframes"!==s||!yv(/<\/no(script|embed|frames)/i,e.innerHTML)?(q&&3===e.nodeType&&(t=e.textContent,t=fv(t,E," "),t=fv(t,A," "),t=fv(t,M," "),e.textContent!==t&&(mv(o.removed,{element:e.cloneNode()}),e.textContent=t)),Re("afterSanitizeElements",e,null),!1):(Me(e),!0)},ze=function(e,t,o){if(ee&&("id"===t||"name"===t)&&(o in r||o in Se))return!1;if(j&&!U[t]&&yv(D,t));else if(W&&yv(B,t));else if(!L[t]||U[t]){if(!(Le(e)&&(H.tagNameCheck instanceof RegExp&&yv(H.tagNameCheck,e)||H.tagNameCheck instanceof Function&&H.tagNameCheck(e))&&(H.attributeNameCheck instanceof RegExp&&yv(H.attributeNameCheck,t)||H.attributeNameCheck instanceof Function&&H.attributeNameCheck(t))||"is"===t&&H.allowCustomizedBuiltInElements&&(H.tagNameCheck instanceof RegExp&&yv(H.tagNameCheck,o)||H.tagNameCheck instanceof Function&&H.tagNameCheck(o))))return!1}else if(ce[t]);else if(yv(R,fv(o,F,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==bv(o,"data:")||!ie[e])if($&&!yv(I,fv(o,F,"")));else if(o)return!1;return!0},Le=function(e){return e.indexOf("-")>0},Ve=function(e){let t,o,n,s;Re("beforeSanitizeAttributes",e,null);const{attributes:r}=e;if(!r)return;const a={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:L};for(s=r.length;s--;){t=r[s];const{name:i,namespaceURI:l}=t;o="value"===i?t.value:vv(t.value);const c=o;if(n=xe(i),a.attrName=n,a.attrValue=o,a.keepAttr=!0,a.forceKeepAttr=void 0,Re("uponSanitizeAttribute",e,a),o=a.attrValue,a.forceKeepAttr)continue;if(!a.keepAttr){De(i,e);continue}if(!G&&yv(/\/>/i,o)){De(i,e);continue}q&&(o=fv(o,E," "),o=fv(o,A," "),o=fv(o,M," "));const d=xe(e.nodeName);if(ze(d,n,o)){if(!te||"id"!==n&&"name"!==n||(De(i,e),o="user-content-"+o),x&&"object"==typeof p&&"function"==typeof p.getAttributeType)if(l);else switch(p.getAttributeType(d,n)){case"TrustedHTML":o=x.createHTML(o);break;case"TrustedScriptURL":o=x.createScriptURL(o)}if(o!==c)try{l?e.setAttributeNS(l,i,o):e.setAttribute(i,o)}catch(t){De(i,e)}}else De(i,e)}Re("afterSanitizeAttributes",e,null)},He=function e(t){let o;const n=Ie(t);for(Re("beforeSanitizeShadowDOM",t,null);o=n.nextNode();)Re("uponSanitizeShadowNode",o,null),Ne(o)||(o.content instanceof a&&e(o.content),Ve(o));Re("afterSanitizeShadowDOM",t,null)};return o.sanitize=function(e){let t,s,r,i,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(he=!e,he&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Fe(e)){if("function"!=typeof e.toString)throw xv("toString is not a function");if("string"!=typeof(e=e.toString()))throw xv("dirty is not a string, aborting")}if(!o.isSupported)return e;if(X||Ce(c),o.removed=[],"string"==typeof e&&(ne=!1),ne){if(e.nodeName){const t=xe(e.nodeName);if(!N[t]||P[t])throw xv("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof l)t=Be("\x3c!----\x3e"),s=t.ownerDocument.importNode(e,!0),1===s.nodeType&&"BODY"===s.nodeName||"HTML"===s.nodeName?t=s:t.appendChild(s);else{if(!J&&!q&&!Y&&-1===e.indexOf("<"))return x&&Z?x.createHTML(e):e;if(t=Be(e),!t)return J?null:Z?w:""}t&&K&&Me(t.firstChild);const d=Ie(ne?e:t);for(;r=d.nextNode();)Ne(r)||(r.content instanceof a&&He(r.content),Ve(r));if(ne)return e;if(J){if(Q)for(i=C.call(t.ownerDocument);t.firstChild;)i.appendChild(t.firstChild);else i=t;return(L.shadowroot||L.shadowrootmode)&&(i=_.call(n,i,!0)),i}let u=Y?t.outerHTML:t.innerHTML;return Y&&N["!doctype"]&&t.ownerDocument&&t.ownerDocument.doctype&&t.ownerDocument.doctype.name&&yv($v,t.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+t.ownerDocument.doctype.name+">\n"+u),q&&(u=fv(u,E," "),u=fv(u,A," "),u=fv(u,M," ")),x&&Z?x.createHTML(u):u},o.setConfig=function(e){Ce(e),X=!0},o.clearConfig=function(){we=null,X=!1},o.isValidAttribute=function(e,t,o){we||Ce({});const n=xe(e),s=xe(t);return ze(n,s,o)},o.addHook=function(e,t){"function"==typeof t&&(T[e]=T[e]||[],mv(T[e],t))},o.removeHook=function(e){if(T[e])return uv(T[e])},o.removeHooks=function(e){T[e]&&(T[e]=[])},o.removeAllHooks=function(){T={}},o}();const Xv=e=>Yv().sanitize(e);var Kv=tinymce.util.Tools.resolve("tinymce.util.I18n");const Jv={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},Qv="temporary-placeholder",Zv=e=>()=>fe(e,Qv).getOr("!not found!"),ey=(e,t)=>{const o=e.toLowerCase();if(Kv.isRtl()){const e=((e,t)=>Ee(e,t)?e:((e,t)=>e+t)(e,t))(o,"-rtl");return be(t,e)?e:o}return o},ty=(e,t)=>fe(t,ey(e,t)),oy=(e,t)=>{const o=t();return ty(e,o).getOrThunk(Zv(o))},ny=()=>Mh("add-focusable",[ea((e=>{Ol(e.element,"svg").each((e=>Ct(e,"focusable","false")))}))]),sy=(e,t,o,n)=>{var s,r;const a=(e=>!!Kv.isRtl()&&be(Jv,e))(t)?["tox-icon--flip"]:[],i=fe(o,ey(t,o)).or(n).getOrThunk(Zv(o));return{dom:{tag:e.tag,attributes:null!==(s=e.attributes)&&void 0!==s?s:{},classes:e.classes.concat(a),innerHtml:i},behaviours:ma([...null!==(r=e.behaviours)&&void 0!==r?r:[],ny()])}},ry=(e,t,o,n=A.none())=>sy(t,e,o(),n),ay={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},iy=Xm({name:"Notification",factory:e=>{const t=Bi("notification-text"),o=Vb({dom:Lb(`<p id=${t}>${Xv(e.backstageProvider.translate(e.text))}</p>`),behaviours:ma([Ah.config({})])}),n=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),s=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),r=Vb({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[n(0)]},s(0)],behaviours:ma([Ah.config({})])}),a={updateProgress:(e,t)=>{e.getSystem().isConnected()&&r.getOpt(e).each((e=>{Ah.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[n(t)]},s(t)])}))},updateText:(e,t)=>{if(e.getSystem().isConnected()){const n=o.get(e);Ah.set(n,[ul(t)])}}},i=G([e.icon.toArray(),e.level.toArray(),e.level.bind((e=>A.from(ay[e]))).toArray()]),l=Vb(zb.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"],attributes:{"aria-label":e.backstageProvider.translate("Close")}},components:[ry("close",{tag:"span",classes:["tox-icon"]},e.iconProvider)],buttonBehaviours:ma([Ub.config({}),Zb.config({...e.backstageProvider.tooltips.getConfig({tooltipText:e.backstageProvider.translate("Close")})})]),action:t=>{e.onAction(t)}})),c=((e,t,o)=>{const n=o(),s=j(e,(e=>be(n,ey(e,n))));return sy({tag:"div",classes:["tox-notification__icon"]},s.getOr(Qv),n,A.none())})(i,0,e.iconProvider),d=[c,{dom:{tag:"div",classes:["tox-notification__body"]},components:[o.asSpec()],behaviours:ma([Ah.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert","aria-labelledby":t},classes:e.level.map((e=>["tox-notification","tox-notification--in",`tox-notification--${e}`])).getOr(["tox-notification","tox-notification--in"])},behaviours:ma([Ub.config({}),Rh.config({}),xh.config({mode:"special",onEscape:t=>(e.onAction(t),A.some(!0))})]),components:d.concat(e.progress?[r.asSpec()]:[]).concat([l.asSpec()]),apis:a}},configFields:[fs("level"),is("progress"),fs("icon"),is("onAction"),is("text"),is("iconProvider"),is("backstageProvider")],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var ly=(e,t,o,n)=>{const s=t.backstage.shared,r=()=>{const t=Qo(ze(e.getContentAreaContainer()));return A.some(t)},a=e=>{r().each((t=>{V(e,(e=>{Qt(e.element)>t.width&&Bt(e.element,"width",t.width+"px")}))}))};return{open:(t,i,l)=>{const c=()=>{n.on((t=>{i();const o=l();(e=>{Ah.remove(e,d),u()})(t),((t,o)=>{0===lt(t.element).length?((t,o)=>{wf.hide(t),n.clear(),o&&e.focus()})(t,o):((e,t)=>{t&&xh.focusIn(e)})(t,o)})(t,o)}))},d=hl(iy.sketch({text:t.text,level:F(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:t.icon,onAction:c,iconProvider:s.providers.icons,backstageProvider:s.providers}));if(n.isSet()){const e=fl(d);n.on((t=>{Ah.append(t,e),wf.reposition(t),Ei.refresh(t),a(t.components())}))}else{const t=hl(wf.sketch({dom:{tag:"div",classes:["tox-notifications-container"],attributes:{"aria-label":"Notifications",role:"region"}},lazySink:s.getSink,fireDismissalEventInstead:{},...s.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}},inlineBehaviours:ma([xh.config({mode:"cyclic",selector:".tox-notification, .tox-notification a, .tox-notification button"}),Ah.config({}),...Eb(e)&&!s.header.isPositionedAtTop()?[]:[Ei.config({contextual:{lazyContext:()=>A.some(Qo(ze(e.getContentAreaContainer()))),fadeInClass:"tox-notification-container-dock-fadein",fadeOutClass:"tox-notification-container-dock-fadeout",transitionClass:"tox-notification-container-dock-transition"},modes:["top"],lazyViewport:t=>Rb(e,t.element).map((e=>({bounds:Nb(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Xt(e.element).top})}))).getOrThunk((()=>({bounds:tn(),optScrollEnv:A.none()})))})]])})),a=fl(d),i={maxHeightFunction:Uc()},l={...s.anchors.banner(),overrides:i};n.set(t),o.add(t),wf.showWithinBounds(t,a,{anchor:l},r)}h(t.timeout)&&t.timeout>0&&Cf.setEditorTimeout(e,(()=>{c()}),t.timeout);const u=()=>{n.on((e=>{wf.reposition(e),Ei.refresh(e),a(e.components())}))};return{close:c,reposition:u,text:e=>{iy.updateText(d,e)},settings:t,getEl:()=>d.element.dom,progressBar:{value:e=>{iy.updateProgress(d,e)}}}},close:e=>{e.close()},getArgs:e=>e.settings}};var cy;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(cy||(cy={}));var dy=cy;const uy="tox-menu-nav__js",my="tox-collection__item",gy="tox-swatch",py={normal:uy,color:gy},hy="tox-collection__item--enabled",fy="tox-collection__item-icon",by="tox-collection__item-label",vy="tox-collection__item-caret",yy="tox-collection__item--active",xy="tox-collection__item-container",wy="tox-collection__item-container--row",Sy=e=>fe(py,e).getOr(uy),ky=e=>"color"===e?"tox-swatches":"tox-menu",Cy=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:ky(e),tieredMenu:"tox-tiered-menu"}),Oy=e=>{const t=Cy(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:Sy(e)}},_y=(e,t,o)=>{const n=Cy(o);return{tag:"div",classes:G([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},Ty=[uf.parts.items({})],Ey=(e,t,o)=>{const n=Cy(o);return{dom:{tag:"div",classes:G([[n.tieredMenu]])},markers:Oy(o)}},Ay=x([fs("data"),Cs("inputAttributes",{}),Cs("inputStyles",{}),Cs("tag","input"),Cs("inputClasses",[]),xi("onSetValue"),Cs("styles",{}),Cs("eventOrder",{}),Yu("inputBehaviours",[qu,Rh]),Cs("selectOnFocus",!0)]),My=e=>ma([Rh.config({onFocus:e.selectOnFocus?e=>{const t=e.element,o=ol(t);t.dom.setSelectionRange(0,o.length)}:b})]),Dy=e=>({...My(e),...Ku(e.inputBehaviours,[qu.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:e=>ol(e.element),setValue:(e,t)=>{ol(e.element)!==t&&nl(e.element,t)}},onSetValue:e.onSetValue})])}),By=e=>({tag:e.tag,attributes:{type:"text",...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),Iy=Xm({name:"Input",configFields:Ay(),factory:(e,t)=>({uid:e.uid,dom:By(e),components:[],behaviours:Dy(e),eventOrder:e.eventOrder})}),Fy=Bi("refetch-trigger-event"),Ry=Bi("redirect-menu-item-interaction"),Ny="tox-menu__searcher",zy=e=>_l(e.element,`.${Ny}`).bind((t=>e.getSystem().getByDom(t).toOptional())),Ly=zy,Vy=e=>({fetchPattern:qu.getValue(e),selectionStart:e.element.dom.selectionStart,selectionEnd:e.element.dom.selectionEnd}),Hy=e=>{const t=(e,t)=>(t.cut(),A.none()),o=(e,t)=>{const o={interactionEvent:t.event,eventType:t.event.raw.type};return Lr(e,Ry,o),A.some(!0)},n="searcher-events";return{dom:{tag:"div",classes:[my]},components:[Iy.sketch({inputClasses:[Ny,"tox-textfield"],inputAttributes:{...e.placeholder.map((t=>({placeholder:e.i18n(t)}))).getOr({}),type:"search","aria-autocomplete":"list"},inputBehaviours:ma([Mh(n,[Gr(or(),(e=>{zr(e,Fy)})),Gr(er(),((e,t)=>{"Escape"===t.event.raw.key&&t.stop()}))]),xh.config({mode:"special",onLeft:t,onRight:t,onSpace:t,onEnter:o,onEscape:o,onUp:o,onDown:o})]),eventOrder:{keydown:[n,xh.name()]}})]}},Py="tox-collection--results__js",Uy=e=>{var t;return e.dom?{...e,dom:{...e.dom,attributes:{...null!==(t=e.dom.attributes)&&void 0!==t?t:{},id:Bi("aria-item-search-result-id"),"aria-selected":"false"}}}:e},Wy=(e,t)=>o=>{const n=z(o,t);return L(n,(t=>({dom:e,components:t})))},jy=(e,t)=>{const o=[];let n=[];return V(e,((e,s)=>{t(e,s)?(n.length>0&&o.push(n),n=[],(be(e.dom,"innerHtml")||e.components&&e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),L(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},$y=(e,t,o)=>uf.parts.items({preprocess:n=>{const s=L(n,o);return"auto"!==e&&e>1?Wy({tag:"div",classes:["tox-collection__group"]},e)(s):jy(s,((e,o)=>"separator"===t[o].type))}}),Gy=(e,t,o=!0)=>({dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[$y(e,t,w)]}),qy=e=>R(e,(e=>"icon"in e&&void 0!==e.icon)),Yy=e=>(console.error(ts(e)),console.log(e),A.none()),Xy=(e,t,o,n,s)=>{const r=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[uf.parts.items({preprocess:e=>jy(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:r.dom,components:r.components,items:o}},Ky=(e,t,o,n,s)=>{if("color"===s.menuType){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[uf.parts.items({preprocess:"auto"!==e?Wy({tag:"div",classes:["tox-swatches__row"]},e):w})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType&&"auto"===n){const t=Gy(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType||"searchable"===s.menuType){const t="searchable"!==s.menuType?Gy(n,o):"search-with-field"===s.searchMode.searchMode?((e,t,o)=>{const n=Bi("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Hy({i18n:Kv.translate,placeholder:o.placeholder}),{dom:{tag:"div",classes:[...1===e?["tox-collection--list"]:["tox-collection--grid"],Py],attributes:{id:n}},components:[$y(e,t,Uy)]}]}})(n,o,s.searchMode):((e,t,o=!0)=>{const n=Bi("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection",Py].concat(1===e?["tox-collection--list"]:["tox-collection--grid"]),attributes:{id:n}},components:[$y(e,t,Uy)]}})(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===s.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[uf.parts.items({preprocess:Wy({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:_y(t,n,s.menuType),components:Ty,items:o}},Jy=ds("type"),Qy=ds("name"),Zy=ds("label"),ex=ds("text"),tx=ds("title"),ox=ds("icon"),nx=ds("value"),sx=ms("fetch"),rx=ms("getSubmenuItems"),ax=ms("onAction"),ix=ms("onItemAction"),lx=Ms("onSetup",(()=>b)),cx=ys("name"),dx=ys("text"),ux=ys("role"),mx=ys("icon"),gx=ys("tooltip"),px=ys("label"),hx=ys("shortcut"),fx=ws("select"),bx=As("active",!1),vx=As("borderless",!1),yx=As("enabled",!0),xx=As("primary",!1),wx=e=>Cs("columns",e),Sx=Cs("meta",{}),kx=Ms("onAction",b),Cx=e=>Ts("type",e),Ox=e=>ss("name","name",kn((()=>Bi(`${e}-name`))),jn),_x=Nn([Jy,dx]),Tx=Nn([Cx("autocompleteitem"),bx,yx,Sx,nx,dx,mx]),Ex=[yx,gx,mx,dx,lx],Ax=Nn([Jy,ax,hx].concat(Ex)),Mx=e=>Qn("toolbarbutton",Ax,e),Dx=[bx].concat(Ex),Bx=Nn(Dx.concat([Jy,ax,hx])),Ix=e=>Qn("ToggleButton",Bx,e),Fx=[Ms("predicate",T),Es("scope","node",["node","editor"]),Es("position","selection",["node","selection","line"])],Rx=Ex.concat([Cx("contextformbutton"),xx,ax,rs("original",w)]),Nx=Dx.concat([Cx("contextformbutton"),xx,ax,rs("original",w)]),zx=Ex.concat([Cx("contextformbutton")]),Lx=Dx.concat([Cx("contextformtogglebutton")]),Vx=os("type",{contextformbutton:Rx,contextformtogglebutton:Nx}),Hx=Nn([Cx("contextform"),Ms("initValue",x("")),px,hs("commands",Vx),bs("launch",os("type",{contextformbutton:zx,contextformtogglebutton:Lx}))].concat(Fx)),Px=Nn([Cx("contexttoolbar"),ds("items")].concat(Fx)),Ux=[Jy,ds("src"),ys("alt"),Ds("classes",[],jn)],Wx=Nn(Ux),jx=[Jy,ex,cx,Ds("classes",["tox-collection__item-label"],jn)],$x=Nn(jx),Gx=In((()=>Xn("type",{cardimage:Wx,cardtext:$x,cardcontainer:qx}))),qx=Nn([Jy,Ts("direction","horizontal"),Ts("align","left"),Ts("valign","middle"),hs("items",Gx)]),Yx=[yx,dx,ux,hx,("menuitem",ss("value","value",kn((()=>Bi("menuitem-value"))),Pn())),Sx];const Xx=Nn([Jy,px,hs("items",Gx),lx,kx].concat(Yx)),Kx=Nn([Jy,bx,mx].concat(Yx)),Jx=[Jy,ds("fancytype"),kx],Qx=[Cs("initData",{})].concat(Jx),Zx=[ws("select"),Bs("initData",{},[As("allowCustomColors",!0),Ts("storageKey","default"),Ss("colors",Pn())])].concat(Jx),ew=os("fancytype",{inserttable:Qx,colorswatch:Zx}),tw=Nn([Jy,lx,kx,mx].concat(Yx)),ow=Nn([Jy,rx,lx,mx].concat(Yx)),nw=Nn([Jy,mx,bx,lx,ax].concat(Yx)),sw=(e,t,o)=>{const n=Td(e.element,"."+o);if(n.length>0){const e=$(n,(e=>{const o=e.dom.getBoundingClientRect().top,s=n[0].dom.getBoundingClientRect().top;return Math.abs(o-s)>t})).getOr(n.length);return A.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return A.none()},rw=e=>((e,t)=>ma([Mh(e,t)]))(Bi("unnamed-events"),e),aw="silver.readonly",iw=Nn([("readonly",ls("readonly",$n))]);const lw=(e,t)=>{const o=e.mainUi.outerContainer.element,n=[e.mainUi.mothership,...e.uiMotherships];t&&V(n,(e=>{e.broadcastOn([Eu()],{target:o})})),V(n,(e=>{e.broadcastOn([aw],{readonly:t})}))},cw=(e,t)=>{e.on("init",(()=>{e.mode.isReadOnly()&&lw(t,!0)})),e.on("SwitchMode",(()=>lw(t,e.mode.isReadOnly()))),Df(e)&&e.mode.set("readonly")},dw=()=>gc.config({channels:{[aw]:{schema:iw,onReceive:(e,t)=>{pg.set(e,t.readonly)}}}}),uw=e=>pg.config({disabled:e}),mw=e=>pg.config({disabled:e,disableClass:"tox-tbtn--disabled"}),gw=e=>pg.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),pw=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},hw=(e,t)=>ea((o=>{pw(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),fw=(e,t)=>ta((o=>pw(e,o)(t.get()))),bw=(e,t)=>na(((o,n)=>{pw(e,o)(e.onAction),e.triggersSubmenu||t!==dy.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&zr(o,yr()),n.stop())})),vw={[hr()]:["disabling","alloy.base.behaviour","toggling","item-events"]},yw=xe,xw=(e,t,o,n)=>{const s=on(b);return{type:"item",dom:t.dom,components:yw(t.optComponents),data:e.data,eventOrder:vw,hasSubmenu:e.triggersSubmenu,itemBehaviours:ma([Mh("item-events",[bw(e,o),hw(e,s),fw(e,s)]),(r=()=>!e.enabled||n.isDisabled(),pg.config({disabled:r,disableClass:"tox-collection__item--state-disabled"})),dw(),Ah.config({})].concat(e.itemBehaviours))};var r},ww=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),Sw=e=>{const t=Tf.os.isMacOS()||Tf.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),s=L(n,(e=>{const t=e.toLowerCase().trim();return be(o,t)?o[t]:e}));return t?s.join(""):s.join("+")},kw=(e,t,o=[fy])=>ry(e,{tag:"div",classes:o},t),Cw=e=>({dom:{tag:"div",classes:[by]},components:[ul(Kv.translate(e))]}),Ow=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),_w=(e,t)=>({dom:{tag:"div",classes:[by]},components:[{dom:{tag:e.tag,styles:e.styles},components:[ul(Kv.translate(t))]}]}),Tw=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[ul(Sw(e))]}),Ew=e=>kw("checkmark",e,["tox-collection__item-checkmark"]),Aw=e=>{const t=e.map((e=>({attributes:{id:Bi("menu-item"),"aria-label":Kv.translate(e)}}))).getOr({});return{tag:"div",classes:[uy,my],...t}},Mw=(e,t,o,n=A.none())=>"color"===e.presets?((e,t,o)=>{const n=e.value,s=e.iconContent.map((e=>((e,t,o)=>{const n=t();return ty(e,n).or(o).getOrThunk(Zv(n))})(e,t.icons,o))),r=e.ariaLabel.map((e=>({"aria-label":t.translate(e),"data-mce-name":e}))).getOr({});return{dom:(()=>{const e=gy,t=s.getOr(""),o={tag:"div",attributes:r,classes:[e]};return"custom"===n?{...o,tag:"button",classes:[...o.classes,"tox-swatches__picker-btn"],innerHtml:t}:"remove"===n?{...o,classes:[...o.classes,"tox-swatch--remove"],innerHtml:t}:g(n)?{...o,attributes:{...o.attributes,"data-mce-color":n},styles:{"background-color":n},innerHtml:t}:o})(),optComponents:[]}})(e,t,n):((e,t,o,n)=>{const s={tag:"div",classes:[fy]},r=o?e.iconContent.map((e=>ry(e,s,t.icons,n))).orThunk((()=>A.some({dom:s}))):A.none(),a=e.checkMark,i=A.from(e.meta).fold((()=>Cw),(e=>be(e,"style")?k(_w,e.style):Cw)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>A.some(Ow(e,[by]))));return{dom:Aw(e.ariaLabel),optComponents:[r,l,e.shortcutContent.map(Tw),a,e.caret]}})(e,t,o,n),Dw=(e,t,o)=>fe(e,"tooltipWorker").map((e=>[Zb.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:Uc}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{Zb.setComponents(t,[ml({element:ze(e)})])}))}})])).getOrThunk((()=>o.map((e=>[Zb.config({...t.providers.tooltips.getConfig({tooltipText:e}),mode:"follow-highlight"})])).getOr([]))),Bw=(e,t)=>{const o=(e=>Of.DOM.encode(e))(Kv.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},Iw=(e,t)=>L(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":wy,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[xy,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,Iw(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>F(t.cardText.highlightOn,e))),n=o?A.from(t.cardText.matchText).getOr(""):"";return Ow(Bw(e.text,n),e.classes)}})),Fw=Em(nf(),sf()),Rw=e=>({value:Vw(e)}),Nw=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,zw=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Lw=e=>Nw.test(e)||zw.test(e),Vw=e=>Oe(e,"#").toUpperCase(),Hw=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},Pw=e=>{const t=Hw(e.red)+Hw(e.green)+Hw(e.blue);return Rw(t)},Uw=Math.min,Ww=Math.max,jw=Math.round,$w=/^\s*rgb\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*\)\s*$/i,Gw=/^\s*rgba\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*((?:\d?\.\d+|\d+)%?)\s*\)\s*$/i,qw=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),Yw=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},Xw=e=>{let t,o,n;const s=(e.hue||0)%360;let r=e.saturation/100,a=e.value/100;if(r=Ww(0,Uw(r,1)),a=Ww(0,Uw(a,1)),0===r)return t=o=n=jw(255*a),qw(t,o,n,1);const i=s/60,l=a*r,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=jw(255*(t+d)),o=jw(255*(o+d)),n=jw(255*(n+d)),qw(t,o,n,1)},Kw=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(Nw,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=zw.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),s=parseInt(t[3],16);return qw(o,n,s,1)},Jw=(e,t,o,n)=>{const s=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return qw(s,r,a,i)},Qw=e=>{const t=$w.exec(e);if(null!==t)return A.some(Jw(t[1],t[2],t[3],"1"));const o=Gw.exec(e);return null!==o?A.some(Jw(o[1],o[2],o[3],o[4])):A.none()},Zw=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,eS=qw(255,0,0,1),tS=(e,t)=>{e.dispatch("ResizeContent",t)},oS=(e,t)=>{e.dispatch("TextColorChange",t)},nS=(e,t)=>e.dispatch("ResolveName",{name:t.nodeName.toLowerCase(),target:t}),sS=(e,t)=>()=>{e(),t()},rS=e=>iS(e,"NodeChange",(t=>{t.setEnabled(e.selection.isEditable())})),aS=(e,t)=>o=>{const n=rS(e)(o),s=((e,t)=>o=>{const n=sn(),s=()=>{o.setActive(e.formatter.match(t));const s=e.formatter.formatChanged(t,o.setActive);n.set(s)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),n.clear()}})(e,t)(o);return()=>{n(),s()}},iS=(e,t,o)=>n=>{const s=()=>o(n),r=()=>{o(n),e.on(t,s)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),e.off(t,s)}},lS=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},cS=(e,t)=>()=>e.execCommand(t);var dS=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const uS={},mS=e=>fe(uS,e).getOrThunk((()=>{const t=`tinymce-custom-colors-${e}`,o=dS.getItem(t);if(m(o)){const e=dS.getItem("tinymce-custom-colors");dS.setItem(t,g(e)?e:"[]")}const n=((e,t=10)=>{const o=dS.getItem(e),n=r(o)?JSON.parse(o):[],s=t-(a=n).length<0?a.slice(0,t):a;var a;const i=e=>{s.splice(e,1)};return{add:o=>{((e,t)=>{const o=I(e,t);return-1===o?A.none():A.some(o)})(s,o).each(i),s.unshift(o),s.length>t&&s.pop(),dS.setItem(e,JSON.stringify(s))},state:()=>s.slice(0)}})(t,10);return uS[e]=n,n})),gS=(e,t)=>{mS(e).add(t)},pS=(e,t,o)=>({hue:e,saturation:t,value:o}),hS=e=>{let t=0,o=0,n=0;const s=e.red/255,r=e.green/255,a=e.blue/255,i=Math.min(s,Math.min(r,a)),l=Math.max(s,Math.max(r,a));return i===l?(n=i,pS(0,0,100*n)):(t=s===i?3:a===i?1:5,t=60*(t-(s===i?r-a:a===i?s-r:a-s)/(l-i)),o=(l-i)/l,n=l,pS(Math.round(t),Math.round(100*o),Math.round(100*n)))},fS=e=>Pw(Xw(e)),bS=e=>{return(t=e,Lw(t)?A.some({value:Vw(t)}):A.none()).orThunk((()=>Qw(e).map(Pw))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,s=n[0],r=n[1],a=n[2],i=n[3];return Pw(qw(s,r,a,i))}));var t},vS="forecolor",yS="hilitecolor",xS=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:"#"+bS(e[o]).value,icon:"checkmark",type:"choiceitem"});return t},wS=e=>t=>t.options.get(e),SS="#000000",kS=(e,t)=>t===vS&&e.options.isSet("color_map_foreground")?wS("color_map_foreground")(e):t===yS&&e.options.isSet("color_map_background")?wS("color_map_background")(e):wS("color_map")(e),CS=(e,t="default")=>Math.max(5,Math.ceil(Math.sqrt(kS(e,t).length))),OS=(e,t)=>{const o=wS("color_cols")(e),n=CS(e,t);return o===CS(e)?n:o},_S=(e,t="default")=>Math.round(t===vS?wS("color_cols_foreground")(e):t===yS?wS("color_cols_background")(e):wS("color_cols")(e)),TS=wS("custom_colors"),ES=wS("color_default_foreground"),AS=wS("color_default_background"),MS=(e,t)=>{const o=ze(e.selection.getStart()),n="hilitecolor"===t?Ls(o,(e=>{if($e(e)){const t=Rt(e,"background-color");return ke(Qw(t).exists((e=>0!==e.alpha)),t)}return A.none()})).getOr("rgba(0, 0, 0, 0)"):Rt(o,"color");return Qw(n).map((e=>"#"+Pw(e).value))},DS=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},BS=(e,t,o,n)=>{"custom"===o?PS(e)((o=>{o.each((o=>{gS(t,o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),MS(e,t).getOr(SS)):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},IS=(e,t,o)=>e.concat((e=>L(mS(e).state(),(e=>({type:"choiceitem",text:e,icon:"checkmark",value:e}))))(t).concat(DS(o))),FS=(e,t,o)=>n=>{n(IS(e,t,o))},RS=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},NS=(e,t)=>{e.setTooltip(t)},zS=(e,t)=>o=>{const n=MS(e,t);return ye(n,o.toUpperCase())},LS=(e,t,o)=>{if(De(o))return"forecolor"===t?"Text color":"Background color";const n="forecolor"===t?"Text color {0}":"Background color {0}",s=IS(kS(e,t),t,!1),r=j(s,(e=>e.value===o)).getOr({text:""}).text;return e.translate([n,e.translate(r)])},VS=(e,t,o,n)=>{e.ui.registry.addSplitButton(t,{tooltip:LS(e,o,n.get()),presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:zS(e,o),columns:_S(e,o),fetch:FS(kS(e,o),o,TS(e)),onAction:t=>{BS(e,o,n.get(),b)},onItemAction:(s,r)=>{BS(e,o,r,(o=>{n.set(o),oS(e,{name:t,color:o})}))},onSetup:s=>{RS(s,t,n.get());const r=n=>{n.name===t&&(RS(s,n.name,n.color),NS(s,LS(e,o,n.color)))};return e.on("TextColorChange",r),sS(rS(e)(s),(()=>{e.off("TextColorChange",r)}))}})},HS=(e,t,o,n,s)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",onSetup:n=>(NS(n,LS(e,o,s.get())),RS(n,t,s.get()),rS(e)(n)),getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",select:zS(e,o),initData:{storageKey:o},onAction:n=>{BS(e,o,n.value,(o=>{s.set(o),oS(e,{name:t,color:o})}))}}]})},PS=e=>(t,o)=>{let n=!1;const s={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const s=o.getData().colorpicker;n?(t(A.from(s)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",s]))},onClose:b,onCancel:()=>{t(A.none())}})},US=(e,t,o,n,s,r,a,i)=>{const l=qy(t),c=WS(t,o,n,"color"!==s?"normal":"color",r,a,i);return Ky(e,l,c,n,{menuType:s})},WS=(e,t,o,n,s,r,a)=>xe(L(e,(i=>{return"choiceitem"===i.type?(l=i,Qn("choicemenuitem",Kx,l)).fold(Yy,(i=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Mw({presets:o,textContent:t?e.text:A.none(),htmlContent:A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:A.none(),checkMark:t?A.some(Ew(a.icons)):A.none(),caret:A.none(),value:e.value},a,i),c=e.text.filter(x(!t)).map((e=>Zb.config(a.tooltips.getConfig({tooltipText:a.translate(e)}))));return wn(xw({data:ww(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Wh.set(e,t)},isActive:()=>Wh.isOn(e),isEnabled:()=>!pg.isDisabled(e),setEnabled:t=>pg.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(s),b),triggersSubmenu:!1,itemBehaviours:[...c.toArray()]},l,r,a),{toggling:{toggleClass:hy,toggleOnExecute:!1,selected:e.active,exclusive:!0}})})(i,1===o,n,t,r(i.value),s,a,qy(e))))):A.none();var l}))),jS=(e,t)=>{const o=Oy(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group"),previousSelector:e=>"color"===t?_l(e.element,"[aria-checked=true]"):A.none()}},$S=Bi("cell-over"),GS=Bi("cell-execute"),qS=(e,t,o)=>{const n=o=>Lr(o,GS,{row:e,col:t}),s=(e,t)=>{t.stop(),n(e)};return hl({dom:{tag:"div",attributes:{role:"button","aria-label":o}},behaviours:ma([Mh("insert-table-picker-cell",[Gr(Js(),Rh.focus),Gr(hr(),n),Gr(sr(),s),Gr(br(),s)]),Wh.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),Rh.config({onFocus:o=>Lr(o,$S,{row:e,col:t})})])})},YS=e=>q(e,(e=>L(e,fl))),XS=(e,t)=>ul(`${t}x${e}`),KS={inserttable:(e,t)=>{const o=(e=>(t,o)=>e.shared.providers.translate(["{0} columns, {1} rows",o,t]))(t),n=((e,t,o)=>{const n=[];for(let t=0;t<10;t++){const o=[];for(let n=0;n<10;n++){const s=e(t+1,n+1);o.push(qS(t,n,s))}n.push(o)}return n})(o),s=XS(0,0),r=Vb({dom:{tag:"span",classes:["tox-insert-table-picker__label"]},components:[s],behaviours:ma([Ah.config({})])});return{type:"widget",data:{value:Bi("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Fw.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:YS(n).concat(r.asSpec()),behaviours:ma([Mh("insert-table-picker",[ea((e=>{Ah.set(r.get(e),[s])})),Kr($S,((e,t,o)=>{const{row:s,col:a}=o.event;((e,t,o,n,s)=>{for(let n=0;n<10;n++)for(let s=0;s<10;s++)Wh.set(e[n][s],n<=t&&s<=o)})(n,s,a),Ah.set(r.get(e),[XS(s+1,a+1)])})),Kr(GS,((t,o,n)=>{const{row:s,col:r}=n.event;e.onAction({numRows:s+1,numColumns:r+1}),zr(t,yr())}))]),xh.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>IS(t.colorinput.getColors(e.initData.storageKey),e.initData.storageKey,o)),(e=>e.concat(DS(o))))})(e,t),n=t.colorinput.getColorCols(e.initData.storageKey),s="color",r={...US(Bi("menu-value"),o,(t=>{e.onAction({value:t})}),n,s,dy.CLOSE_ON_EXECUTE,e.select.getOr(T),t.shared.providers),markers:Oy(s),movement:jS(n,s),showMenuRole:!1};return{type:"widget",data:{value:Bi("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Fw.widget(uf.sketch(r))]}}},JS=e=>({type:"separator",dom:{tag:"div",classes:[my,"tox-collection__group-heading"]},components:e.text.map(ul).toArray()});var QS=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n),getExistingCoupled:(e,t,o,n)=>o.getExisting(e,t,n)}),ZS=[ls("others",Jn(dn.value,Pn()))],ek=Object.freeze({__proto__:null,init:()=>{const e={},t=(t,o)=>{if(0===re(t.others).length)throw new Error("Cannot find any known coupled components");return fe(e,o)},o=x({});return ua({readState:o,getExisting:(e,o,n)=>t(o,n).orThunk((()=>(fe(o.others,n).getOrDie("No information found for coupled component: "+n),A.none()))),getOrCreate:(o,n,s)=>t(n,s).getOrThunk((()=>{const t=fe(n.others,s).getOrDie("No information found for coupled component: "+s)(o),r=o.getSystem().build(t);return e[s]=r,r}))})}});const tk=pa({fields:ZS,name:"coupling",apis:QS,state:ek}),ok=e=>{let t=A.none(),o=[];const n=e=>{s()?r(e):o.push(e)},s=()=>t.isSome(),r=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{s()||(t=A.some(e),V(o,r),o=[])})),{get:n,map:e=>ok((t=>{n((o=>{t(e(o))}))})),isReady:s}},nk={nu:ok,pure:e=>ok((t=>{t(e)}))},sk=e=>{setTimeout((()=>{throw e}),0)},rk=e=>{const t=t=>{e().then(t,sk)};return{map:t=>rk((()=>e().then(t))),bind:t=>rk((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>rk((()=>e().then((()=>t.toPromise())))),toLazy:()=>nk.nu(t),toCached:()=>{let t=null;return rk((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},ak=e=>rk((()=>new Promise(e))),ik=e=>rk((()=>Promise.resolve(e))),lk=x("sink"),ck=x(km({name:lk(),overrides:x({dom:{tag:"div"},behaviours:ma([tu.config({useFixed:E})]),events:Wr([Jr(er()),Jr(qs()),Jr(sr())])})})),dk=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",s=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:s})),(e=>({type:n,hotspot:o,overrides:s,layouts:e})))},uk=(e,t,o,n,s,r,a)=>{const i=((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=pk(n,e);return i.map((t=>t.bind((t=>{const i=t.menus[t.primary];return A.from(i).each((t=>{e.listRole.each((e=>{t.role=e}))})),A.from(xf.sketch({...r.menu(),uid:Vi(""),data:t,highlightOnOpen:a,onOpenMenu:(e,t)=>{const n=l().getOrDie();tu.position(n,t,{anchor:o}),Tu.decloak(s)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();tu.position(n,o,{anchor:{type:"submenu",item:t}}),Tu.decloak(s)},onRepositionMenu:(e,t,n)=>{const s=l().getOrDie();tu.position(s,t,{anchor:o}),V(n,(e=>{tu.position(s,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(Rh.focus(n),Tu.close(s),A.some(!0))}))}))))})(e,t,dk(e,o),o,n,s,a);return i.map((e=>(e.fold((()=>{Tu.isOpen(n)&&Tu.close(n)}),(e=>{Tu.cloak(n),Tu.open(n,e),r(n)})),n)))},mk=(e,t,o,n,s,r,a)=>(Tu.close(n),ik(n)),gk=(e,t,o,n,s,r)=>{const a=tk.getCoupled(o,"sandbox");return(Tu.isOpen(a)?mk:uk)(e,t,o,a,n,s,r)},pk=(e,t)=>e.getSystem().getByUid(t.uid+"-"+lk()).map((e=>()=>dn.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>dn.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),hk=e=>{Tu.getState(e).each((e=>{xf.repositionMenus(e)}))},fk=(e,t,o)=>{const n=Al(),s=pk(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id}},behaviours:Qu(e.sandboxBehaviours,[qu.config({store:{mode:"memory",initialValue:t}}),Tu.config({onOpen:(s,r)=>{const a=dk(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=eg.getCurrent(t).getOr(t),s=Qt(e.element);o?Bt(n.element,"min-width",s+"px"):((e,t)=>{Jt.set(e,t)})(n.element,s)})(a.hotspot,r,e.useMinWidth),e.onOpen(a,s,r),void 0!==o&&void 0!==o.onOpen&&o.onOpen(s,r)},onClose:(e,r)=>{n.unlink(t.element),s().getOr(r).element.dom.dispatchEvent(new window.FocusEvent("focusout")),void 0!==o&&void 0!==o.onClose&&o.onClose(e,r)},isPartOf:(e,o,n)=>Ml(o,n)||Ml(t,n),getAttachPoint:()=>s().getOrDie()}),eg.config({find:e=>Tu.getState(e).bind((e=>eg.getCurrent(e)))}),gc.config({channels:{...Bu({isExtraPart:T}),...Fu({doReposition:hk})}})])}},bk=e=>{const t=tk.getCoupled(e,"sandbox");hk(t)},vk=()=>[Cs("sandboxClasses",[]),Ju("sandboxBehaviours",[eg,gc,Tu,qu])],yk=x([is("dom"),is("fetch"),xi("onOpen"),wi("onExecute"),Cs("getHotspot",A.some),Cs("getAnchorOverrides",x({})),ed(),Yu("dropdownBehaviours",[Wh,tk,xh,Rh]),is("toggleClass"),Cs("eventOrder",{}),fs("lazySink"),Cs("matchWidth",!1),Cs("useMinWidth",!1),fs("role"),fs("listRole")].concat(vk())),xk=x([Sm({schema:[bi(),Cs("fakeFocus",!1)],name:"menu",defaults:e=>({onExecute:e.onExecute})}),ck()]),wk=Km({name:"Dropdown",configFields:yk(),partFields:xk(),factory:(e,t,o,n)=>{const s=e=>{Tu.getState(e).each((e=>{xf.highlightPrimary(e)}))},r=(t,o,s)=>gk(e,w,t,n,o,s),a={expand:e=>{Wh.isOn(e)||r(e,b,vf.HighlightNone).get(b)},open:e=>{Wh.isOn(e)||r(e,b,vf.HighlightMenuAndItem).get(b)},refetch:t=>tk.getExistingCoupled(t,"sandbox").fold((()=>r(t,b,vf.HighlightMenuAndItem).map(b)),(o=>uk(e,w,t,o,n,b,vf.HighlightMenuAndItem).map(b))),isOpen:Wh.isOn,close:e=>{Wh.isOn(e)&&r(e,b,vf.HighlightMenuAndItem).get(b)},repositionMenus:e=>{Wh.isOn(e)&&bk(e)}},i=(e,t)=>(Vr(e),A.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:Ku(e.dropdownBehaviours,[Wh.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),tk.config({others:{sandbox:t=>fk(e,t,{onOpen:()=>Wh.on(t),onClose:()=>Wh.off(t)})}}),xh.config({mode:"special",onSpace:i,onEnter:i,onDown:(e,t)=>{if(wk.isOpen(e)){const t=tk.getCoupled(e,"sandbox");s(t)}else wk.open(e);return A.some(!0)},onEscape:(e,t)=>wk.isOpen(e)?(wk.close(e),A.some(!0)):A.none()}),Rh.config({})]),events:$h(A.some((e=>{r(e,s,vf.HighlightMenuAndItem).get(b)}))),eventOrder:{...e.eventOrder,[hr()]:["disabling","toggling","alloy.base.behaviour"]},apis:a,domModification:{attributes:{"aria-haspopup":e.listRole.getOr("true"),...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:("type",fe(e.dom,"attributes").bind((e=>fe(e,"type")))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),refetch:(e,t)=>e.refetch(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),Sk=(e,t,o)=>{Ly(e).each((e=>{var n;((e,t)=>{Tt(t.element,"id").each((t=>Ct(e.element,"aria-activedescendant",t)))})(e,o),(_a((n=t).element,Py)?A.some(n.element):_l(n.element,"."+Py)).each((t=>{Tt(t,"id").each((t=>Ct(e.element,"aria-controls",t)))}))})),Ct(o.element,"aria-selected","true")},kk=(e,t,o)=>{Ct(o.element,"aria-selected","false")},Ck=e=>tk.getExistingCoupled(e,"sandbox").bind(zy).map(Vy).map((e=>e.fetchPattern)).getOr("");var Ok;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(Ok||(Ok={}));const _k=(e,t,o,n,s)=>{const r=o.shared.providers,a=e=>s?{...e,shortcut:A.none(),icon:e.text.isSome()?A.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,Qn("menuitem",tw,i)).fold(Yy,(e=>A.some(((e,t,o,n=!0)=>{const s=Mw({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.none(),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return xw({data:ww(e),getApi:e=>({isEnabled:()=>!pg.isDisabled(e),setEnabled:t=>pg.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o)})(a(e),t,r,n))));case"nestedmenuitem":return(e=>Qn("nestedmenuitem",ow,e))(e).fold(Yy,(e=>A.some(((e,t,o,n=!0,s=!1)=>{const r=s?(a=o.icons,kw("chevron-down",a,[vy])):(e=>kw("chevron-right",e,[vy]))(o.icons);var a;const i=Mw({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.some(r),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return xw({data:ww(e),getApi:e=>({isEnabled:()=>!pg.isDisabled(e),setEnabled:t=>pg.set(e,!t),setIconFill:(t,o)=>{_l(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{Ct(e,"fill",o)}))},setTooltip:t=>{const n=o.translate(t);Ct(e.element,"aria-label",n)}}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,r,n,s))));case"togglemenuitem":return(e=>Qn("togglemenuitem",nw,e))(e).fold(Yy,(e=>A.some(((e,t,o,n=!0)=>{const s=Mw({iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,checkMark:A.some(Ew(o.icons)),caret:A.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return wn(xw({data:ww(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Wh.set(e,t)},isActive:()=>Wh.isOn(e),isEnabled:()=>!pg.isDisabled(e),setEnabled:t=>pg.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o),{toggling:{toggleClass:hy,toggleOnExecute:!1,selected:e.active},role:e.role.getOrUndefined()})})(a(e),t,r,n))));case"separator":return(e=>Qn("separatormenuitem",_x,e))(e).fold(Yy,(e=>A.some(JS(e))));case"fancymenuitem":return(e=>Qn("fancymenuitem",ew,e))(e).fold(Yy,(e=>((e,t)=>fe(KS,e.fancytype).map((o=>o(e,t))))(e,o)));default:return console.error("Unknown item in general menu",e),A.none()}var i},Tk=(e,t,o,n,s,r,a)=>{const i=1===n,l=!i||qy(e);return xe(L(e,(e=>{switch(e.type){case"separator":return(n=e,Qn("Autocompleter.Separator",_x,n)).fold(Yy,(e=>A.some(JS(e))));case"cardmenuitem":return(e=>Qn("cardmenuitem",Xx,e))(e).fold(Yy,(e=>A.some(((e,t,o,n)=>{const s={dom:Aw(e.label),optComponents:[A.some({dom:{tag:"div",classes:[xy,wy]},components:Iw(e.items,n)})]};return xw({data:ww({text:A.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!pg.isDisabled(e),setEnabled:t=>{pg.set(e,!t),V(Td(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(pg)&&pg.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:A.from(n.itemBehaviours).getOr([])},s,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},s,r,{itemBehaviours:Dw(e.meta,r,A.none()),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>Qn("Autocompleter.Item",Tx,e))(e).fold(Yy,(e=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Mw({presets:n,textContent:A.none(),htmlContent:o?e.text.map((e=>Bw(e,t))):A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:A.none(),checkMark:A.none(),caret:A.none(),value:e.value},a.providers,i,e.icon),c=e.text.filter((e=>!o&&""!==e));return xw({data:ww(e),enabled:e.enabled,getApi:x({}),onAction:t=>s(e.value,e.meta),onSetup:x(b),triggersSubmenu:!1,itemBehaviours:Dw(e,a,c)},l,r,a.providers)})(e,t,i,"normal",o,s,r,l))))}var n})))},Ek=(e,t,o,n,s,r)=>{const a=qy(t),i=xe(L(t,(e=>{const t=e=>_k(e,o,n,(e=>s?!be(e,"text"):a)(e),s);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)}))),l=(e=>"no-search"===e.searchMode?{menuType:"normal"}:{menuType:"searchable",searchMode:e})(r);return(s?Xy:Ky)(e,a,i,1,l)},Ak=e=>xf.singleData(e.value,e),Mk=e=>hd(ze(e.startContainer),e.startOffset,ze(e.endContainer),e.endOffset),Dk=(e,t)=>{const o=Bi("autocompleter"),n=on(!1),s=on(!1),r=rn(),a=hl(wf.sketch({dom:{tag:"div",classes:["tox-autocompleter"],attributes:{id:o}},components:[],fireDismissalEventInstead:{},inlineBehaviours:ma([Mh("dismissAutocompleter",[Gr(Er(),(()=>u())),Gr(Rr(),((t,o)=>{Tt(o.event.target,"id").each((t=>Ct(ze(e.getBody()),"aria-activedescendant",t)))}))])]),lazySink:t.getSink})),i=()=>wf.isOpen(a),l=s.get,c=()=>{if(i()){wf.hide(a),e.dom.remove(o,!1);const t=ze(e.getBody());Tt(t,"aria-owns").filter((e=>e===o)).each((()=>{At(t,"aria-owns"),At(t,"aria-activedescendant")}))}},d=()=>wf.getContent(a).bind((e=>ee(e.components(),0))),u=()=>e.execCommand("mceAutocompleterClose"),m=s=>{const i=(o=>{const s=se(o,(e=>A.from(e.columns))).getOr(1);return q(o,(o=>{const a=o.items;return Tk(a,o.matchText,((t,s)=>{const a={hide:()=>u(),reload:t=>{c(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};e.execCommand("mceAutocompleterRefreshActiveRange"),r.get().each((e=>{n.set(!0),o.onAction(a,e,t,s),n.set(!1)}))}),s,dy.BUBBLE_TO_SANDBOX,t,o.highlightOn)}))})(s);i.length>0?(((t,o)=>{const n=se(t,(e=>A.from(e.columns))).getOr(1);wf.showMenuAt(a,{anchor:{type:"selection",getSelection:()=>r.get().map(Mk),root:ze(e.getBody())}},((e,t,o,n)=>{const s=jS(t,n),r=Oy(n);return{data:Ak({...e,movement:s,menuBehaviours:rw("auto"!==t?[]:[ea(((e,t)=>{sw(e,4,r.item).each((({numColumns:t,numRows:o})=>{xh.setGridSize(e,o,t)}))}))])}),menu:{markers:Oy(n),fakeFocus:o===Ok.ContentFocus}}})(Ky("autocompleter-value",!0,o,n,{menuType:"normal"}),n,Ok.ContentFocus,"normal")),d().each(Cg.highlightFirst)})(s,i),Ct(ze(e.getBody()),"aria-owns",o),e.inline||g()):c()},g=()=>{e.dom.get(o)&&e.dom.remove(o,!1);const t=e.getDoc().documentElement,n=e.selection.getNode(),s=(e=>ai(e,!0))(a.element);It(s,{border:"0",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0",position:"absolute",width:"1px",top:`${n.offsetTop}px`,left:`${n.offsetLeft}px`}),e.dom.add(t,s.dom),_l(s,'[role="menu"]').each((e=>{Ht(e,"position"),Ht(e,"max-height")}))};e.on("AutocompleterStart",(({lookupData:e})=>{s.set(!0),n.set(!1),m(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>m(e))),e.on("AutocompleterUpdateActiveRange",(({range:e})=>r.set(e))),e.on("AutocompleterEnd",(()=>{c(),s.set(!1),n.set(!1),r.clear()}));((e,t)=>{const o=(e,t)=>{Lr(e,er(),{raw:t})},n=()=>e.getMenu().bind(Cg.getHighlighted);t.on("keydown",(t=>{const s=t.which;e.isActive()&&(e.isMenuOpen()?13===s?(n().each(Vr),t.preventDefault()):40===s?(n().fold((()=>{e.getMenu().each(Cg.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==s&&38!==s&&39!==s||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==s&&38!==s&&40!==s||e.cancelIfNecessary())})),t.on("NodeChange",(()=>{!e.isActive()||e.isProcessingAction()||t.queryCommandState("mceAutoCompleterInRange")||e.cancelIfNecessary()}))})({cancelIfNecessary:u,isMenuOpen:i,isActive:l,isProcessingAction:n.get,getMenu:d},e)},Bk=(e,t,o)=>Tl(e,t,o).isSome(),Ik=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},Fk=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?A.none():A.some(t.touches[0])},Rk=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=rn(),o=on(!1),n=Ik((t=>{e.triggerEvent(vr(),t),o.set(!0)}),400),s=Rs([{key:Ws(),value:e=>(Fk(e).each((s=>{n.cancel();const r={x:s.clientX,y:s.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(r)})),A.none())},{key:js(),value:e=>(n.cancel(),Fk(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),A.none())},{key:$s(),value:s=>(n.cancel(),t.get().filter((e=>Ze(e.target,s.target))).map((t=>o.get()?(s.prevent(),!1):e.triggerEvent(br(),s))))}]);return{fireIfReady:(e,t)=>fe(s,t).bind((t=>t(e)))}})(o),s=L(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>Fc(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),r=rn(),a=Fc(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),r.set(setTimeout((()=>{o.triggerEvent(gr(),e)}),0))})),i=Fc(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===Og[0]&&!F(["input","textarea"],Ue(e.target))&&!Bk(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=Fc(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=rn(),d=Fc(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(mr(),e)}),0))}));return{unbind:()=>{V(s,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),r.on(clearTimeout),c.on(clearTimeout)}}},Nk=(e,t)=>{const o=fe(e,"target").getOr(t);return on(o)},zk=Is([{stopped:[]},{resume:["element"]},{complete:[]}]),Lk=(e,t,o,n,s,r)=>{const a=e(t,n),i=((e,t)=>{const o=on(!1),n=on(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,s);return a.fold((()=>(r.logEventNoHandlers(t,n),zk.complete())),(e=>{const o=e.descHandler;return Yi(o)(i),i.isStopped()?(r.logEventStopped(t,e.element,o.purpose),zk.stopped()):i.isCut()?(r.logEventCut(t,e.element,o.purpose),zk.complete()):rt(e.element).fold((()=>(r.logNoParent(t,e.element,o.purpose),zk.complete())),(n=>(r.logEventResponse(t,e.element,o.purpose),zk.resume(n))))}))},Vk=(e,t,o,n,s,r)=>Lk(e,t,o,n,s,r).fold(E,(n=>Vk(e,t,o,n,s,r)),T),Hk=(e,t,o,n,s)=>{const r=Nk(o,n);return Vk(e,t,o,n,r,s)},Pk=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{ie(n,((n,s)=>{const r=void 0!==e[s]?e[s]:{};r[o]=((e,t)=>({cHandler:k.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[s]=r}))},unregisterId:t=>{ie(e,((e,o)=>{be(e,t)&&delete e[t]}))},filterByType:t=>fe(e,t).map((e=>ge(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>fe(e,o).bind((e=>Ls(n,(t=>((e,t)=>Li(t).bind((t=>fe(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{Li(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const s=(e=>{const t=e.element;return Li(t).getOrThunk((()=>((e,t)=>{const o=Bi(Ri+"uid-");return zi(t,o),o})(0,e.element)))})(n);ve(t,s)&&((e,n)=>{const s=t[n];if(s!==e)throw new Error('The tagId "'+n+'" is already used by: '+ii(s.element)+"\nCannot use it for: "+ii(e.element)+"\nThe conflicting element is"+(xt(s.element)?" ":" not ")+"already in the DOM");o(e)})(n,s);const r=[n];e.registerId(r,s,n.events),t[s]=n},unregister:o,getById:e=>fe(t,e)}},Uk=Xm({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:Xu(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[Cs("components",[]),Yu("containerBehaviours",[]),Cs("events",{}),Cs("domModification",{}),Cs("eventOrder",{})]}),Wk=e=>{const t=t=>rt(e.element).fold(E,(e=>Ze(t,e))),o=Pk(),n=(e,n)=>o.find(t,e,n),s=Rk(e.element,{triggerEvent:(e,t)=>ui(e,t.target,(o=>((e,t,o,n)=>Hk(e,t,o,o.target,n))(n,e,t,o)))}),r={debugInfo:x("real"),triggerEvent:(e,t,o)=>{ui(e,t,(s=>Hk(n,e,o,t,s)))},triggerFocus:(e,t)=>{Li(e).fold((()=>{hc(e)}),(o=>{ui(ur(),e,(o=>(((e,t,o,n,s)=>{const r=Nk(o,n);Lk(e,t,o,n,r,s)})(n,ur(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{r.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:hl,buildOrPatch:pl,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:E},a=e=>{e.connect(r),Ge(e.element)||(o.register(e),V(e.components(),a),r.triggerEvent(wr(),e.element,{target:e.element}))},i=e=>{Ge(e.element)||(V(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{lu(e,t)},c=e=>{uu(e)},d=e=>{const t=o.filter(pr());V(t,(t=>{const o=t.descHandler;Yi(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t,o)=>{const n=(e=>{const t=on(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:T,event:e,setSource:O("Cannot set source of a broadcasted event"),getSource:O("Cannot get source of a broadcasted event")}})(t);return V(e,(e=>{const t=e.descHandler;Yi(t)(n)})),n.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>dn.error(new Error('Could not find component with uid: "'+e+'" in system.'))),dn.value),h=e=>{const t=Li(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{s.unbind(),Uo(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},jk=x([Cs("prefix","form-field"),Yu("fieldBehaviours",[eg,qu])]),$k=x([km({schema:[is("dom")],name:"label"}),km({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[is("text")],name:"aria-descriptor"}),wm({factory:{sketch:e=>{const t=((e,t)=>{const o={};return ie(e,((e,n)=>{F(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[is("factory")],name:"field"})]),Gk=Km({name:"FormField",configFields:jk(),partFields:$k(),factory:(e,t,o,n)=>{const s=Ku(e.fieldBehaviours,[eg.config({find:t=>Rm(t,e,"field")}),qu.config({store:{mode:"manual",getValue:e=>eg.getCurrent(e).bind(qu.getValue),setValue:(e,t)=>{eg.getCurrent(e).each((e=>{qu.setValue(e,t)}))}}})]),r=Wr([ea(((t,o)=>{const n=zm(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=Bi(e.prefix);n.label().each((e=>{Ct(e.element,"for",o),Ct(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=Bi(e.prefix);Ct(o.element,"id",n),Ct(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>Rm(t,e,"field"),getLabel:t=>Rm(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:s,events:r,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}});var qk=tinymce.util.Tools.resolve("tinymce.html.Entities");const Yk=(e,t,o,n)=>{const s=Xk(e,t,o,n);return Gk.sketch(s)},Xk=(e,t,o,n)=>({dom:Kk(o),components:e.toArray().concat([t]),fieldBehaviours:ma(n)}),Kk=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),Jk=(e,t)=>Gk.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ul(t.translate(e))]}),Qk=Bi("form-component-change"),Zk=Bi("form-close"),eC=Bi("form-cancel"),tC=Bi("form-action"),oC=Bi("form-submit"),nC=Bi("form-block"),sC=Bi("form-unblock"),rC=Bi("form-tabchange"),aC=Bi("form-resize"),iC=(e,t,o)=>{const n=e.label.map((e=>Jk(e,t))),s=t.icons(),r=e=>(t,o)=>{Tl(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,_t(n,"data-collection-item-value"))}))},a=r(((o,n,s,r)=>{n.stop(),t.isDisabled()||Lr(o,tC,{name:e.name,value:r})})),i=[Gr(Js(),r(((e,t,o)=>{hc(o)}))),Gr(sr(),a),Gr(br(),a),Gr(Qs(),r(((e,t,o)=>{_l(e.element,"."+yy).each((e=>{Oa(e,yy)})),ka(o,yy)}))),Gr(Zs(),r((e=>{_l(e.element,"."+yy).each((e=>{Oa(e,yy),fc(e)}))}))),na(r(((t,o,n,s)=>{Lr(t,tC,{name:e.name,value:s})})))],l=(e,t)=>L(Td(e.element,".tox-collection__item"),t),c=Gk.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:w},behaviours:ma([pg.config({disabled:t.isDisabled,onDisabled:e=>{l(e,(e=>{ka(e,"tox-collection__item--state-disabled"),Ct(e,"aria-disabled",!0)}))},onEnabled:e=>{l(e,(e=>{Oa(e,"tox-collection__item--state-disabled"),At(e,"aria-disabled")}))}}),dw(),Ah.config({}),Zb.config({...t.tooltips.getConfig({tooltipText:"",onShow:e=>{_l(e.element,"."+yy+"[data-mce-tooltip]").each((o=>{Tt(o,"data-mce-tooltip").each((o=>{Zb.setComponents(e,t.tooltips.getComponents({tooltipText:o}))}))}))}}),mode:"children-keyboard-focus",anchor:e=>({type:"node",node:_l(e.element,"."+yy).orThunk((()=>Qe(".tox-collection__item"))),root:e.element,layouts:{onLtr:x([oc,tc,Jl,Zl,Ql,ec]),onRtl:x([oc,tc,Jl,Zl,Ql,ec])},bubble:Gc(0,-2,{})})}),qu.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const r=L(n,(o=>{const n=Kv.translate(o.text),r=1===e.columns?`<div class="tox-collection__item-label">${n}</div>`:"",a=`<div class="tox-collection__item-icon">${(e=>{var t;return null!==(t=s[e])&&void 0!==t?t:e})(o.icon)}</div>`,i={_:" "," - ":" ","-":" "},l=n.replace(/\_| \- |\-/g,(e=>i[e]));return`<div data-mce-tooltip="${l}" class="tox-collection__item${t.isDisabled()?" tox-collection__item--state-disabled":""}" tabindex="-1" data-collection-item-value="${qk.encodeAllRaw(o.value)}" aria-label="${l}">${a}${r}</div>`})),a="auto"!==e.columns&&e.columns>1?z(r,e.columns):[r],i=L(a,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));ri(o.element,i.join(""))})(o,n),"auto"===e.columns&&sw(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{xh.setGridSize(o,e,t)})),zr(o,aC)}}),Ub.config({}),xh.config((d=e.columns,"normal",1===d?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===d?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${my}`}})),Mh("collection-events",i)]),eventOrder:{[hr()]:["disabling","alloy.base.behaviour","collection-events"],[Qs()]:["collection-events","tooltipping"]}});var d;return Yk(n,c,["tox-form__group--collection"],[])},lC=["input","textarea"],cC=e=>{const t=Ue(e);return F(lC,t)},dC=(e,t)=>{const o=t.getRoot(e).getOr(e.element);Oa(o,t.invalidClass),t.notify.each((t=>{cC(e.element)&&Ct(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{ri(e,t.validHtml)})),t.onValid(e)}))},uC=(e,t,o,n)=>{const s=t.getRoot(e).getOr(e.element);ka(s,t.invalidClass),t.notify.each((t=>{cC(e.element)&&Ct(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{ri(e,n)})),t.onInvalid(e,n)}))},mC=(e,t,o)=>t.validator.fold((()=>ik(dn.value(!0))),(t=>t.validate(e))),gC=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),mC(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(uC(e,t,0,o),dn.error(o))),(o=>(dC(e,t),dn.value(o)))):dn.error("No longer in system"))));var pC=Object.freeze({__proto__:null,markValid:dC,markInvalid:uC,query:mC,run:gC,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return _a(o,t.invalidClass)}}),hC=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>Wr([Gr(t.onEvent,(t=>{gC(t,e).get(w)}))].concat(t.validateOnLoad?[ea((t=>{gC(t,e).get(b)}))]:[])))).getOr({})}),fC=[is("invalidClass"),Cs("getRoot",A.none),ks("notify",[Cs("aria","alert"),Cs("getContainer",A.none),Cs("validHtml",""),xi("onValid"),xi("onInvalid"),xi("onValidate")]),ks("validator",[is("validate"),Cs("onEvent","input"),Cs("validateOnLoad",!0)])];const bC=pa({fields:fC,name:"invalidating",active:hC,apis:pC,extra:{validation:e=>t=>{const o=qu.getValue(t);return ik(e(o))}}}),vC=pa({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>Wr([jr(lr(),E)]),exhibit:()=>aa({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),yC=Bi("color-input-change"),xC=Bi("color-swatch-change"),wC=Bi("color-picker-cancel"),SC=km({schema:[is("dom")],name:"label"}),kC=e=>km({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:Wr([qr(Ws(),((t,o,n)=>e(t,n)),[t]),qr(qs(),((t,o,n)=>e(t,n)),[t]),qr(Ys(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),CC=kC("top-left"),OC=kC("top"),_C=kC("top-right"),TC=kC("right"),EC=kC("bottom-right"),AC=kC("bottom"),MC=kC("bottom-left"),DC=kC("left"),BC=wm({name:"thumb",defaults:x({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:Wr([Xr(Ws(),e,"spectrum"),Xr(js(),e,"spectrum"),Xr($s(),e,"spectrum"),Xr(qs(),e,"spectrum"),Xr(Ys(),e,"spectrum"),Xr(Ks(),e,"spectrum")])})}),IC=e=>Hg(e.event);var FC=[SC,DC,TC,OC,AC,CC,_C,MC,EC,BC,wm({schema:[rs("mouseIsDown",(()=>on(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:ma([xh.config({mode:"special",onLeft:(o,n)=>t.onLeft(o,e,IC(n)),onRight:(o,n)=>t.onRight(o,e,IC(n)),onUp:(o,n)=>t.onUp(o,e,IC(n)),onDown:(o,n)=>t.onDown(o,e,IC(n))}),Ub.config({}),Rh.config({})]),events:Wr([Gr(Ws(),o),Gr(js(),o),Gr(qs(),o),Gr(Ys(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}})];const RC=x("slider.change.value"),NC=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?A.some(e.touches[0]).map((e=>qt(e.clientX,e.clientY))):A.none()}{const e=t;return void 0!==e.clientX?A.some(e).map((e=>qt(e.clientX,e.clientY))):A.none()}},zC=e=>e.model.minX,LC=e=>e.model.minY,VC=e=>e.model.minX-1,HC=e=>e.model.minY-1,PC=e=>e.model.maxX,UC=e=>e.model.maxY,WC=e=>e.model.maxX+1,jC=e=>e.model.maxY+1,$C=(e,t,o)=>t(e)-o(e),GC=e=>$C(e,PC,zC),qC=e=>$C(e,UC,LC),YC=e=>GC(e)/2,XC=e=>qC(e)/2,KC=(e,t)=>t?e.stepSize*e.speedMultiplier:e.stepSize,JC=e=>e.snapToGrid,QC=e=>e.snapStart,ZC=e=>e.rounded,eO=(e,t)=>void 0!==e[t+"-edge"],tO=e=>eO(e,"left"),oO=e=>eO(e,"right"),nO=e=>eO(e,"top"),sO=e=>eO(e,"bottom"),rO=e=>e.model.value.get(),aO=(e,t)=>({x:e,y:t}),iO=(e,t)=>{Lr(e,RC(),{value:t})},lO=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),cO=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),dO=(e,t,o)=>Math.max(t,Math.min(o,e)),uO=e=>{const{min:t,max:o,range:n,value:s,step:r,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(s<u)return p;if(s>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(s,u,m),c=dO(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,s)=>s.fold((()=>{const s=e-t,r=Math.round(s/n)*n;return dO(t+r,t-1,o+1)}),(t=>{const s=(e-t)%n,r=Math.round(s/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+r)*n;return Math.max(t,l)})))(c,t,o,r,i):l?Math.round(c):c}},mO=e=>{const{min:t,max:o,range:n,value:s,hasMinEdge:r,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return s<t?r?0:c:s>o?a?i:d:(s-t)/n*l},gO="top",pO="right",hO="bottom",fO="left",bO=e=>e.element.dom.getBoundingClientRect(),vO=(e,t)=>e[t],yO=e=>{const t=bO(e);return vO(t,fO)},xO=e=>{const t=bO(e);return vO(t,pO)},wO=e=>{const t=bO(e);return vO(t,gO)},SO=e=>{const t=bO(e);return vO(t,hO)},kO=e=>{const t=bO(e);return vO(t,"width")},CO=e=>{const t=bO(e);return vO(t,"height")},OO=(e,t,o)=>(e+t)/2-o,_O=(e,t)=>{const o=bO(e),n=bO(t),s=vO(o,fO),r=vO(o,pO),a=vO(n,fO);return OO(s,r,a)},TO=(e,t)=>{const o=bO(e),n=bO(t),s=vO(o,gO),r=vO(o,hO),a=vO(n,gO);return OO(s,r,a)},EO=(e,t)=>{Lr(e,RC(),{value:t})},AO=(e,t,o)=>{const n={min:zC(t),max:PC(t),range:GC(t),value:o,step:KC(t),snap:JC(t),snapStart:QC(t),rounded:ZC(t),hasMinEdge:tO(t),hasMaxEdge:oO(t),minBound:yO(e),maxBound:xO(e),screenRange:kO(e)};return uO(n)},MO=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?cO:lO)(rO(o),zC(o),PC(o),KC(o,n));return EO(t,s),A.some(s)})(e,t,o,n).map(E),DO=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=kO(e),a=n.bind((t=>A.some(_O(t,e)))).getOr(0),i=s.bind((t=>A.some(_O(t,e)))).getOr(r),l={min:zC(t),max:PC(t),range:GC(t),value:o,hasMinEdge:tO(t),hasMaxEdge:oO(t),minBound:yO(e),minOffset:0,maxBound:xO(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return mO(l)})(t,r,o,n,s);return yO(t)-yO(e)+a},BO=MO(-1),IO=MO(1),FO=A.none,RO=A.none,NO={"top-left":A.none(),top:A.none(),"top-right":A.none(),right:A.some(((e,t)=>{iO(e,WC(t))})),"bottom-right":A.none(),bottom:A.none(),"bottom-left":A.none(),left:A.some(((e,t)=>{iO(e,VC(t))}))};var zO=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=AO(e,t,o);return EO(e,n),n},setToMin:(e,t)=>{const o=zC(t);EO(e,o)},setToMax:(e,t)=>{const o=PC(t);EO(e,o)},findValueOfOffset:AO,getValueFromEvent:e=>NC(e).map((e=>e.left)),findPositionOfValue:DO,setPositionFromValue:(e,t,o,n)=>{const s=rO(o),r=DO(e,n.getSpectrum(e),s,n.getLeftEdge(e),n.getRightEdge(e),o),a=Qt(t.element)/2;Bt(t.element,"left",r-a+"px")},onLeft:BO,onRight:IO,onUp:FO,onDown:RO,edgeActions:NO});const LO=(e,t)=>{Lr(e,RC(),{value:t})},VO=(e,t,o)=>{const n={min:LC(t),max:UC(t),range:qC(t),value:o,step:KC(t),snap:JC(t),snapStart:QC(t),rounded:ZC(t),hasMinEdge:nO(t),hasMaxEdge:sO(t),minBound:wO(e),maxBound:SO(e),screenRange:CO(e)};return uO(n)},HO=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?cO:lO)(rO(o),LC(o),UC(o),KC(o,n));return LO(t,s),A.some(s)})(e,t,o,n).map(E),PO=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=CO(e),a=n.bind((t=>A.some(TO(t,e)))).getOr(0),i=s.bind((t=>A.some(TO(t,e)))).getOr(r),l={min:LC(t),max:UC(t),range:qC(t),value:o,hasMinEdge:nO(t),hasMaxEdge:sO(t),minBound:wO(e),minOffset:0,maxBound:SO(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return mO(l)})(t,r,o,n,s);return wO(t)-wO(e)+a},UO=A.none,WO=A.none,jO=HO(-1),$O=HO(1),GO={"top-left":A.none(),top:A.some(((e,t)=>{iO(e,HC(t))})),"top-right":A.none(),right:A.none(),"bottom-right":A.none(),bottom:A.some(((e,t)=>{iO(e,jC(t))})),"bottom-left":A.none(),left:A.none()};var qO=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=VO(e,t,o);return LO(e,n),n},setToMin:(e,t)=>{const o=LC(t);LO(e,o)},setToMax:(e,t)=>{const o=UC(t);LO(e,o)},findValueOfOffset:VO,getValueFromEvent:e=>NC(e).map((e=>e.top)),findPositionOfValue:PO,setPositionFromValue:(e,t,o,n)=>{const s=rO(o),r=PO(e,n.getSpectrum(e),s,n.getTopEdge(e),n.getBottomEdge(e),o),a=jt(t.element)/2;Bt(t.element,"top",r-a+"px")},onLeft:UO,onRight:WO,onUp:jO,onDown:$O,edgeActions:GO});const YO=(e,t)=>{Lr(e,RC(),{value:t})},XO=(e,t)=>({x:e,y:t}),KO=(e,t)=>(o,n,s)=>((e,t,o,n,s)=>{const r=e>0?cO:lO,a=t?rO(n).x:r(rO(n).x,zC(n),PC(n),KC(n,s)),i=t?r(rO(n).y,LC(n),UC(n),KC(n,s)):rO(n).y;return YO(o,XO(a,i)),A.some(a)})(e,t,o,n,s).map(E),JO=KO(-1,!1),QO=KO(1,!1),ZO=KO(-1,!0),e_=KO(1,!0),t_={"top-left":A.some(((e,t)=>{iO(e,aO(VC(t),HC(t)))})),top:A.some(((e,t)=>{iO(e,aO(YC(t),HC(t)))})),"top-right":A.some(((e,t)=>{iO(e,aO(WC(t),HC(t)))})),right:A.some(((e,t)=>{iO(e,aO(WC(t),XC(t)))})),"bottom-right":A.some(((e,t)=>{iO(e,aO(WC(t),jC(t)))})),bottom:A.some(((e,t)=>{iO(e,aO(YC(t),jC(t)))})),"bottom-left":A.some(((e,t)=>{iO(e,aO(VC(t),jC(t)))})),left:A.some(((e,t)=>{iO(e,aO(VC(t),XC(t)))}))};var o_=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=AO(e,t,o.left),s=VO(e,t,o.top),r=XO(n,s);return YO(e,r),r},setToMin:(e,t)=>{const o=zC(t),n=LC(t);YO(e,XO(o,n))},setToMax:(e,t)=>{const o=PC(t),n=UC(t);YO(e,XO(o,n))},getValueFromEvent:e=>NC(e),setPositionFromValue:(e,t,o,n)=>{const s=rO(o),r=DO(e,n.getSpectrum(e),s.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=PO(e,n.getSpectrum(e),s.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=Qt(t.element)/2,l=jt(t.element)/2;Bt(t.element,"left",r-i+"px"),Bt(t.element,"top",a-l+"px")},onLeft:JO,onRight:QO,onUp:ZO,onDown:e_,edgeActions:t_});const n_=Km({name:"Slider",configFields:[Cs("stepSize",1),Cs("speedMultiplier",10),Cs("onChange",b),Cs("onChoose",b),Cs("onInit",b),Cs("onDragStart",b),Cs("onDragEnd",b),Cs("snapToGrid",!1),Cs("rounded",!0),fs("snapStart"),ls("model",os("mode",{x:[Cs("minX",0),Cs("maxX",100),rs("value",(e=>on(e.mode.minX))),is("getInitialValue"),Ci("manager",zO)],y:[Cs("minY",0),Cs("maxY",100),rs("value",(e=>on(e.mode.minY))),is("getInitialValue"),Ci("manager",qO)],xy:[Cs("minX",0),Cs("maxX",100),Cs("minY",0),Cs("maxY",100),rs("value",(e=>on({x:e.mode.minX,y:e.mode.minY}))),is("getInitialValue"),Ci("manager",o_)]})),Yu("sliderBehaviours",[xh,qu]),rs("mouseIsDown",(()=>on(!1)))],partFields:FC,factory:(e,t,o,n)=>{const s=t=>Nm(t,e,"thumb"),r=t=>Nm(t,e,"spectrum"),a=t=>Rm(t,e,"left-edge"),i=t=>Rm(t,e,"right-edge"),l=t=>Rm(t,e,"top-edge"),c=t=>Rm(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:r})},g=(e,t)=>{d.value.set(t);const o=s(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&Rm(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,s(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,s(t)),p(t)},b=t=>{Rm(t,e,"spectrum").map(xh.focusIn)};return{uid:e.uid,dom:e.dom,components:t,behaviours:Ku(e.sliderBehaviours,[xh.config({mode:"special",focusIn:b}),qu.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),gc.config({channels:{[Mu()]:{onReceive:p}}})]),events:Wr([Gr(RC(),((t,o)=>{((t,o)=>{g(t,o);const n=s(t);e.onChange(t,n,o),A.some(!0)})(t,o.event.value)})),ea(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=s(t);m(t,a);const i=r(t);e.onInit(t,a,i,d.value.get())})),Gr(Ws(),h),Gr($s(),f),Gr(qs(),((e,t)=>{b(e),h(e,t)})),Gr(Ks(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),s_=Bi("rgb-hex-update"),r_=Bi("slider-update"),a_=Bi("palette-update"),i_="form",l_=[Yu("formBehaviours",[qu])],c_=e=>"<alloy.field."+e+">",d_=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Ku(e.formBehaviours,[qu.config({store:{mode:"manual",getValue:t=>{const o=Lm(t,e);return le(o,((e,t)=>e().bind((e=>{return o=eg.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+ii(e.element)),o.fold((()=>dn.error(n)),dn.value);var o,n})).map(qu.getValue)))},setValue:(t,o)=>{ie(o,((o,n)=>{Rm(t,e,n).each((e=>{eg.getCurrent(e).each((e=>{qu.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>Rm(t,e,o).bind(eg.getCurrent)}}),u_={getField:Gi(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),Mm(i_,c_(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=L(n,(e=>wm({name:e,pname:c_(e)})));return $m(i_,l_,s,d_,o)}},m_=Bi("valid-input"),g_=Bi("invalid-input"),p_=Bi("validating-input"),h_="colorcustom.rgb.",f_=(e,t,o,n)=>{const s=(o,n)=>bC.config({invalidClass:t("invalid"),notify:{onValidate:e=>{Lr(e,p_,{type:o})},onValid:e=>{Lr(e,m_,{type:o,value:qu.getValue(e)})},onInvalid:e=>{Lr(e,g_,{type:o,value:qu.getValue(e)})}},validator:{validate:t=>{const o=qu.getValue(t),s=n(o)?dn.value(!0):dn.error(e("aria.input.invalid"));return ik(s)},validateOnLoad:!1}}),r=(o,n,r,a,i)=>{const l=e(h_+"range"),c=Gk.parts.label({dom:{tag:"label",attributes:{"aria-label":a}},components:[ul(r)]}),d=Gk.parts.field({data:i,factory:Iy,inputAttributes:{type:"text",..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:ma([s(n,o),Ub.config({})]),onSetValue:e=>{bC.isInvalid(e)&&bC.run(e).get(b)}}),u=[c,d],m="hex"!==n?[Gk.parts["aria-descriptor"]({text:l})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(m)}},a=(e,t)=>{const o=t.red,n=t.green,s=t.blue;qu.setValue(e,{red:o,green:n,blue:s})},i=Vb({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),l=(e,t)=>{i.getOpt(e).each((e=>{Bt(e.element,"background-color","#"+t.value)}))},c=Xm({factory:()=>{const s={red:on(A.some(255)),green:on(A.some(255)),blue:on(A.some(255)),hex:on(A.some("ffffff"))},c=e=>s[e].get(),d=(e,t)=>{s[e].set(t)},u=e=>{const t=e.red,o=e.green,n=e.blue;d("red",A.some(t)),d("green",A.some(o)),d("blue",A.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?d(o.type,A.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=Rw(t);d("hex",A.some(n.value));const s=Kw(n);a(e,s),u(s),Lr(e,s_,{hex:n}),l(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);d(t,A.some(n)),c("red").bind((e=>c("green").bind((t=>c("blue").map((o=>qw(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=Pw(t);return u_.getField(e,"hex").each((t=>{Rh.isFocused(t)||qu.setValue(e,{hex:o.value})})),o})(e,t);Lr(e,s_,{hex:o}),l(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(h_+t+".label"),description:e(h_+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return wn(u_.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",Gk.sketch(r(Yw,"red",h.label,h.description,255))),o.field("green",Gk.sketch(r(Yw,"green",f.label,f.description,255))),o.field("blue",Gk.sketch(r(Yw,"blue",b.label,b.description,255))),o.field("hex",Gk.sketch(r(Lw,"hex",v.label,v.description,"ffffff"))),i.asSpec()],formBehaviours:ma([bC.config({invalidClass:t("form-invalid")}),Mh("rgb-form-events",[Gr(m_,g),Gr(g_,m),Gr(p_,m)])])}))),{apis:{updateHex:(e,t)=>{qu.setValue(e,{hex:t.value}),((e,t)=>{const o=Kw(t);a(e,o),u(o)})(e,t),l(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return c},b_=(e,t)=>{const o=Xm({name:"ColourPicker",configFields:[is("dom"),Cs("onValidHex",b),Cs("onInvalidHex",b)],factory:o=>{const n=f_(e,t,o.onValidHex,o.onInvalidHex),s=((e,t)=>{const o=n_.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=n_.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),s=(e,t)=>{const{width:o,height:n}=e,s=e.getContext("2d");if(null===s)return;s.fillStyle=t,s.fillRect(0,0,o,n);const r=s.createLinearGradient(0,0,o,0);r.addColorStop(0,"rgba(255,255,255,1)"),r.addColorStop(1,"rgba(255,255,255,0)"),s.fillStyle=r,s.fillRect(0,0,o,n);const a=s.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),s.fillStyle=a,s.fillRect(0,0,o,n)};return Xm({factory:r=>{const a=x({x:0,y:0}),i=ma([eg.config({find:A.some}),Rh.config({})]);return n_.sketch({dom:{tag:"div",attributes:{role:"slider","aria-valuetext":e(["Saturation {0}%, Brightness {1}%",0,0])},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:a},rounded:!1,components:[o,n],onChange:(t,o,n)=>{h(n)||Ct(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",Math.floor(n.x),Math.floor(100-n.y)])),Lr(t,a_,{value:n})},onInit:(e,t,o,n)=>{s(o.element.dom,Zw(eS))},sliderBehaviours:i})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=pS(t,100,100),r=Xw(n);s(o,Zw(r))})(t,o)},setThumb:(t,o,n)=>{((t,o)=>{const n=hS(Kw(o));n_.setValue(t,{x:n.saturation,y:100-n.value}),Ct(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",n.saturation,n.value]))})(o,n)}},extraApis:{}})})(e,t),r={paletteRgba:on(eS),paletteHue:on(0)},a=Vb(((e,t)=>{const o=n_.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=n_.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return n_.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"slider","aria-valuemin":0,"aria-valuemax":360,"aria-valuenow":120}},rounded:!1,model:{mode:"y",getInitialValue:x(0)},components:[o,n],sliderBehaviours:ma([Rh.config({})]),onChange:(e,t,o)=>{Ct(e.element,"aria-valuenow",Math.floor(360-3.6*o)),Lr(e,r_,{value:o})}})})(0,t)),i=Vb(s.sketch({})),l=Vb(n.sketch({})),c=(e,t,o)=>{i.getOpt(e).each((e=>{s.setHue(e,o)}))},d=(e,t)=>{l.getOpt(e).each((e=>{n.updateHex(e,t)}))},u=(e,t,o)=>{a.getOpt(e).each((e=>{n_.setValue(e,(e=>100-e/360*100)(o))}))},m=(e,t)=>{i.getOpt(e).each((e=>{s.setThumb(e,t)}))},g=(e,t,o,n)=>{((e,t)=>{const o=Kw(e);r.paletteRgba.set(o),r.paletteHue.set(t)})(t,o),V(n,(n=>{n(e,t,o)}))};return{uid:o.uid,dom:o.dom,components:[i.asSpec(),a.asSpec(),l.asSpec()],behaviours:ma([Mh("colour-picker-events",[Gr(s_,(()=>{const e=[c,u,m];return(t,o)=>{const n=o.event.hex,s=(e=>hS(Kw(e)))(n);g(t,n,s.hue,e)}})()),Gr(a_,(()=>{const e=[d];return(t,o)=>{const n=o.event.value,s=r.paletteHue.get(),a=pS(s,n.x,100-n.y),i=fS(a);g(t,i,s,e)}})()),Gr(r_,(()=>{const e=[c,d];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),s=r.paletteRgba.get(),a=hS(s),i=pS(n,a.saturation,a.value),l=fS(i);g(t,l,n,e)}})())]),eg.config({find:e=>l.getOpt(e)}),xh.config({mode:"acyclic"})])}}});return o},v_=()=>eg.config({find:A.some}),y_=e=>eg.config({find:t=>ct(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),x_=Nn([Cs("preprocess",w),Cs("postprocess",w)]),w_=(e,t)=>{const o=es("RepresentingConfigs.memento processors",x_,t);return qu.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),s=qu.getValue(n);return o.postprocess(s)},setValue:(t,n)=>{const s=o.preprocess(n),r=e.get(t);qu.setValue(r,s)}}})},S_=(e,t,o)=>qu.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),k_=(e,t,o)=>S_(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),C_=e=>qu.config({store:{mode:"memory",initialValue:e}}),O_={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var __=tinymce.util.Tools.resolve("tinymce.Resource");const T_=e=>be(e,"init");var E_=tinymce.util.Tools.resolve("tinymce.util.Tools");const A_=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...s)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,s)}),t)}}},M_=Bi("alloy-fake-before-tabstop"),D_=Bi("alloy-fake-after-tabstop"),B_=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:ma([Rh.config({ignore:!0}),Ub.config({})])}),I_=(e,t)=>({dom:{tag:"div",classes:["tox-navobj",...e.getOr([])]},components:[B_([M_]),t,B_([D_])],behaviours:ma([y_(1)])}),F_=(e,t)=>{Lr(e,er(),{raw:{which:9,shiftKey:t}})},R_=(e,t)=>{const o=t.element;_a(o,M_)?F_(e,!0):_a(o,D_)&&F_(e,!1)},N_=e=>Bk(e,["."+M_,"."+D_].join(","),T),z_=Bi("update-dialog"),L_=Bi("update-title"),V_=Bi("update-body"),H_=Bi("update-footer"),P_=Bi("body-send-message"),U_=Bi("dialog-focus-shifted"),W_=Bo().browser,j_=W_.isSafari(),$_=W_.isFirefox(),G_=j_||$_,q_=W_.isChromium(),Y_=({scrollTop:e,scrollHeight:t,clientHeight:o})=>Math.ceil(e)+o>=t,X_=(e,t)=>e.scrollTo(0,"bottom"===t?99999999:t),K_=(e,t,o)=>{const n=e.dom;A.from(n.contentDocument).fold(o,(e=>{let o=0;const s=((e,t)=>{const o=e.body;return A.from(!/^<!DOCTYPE (html|HTML)/.test(t)&&(!q_&&!j_||g(o)&&(0!==o.scrollTop||Math.abs(o.scrollHeight-o.clientHeight)>1))?o:e.documentElement)})(e,t).map((e=>(o=e.scrollTop,e))).forall(Y_),r=()=>{const e=n.contentWindow;g(e)&&(s?X_(e,"bottom"):!s&&G_&&0!==o&&X_(e,o))};j_&&n.addEventListener("load",r,{once:!0}),e.open(),e.write(t),e.close(),j_||r()}))},J_=ke(G_,j_?500:200).map((e=>((e,t)=>{let o=null,n=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null,n=null)},throttle:(...s)=>{n=s,c(o)&&(o=setTimeout((()=>{const t=n;o=null,n=null,e.apply(null,t)}),t))}}})(K_,e))),Q_=Bi("toolbar.button.execute"),Z_=Bi("common-button-display-events"),eT={[hr()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events","tooltipping"],[_r()]:["toolbar-button-events",Z_],[Tr()]:["toolbar-button-events","dropdown-events","tooltipping"],[qs()]:["focusing","alloy.base.behaviour",Z_]},tT=e=>Bt(e.element,"width",Rt(e.element,"width")),oT=(e,t,o)=>ry(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),nT=(e,t)=>oT(e,t,[]),sT=(e,t)=>oT(e,t,[Ah.config({})]),rT=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[ul(o.translate(e))],behaviours:ma([Ah.config({})])}),aT=Bi("update-menu-text"),iT=Bi("update-menu-icon"),lT=(e,t,o,n)=>{const s=on(b),r=e.text.map((e=>Vb(rT(e,t,o.providers)))),a=e.icon.map((e=>Vb(sT(e,o.providers.icons)))),i=(e,t)=>{const o=qu.getValue(e);return Rh.focus(o),Lr(o,"keydown",{raw:t.event.raw}),wk.close(o),A.some(!0)},l=e.role.fold((()=>({})),(e=>({role:e}))),c=A.from(e.listRole).map((e=>({listRole:e}))).getOr({}),d=e.ariaLabel.fold((()=>({})),(e=>({"aria-label":o.providers.translate(e)}))),u=ry("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons),m=Bi("common-button-display-events"),p="dropdown-events",h=Vb(wk.sketch({...e.uid?{uid:e.uid}:{},...l,...c,dom:{tag:"button",classes:[t,`${t}--select`].concat(L(e.classes,(e=>`${t}--${e}`))),attributes:{...d,...g(n)?{"data-mce-name":n}:{}}},components:yw([a.map((e=>e.asSpec())),r.map((e=>e.asSpec())),A.some(u)]),matchWidth:!0,useMinWidth:!0,onOpen:(t,o,n)=>{e.searchable&&(e=>{Ly(e).each((e=>Rh.focus(e)))})(n)},dropdownBehaviours:ma([...e.dropdownBehaviours,uw((()=>e.disabled||o.providers.isDisabled())),dw(),vC.config({}),Ah.config({}),...e.tooltip.map((e=>Zb.config(o.providers.tooltips.getConfig({tooltipText:o.providers.translate(e)})))).toArray(),Mh(p,[hw(e,s),fw(e,s)]),Mh(m,[ea(((e,t)=>tT(e)))]),Mh("menubutton-update-display-text",[Gr(aT,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{Ah.set(e,[ul(o.providers.translate(t.event.text))])}))})),Gr(iT,((e,t)=>{a.bind((t=>t.getOpt(e))).each((e=>{Ah.set(e,[sT(t.event.icon,o.providers.icons)])}))}))])]),eventOrder:wn(eT,{[qs()]:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"],[_r()]:["toolbar-button-events",Zb.name(),p,m]}),sandboxBehaviours:ma([xh.config({mode:"special",onLeft:i,onRight:i}),Mh("dropdown-sandbox-events",[Gr(Fy,((e,t)=>{(e=>{const t=qu.getValue(e),o=zy(e).map(Vy);wk.refetch(t).get((()=>{const e=tk.getCoupled(t,"sandbox");o.each((t=>zy(e).each((e=>((e,t)=>{qu.setValue(e,t.fetchPattern),e.element.dom.selectionStart=t.selectionStart,e.element.dom.selectionEnd=t.selectionEnd})(e,t)))))}))})(e),t.stop()})),Gr(Ry,((e,t)=>{((e,t)=>{(e=>Tu.getState(e).bind(Cg.getHighlighted).bind(Cg.getHighlighted))(e).each((o=>{((e,t,o,n)=>{const s={...n,target:t};e.getSystem().triggerEvent(o,t,s)})(e,o.element,t.event.eventType,t.event.interactionEvent)}))})(e,t),t.stop()}))])]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:{...Ey(0,e.columns,e.presets),fakeFocus:e.searchable,..."listbox"===e.listRole?{}:{onHighlightItem:Sk,onCollapseMenu:(e,t,o)=>{Cg.getHighlighted(o).each((t=>{Sk(e,o,t)}))},onDehighlightItem:kk}}},getAnchorOverrides:()=>({maxHeightFunction:(e,t)=>{Pc()(e,t-10)}}),fetch:t=>ak(k(e.fetch,t))}));return h.asSpec()},cT=e=>"separator"===e.type,dT={type:"separator"},uT=(e,t)=>{const o=((e,t)=>{const o=W(e,((e,o)=>(e=>r(e))(o)?""===o?e:"|"===o?e.length>0&&!cT(e[e.length-1])?e.concat([dT]):e:be(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&cT(o[o.length-1])&&o.pop(),o})(r(e)?e.split(" "):e,t);return U(o,((e,o)=>{if((e=>be(e,"getSubmenuItems"))(o)){const n=(e=>{const t=fe(e,"value").getOrThunk((()=>Bi("generated-menu-item")));return wn({value:t},e)})(o),s=((e,t)=>{const o=e.getSubmenuItems(),n=uT(o,t);return{item:e,menus:wn(n.menus,{[e.value]:n.items}),expansions:wn(n.expansions,{[e.value]:e.value})}})(n,t);return{menus:wn(e.menus,s.menus),items:[s.item,...e.items],expansions:wn(e.expansions,s.expansions)}}return{...e,items:[o,...e.items]}}),{menus:{},expansions:{},items:[]})},mT=(e,t,o,n)=>{const s=Bi("primary-menu"),r=uT(e,o.shared.providers.menuItems());if(0===r.items.length)return A.none();const a=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-field",placeholder:e.placeholder}))))(n),i=Ek(s,r.items,t,o,n.isHorizontalMenu,a),l=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-results"}))))(n),c=le(r.menus,((e,n)=>Ek(n,e,t,o,!1,l))),d=wn(c,Fs(s,i));return A.from(xf.tieredData(s,d,r.expansions))},gT=e=>!be(e,"items"),pT="data-value",hT=(e,t,o,n,s)=>L(o,(o=>gT(o)?{type:"togglemenuitem",...s?{}:{role:"option"},text:o.text,value:o.value,active:o.value===n,onAction:()=>{qu.setValue(e,o.value),Lr(e,Qk,{name:t}),Rh.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>hT(e,t,o.items,n,s)})),fT=(e,t)=>se(e,(e=>gT(e)?ke(e.value===t,e):fT(e.items,t))),bT=Xm({name:"HtmlSelect",configFields:[is("options"),Yu("selectBehaviours",[Rh,qu]),Cs("selectClasses",[]),Cs("selectAttributes",{}),fs("data")],factory:(e,t)=>{const o=L(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>Fs("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:Ku(e.selectBehaviours,[Rh.config({}),qu.config({store:{mode:"manual",getValue:e=>ol(e.element),setValue:(t,o)=>{const n=te(e.options);j(e.options,(e=>e.value===o)).isSome()?nl(t.element,o):-1===t.element.dom.selectedIndex&&""===o&&n.each((e=>nl(t.element,e.value)))},...n}})])}}}),vT=x([Cs("field1Name","field1"),Cs("field2Name","field2"),Si("onLockedChange"),vi(["lockClass"]),Cs("locked",!1),Ju("coupledFieldBehaviours",[eg,qu])]),yT=(e,t)=>wm({factory:Gk,name:e,overrides:e=>({fieldBehaviours:ma([Mh("coupled-input-behaviour",[Gr(or(),(o=>{((e,t,o)=>Rm(e,t,o).bind(eg.getCurrent))(o,e,t).each((t=>{Rm(o,e,"lock").each((n=>{Wh.isOn(n)&&e.onLockedChange(o,t,n)}))}))}))])])})}),xT=x([yT("field1","field2"),yT("field2","field1"),wm({factory:zb,schema:[is("dom")],name:"lock",overrides:e=>({buttonBehaviours:ma([Wh.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),wT=Km({name:"FormCoupledInputs",configFields:vT(),partFields:xT(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Qu(e.coupledFieldBehaviours,[eg.config({find:A.some}),qu.config({store:{mode:"manual",getValue:t=>{const o=Hm(t,e,["field1","field2"]);return{[e.field1Name]:qu.getValue(o.field1()),[e.field2Name]:qu.getValue(o.field2())}},setValue:(t,o)=>{const n=Hm(t,e,["field1","field2"]);ve(o,e.field1Name)&&qu.setValue(n.field1(),o[e.field1Name]),ve(o,e.field2Name)&&qu.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>Rm(t,e,"field1"),getField2:t=>Rm(t,e,"field2"),getLock:t=>Rm(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),ST=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return dn.value({value:e,unit:o})}return dn.error(e)},kT=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>be(o,e);return e.unit===t?A.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?A.some(e.value):A.some(e.value/o[e.unit]*o[t]):A.none()},CT=e=>A.none(),OT=(e,t)=>{const o=e.label.map((e=>Jk(e,t))),n=[pg.config({disabled:()=>e.disabled||t.isDisabled()}),dw(),xh.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>(zr(e,oC),A.some(!0))}),Mh("textfield-change",[Gr(or(),((t,o)=>{Lr(t,Qk,{name:e.name})})),Gr(gr(),((t,o)=>{Lr(t,Qk,{name:e.name})}))]),Ub.config({})],s=e.validation.map((e=>bC.config({getRoot:e=>at(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=qu.getValue(t),n=e.validator(o);return ik(!0===n?dn.value(o):dn.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),r={...e.placeholder.fold(x({}),(e=>({placeholder:t.translate(e)}))),...e.inputMode.fold(x({}),(e=>({inputmode:e})))},a=Gk.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:r,inputClasses:[e.classname],inputBehaviours:ma(G([n,s])),selectOnFocus:!1,factory:Iy}),i=e.multiline?{dom:{tag:"div",classes:["tox-textarea-wrap"]},components:[a]}:a,l=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),c=[pg.config({disabled:()=>e.disabled||t.isDisabled(),onDisabled:e=>{Gk.getField(e).each(pg.disable)},onEnabled:e=>{Gk.getField(e).each(pg.enable)}}),dw()];return Yk(o,i,l,c)},_T=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),TT=e=>e.dimension.property,ET=(e,t)=>e.dimension.getDimension(t),AT=(e,t)=>{const o=_T(e,t);Ea(o,[t.shrinkingClass,t.growingClass])},MT=(e,t)=>{Oa(e.element,t.openClass),ka(e.element,t.closedClass),Bt(e.element,TT(t),"0px"),Pt(e.element)},DT=(e,t)=>{Oa(e.element,t.closedClass),ka(e.element,t.openClass),Ht(e.element,TT(t))},BT=(e,t,o,n)=>{o.setCollapsed(),Bt(e.element,TT(t),ET(t,e.element)),AT(e,t),MT(e,t),t.onStartShrink(e),t.onShrunk(e)},IT=(e,t,o,n)=>{const s=n.getOrThunk((()=>ET(t,e.element)));o.setCollapsed(),Bt(e.element,TT(t),s),Pt(e.element);const r=_T(e,t);Oa(r,t.growingClass),ka(r,t.shrinkingClass),MT(e,t),t.onStartShrink(e)},FT=(e,t,o)=>{const n=ET(t,e.element);("0px"===n?BT:IT)(e,t,o,A.some(n))},RT=(e,t,o)=>{const n=_T(e,t),s=_a(n,t.shrinkingClass),r=ET(t,e.element);DT(e,t);const a=ET(t,e.element);(s?()=>{Bt(e.element,TT(t),r),Pt(e.element)}:()=>{MT(e,t)})(),Oa(n,t.shrinkingClass),ka(n,t.growingClass),DT(e,t),Bt(e.element,TT(t),a),o.setExpanded(),t.onStartGrow(e)},NT=(e,t,o)=>{const n=_T(e,t);return!0===_a(n,t.growingClass)},zT=(e,t,o)=>{const n=_T(e,t);return!0===_a(n,t.shrinkingClass)};var LT=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){Ht(e.element,TT(t));const o=ET(t,e.element);Bt(e.element,TT(t),o)}},grow:(e,t,o)=>{o.isExpanded()||RT(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&FT(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&BT(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:NT,isShrinking:zT,isTransitioning:(e,t,o)=>NT(e,t)||zT(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?FT:RT)(e,t,o)},disableTransitions:AT,immediateGrow:(e,t,o)=>{o.isExpanded()||(DT(e,t),Bt(e.element,TT(t),ET(t,e.element)),AT(e,t),o.setExpanded(),t.onStartGrow(e),t.onGrown(e))}}),VT=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return aa(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:Fs(t.dimension.property,"0px")})},events:(e,t)=>Wr([Zr(ar(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(AT(o,e),t.isExpanded()&&Ht(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),HT=[is("closedClass"),is("openClass"),is("shrinkingClass"),is("growingClass"),fs("getAnimationRoot"),xi("onShrunk"),xi("onStartShrink"),xi("onGrown"),xi("onStartGrow"),Cs("expanded",!1),ls("dimension",os("property",{width:[Ci("property","width"),Ci("getDimension",(e=>Qt(e)+"px"))],height:[Ci("property","height"),Ci("getDimension",(e=>jt(e)+"px"))]}))];const PT=pa({fields:HT,name:"sliding",active:VT,apis:LT,state:Object.freeze({__proto__:null,init:e=>{const t=on(e.expanded);return ua({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:k(t.set,!1),setExpanded:k(t.set,!0),readState:()=>"expanded: "+t.get()})}})}),UT=e=>({isEnabled:()=>!pg.isDisabled(e),setEnabled:t=>pg.set(e,!t),setActive:t=>{const o=e.element;t?(ka(o,"tox-tbtn--enabled"),Ct(o,"aria-pressed",!0)):(Oa(o,"tox-tbtn--enabled"),At(o,"aria-pressed"))},isActive:()=>_a(e.element,"tox-tbtn--enabled"),setText:t=>{Lr(e,aT,{text:t})},setIcon:t=>Lr(e,iT,{icon:t})}),WT=(e,t,o,n,s=!0,r)=>lT({text:e.text,icon:e.icon,tooltip:e.tooltip,ariaLabel:e.tooltip,searchable:e.search.isSome(),role:n,fetch:(t,n)=>{const s={pattern:e.search.isSome()?Ck(t):""};e.fetch((t=>{n(mT(t,dy.CLOSE_ON_EXECUTE,o,{isHorizontalMenu:!1,search:e.search}))}),s,UT(t))},onSetup:e.onSetup,getApi:UT,columns:1,presets:"normal",classes:[],dropdownBehaviours:[...s?[Ub.config({})]:[]]},t,o.shared,r),jT=(e,t,o)=>{const n=e=>n=>{const s=!n.isActive();n.setActive(s),e.storage.set(s),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{hc(t.element),Lr(t,tC,{name:e.name,value:e.storage.get()})}))}))},s=e=>t=>{t.setActive(e.storage.get())};return t=>{t(L(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,onAction:n(e),onSetup:s(e)}})))}},$T=e=>({dom:{tag:"span",classes:["tox-tree__label"],attributes:{"aria-label":e}},components:[ul(e)]}),GT=Bi("leaf-label-event-id"),qT=({leaf:e,onLeafAction:t,visible:o,treeId:n,selectedId:s,backstage:r})=>{const a=e.menu.map((e=>WT(e,"tox-mbtn",r,A.none(),o))),i=[$T(e.title)];return a.each((e=>i.push(e))),zb.sketch({dom:{tag:"div",classes:["tox-tree--leaf__label","tox-trbtn"].concat(o?["tox-tree--leaf__label--visible"]:[])},components:i,role:"treeitem",action:o=>{t(e.id),o.getSystem().broadcastOn([`update-active-item-${n}`],{value:e.id})},eventOrder:{[er()]:[GT,"keying"]},buttonBehaviours:ma([...o?[Ub.config({})]:[],Wh.config({toggleClass:"tox-trbtn--enabled",toggleOnExecute:!1,aria:{mode:"selected"}}),gc.config({channels:{[`update-active-item-${n}`]:{onReceive:(t,o)=>{(o.value===e.id?Wh.on:Wh.off)(t)}}}}),Mh(GT,[ea(((t,o)=>{s.each((o=>{(o===e.id?Wh.on:Wh.off)(t)}))})),Gr(er(),((e,t)=>{const o="ArrowLeft"===t.event.raw.code,n="ArrowRight"===t.event.raw.code;o?(kl(e.element,".tox-tree--directory").each((t=>{e.getSystem().getByDom(t).each((e=>{Ol(t,".tox-tree--directory__label").each((t=>{e.getSystem().getByDom(t).each(Rh.focus)}))}))})),t.stop()):n&&t.stop()}))])])})},YT=Bi("directory-label-event-id"),XT=({directory:e,visible:t,noChildren:o,backstage:n})=>{const s=e.menu.map((e=>WT(e,"tox-mbtn",n,A.none()))),r=[{dom:{tag:"div",classes:["tox-chevron"]},components:[(a="chevron-right",i=n.shared.providers.icons,((e,t,o)=>ry(e,{tag:"span",classes:["tox-tree__icon-wrap","tox-icon"],behaviours:[]},t))(a,i))]},$T(e.title)];var a,i;s.each((e=>{r.push(e)}));const l=t=>{kl(t.element,".tox-tree--directory").each((o=>{t.getSystem().getByDom(o).each((o=>{const n=!Wh.isOn(o);Wh.toggle(o),Lr(t,"expand-tree-node",{expanded:n,node:e.id})}))}))};return zb.sketch({dom:{tag:"div",classes:["tox-tree--directory__label","tox-trbtn"].concat(t?["tox-tree--directory__label--visible"]:[])},components:r,action:l,eventOrder:{[er()]:[YT,"keying"]},buttonBehaviours:ma([...t?[Ub.config({})]:[],Mh(YT,[Gr(er(),((e,t)=>{const n="ArrowRight"===t.event.raw.code,s="ArrowLeft"===t.event.raw.code;n&&o&&t.stop(),(n||s)&&kl(e.element,".tox-tree--directory").each((o=>{e.getSystem().getByDom(o).each((o=>{!Wh.isOn(o)&&n||Wh.isOn(o)&&s?(l(e),t.stop()):s&&!Wh.isOn(o)&&(kl(o.element,".tox-tree--directory").each((e=>{Ol(e,".tox-tree--directory__label").each((e=>{o.getSystem().getByDom(e).each(Rh.focus)}))})),t.stop())}))}))}))])])})},KT=({children:e,onLeafAction:t,visible:o,treeId:n,expandedIds:s,selectedId:r,backstage:a})=>({dom:{tag:"div",classes:["tox-tree--directory__children"]},components:e.map((e=>"leaf"===e.type?qT({leaf:e,selectedId:r,onLeafAction:t,visible:o,treeId:n,backstage:a}):QT({directory:e,expandedIds:s,selectedId:r,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:a}))),behaviours:ma([PT.config({dimension:{property:"height"},closedClass:"tox-tree--directory__children--closed",openClass:"tox-tree--directory__children--open",growingClass:"tox-tree--directory__children--growing",shrinkingClass:"tox-tree--directory__children--shrinking",expanded:o}),Ah.config({})])}),JT=Bi("directory-event-id"),QT=({directory:e,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:s,expandedIds:r,selectedId:a})=>{const{children:i}=e,l=on(r),c=r.includes(e.id);return{dom:{tag:"div",classes:["tox-tree--directory"],attributes:{role:"treeitem"}},components:[XT({directory:e,visible:o,noChildren:0===e.children.length,backstage:s}),KT({children:i,expandedIds:r,selectedId:a,onLeafAction:t,visible:c,treeId:n,backstage:s})],behaviours:ma([Mh(JT,[ea(((e,t)=>{Wh.set(e,c)})),Gr("expand-tree-node",((e,t)=>{const{expanded:o,node:n}=t.event;l.set(o?[...l.get(),n]:l.get().filter((e=>e!==n)))}))]),Wh.config({...e.children.length>0?{aria:{mode:"expanded"}}:{},toggleClass:"tox-tree--directory--expanded",onToggled:(e,o)=>{const r=e.components()[1],c=(d=o,i.map((e=>"leaf"===e.type?qT({leaf:e,selectedId:a,onLeafAction:t,visible:d,treeId:n,backstage:s}):QT({directory:e,expandedIds:l.get(),selectedId:a,onLeafAction:t,labelTabstopping:d,treeId:n,backstage:s}))));var d;o?PT.grow(r):PT.shrink(r),Ah.set(r,c)}})])}},ZT=Bi("tree-event-id");var eE=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return Wr([Gr(e.event,o),ta((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[Gr(e,(()=>t.cancel()))])).getOr([])))}});const tE=e=>{const t=on(null);return ua({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var oE=Object.freeze({__proto__:null,throttle:tE,init:e=>e.stream.streams.state(e)}),nE=[ls("stream",os("mode",{throttle:[is("delay"),Cs("stopEvent",!0),Ci("streams",{setup:(e,t)=>{const o=e.stream,n=A_(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:tE})]})),Cs("event","input"),fs("cancelEvent"),Si("onStream")];const sE=pa({fields:nE,name:"streaming",active:eE,state:oE}),rE=(e,t,o)=>{const n=qu.getValue(o);qu.setValue(t,n),iE(t)},aE=(e,t)=>{const o=e.element,n=ol(o),s=o.dom;"number"!==_t(o,"type")&&t(s,n)},iE=e=>{aE(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},lE=x("alloy.typeahead.itemexecute"),cE=x([fs("lazySink"),is("fetch"),Cs("minChars",5),Cs("responseTime",1e3),xi("onOpen"),Cs("getHotspot",A.some),Cs("getAnchorOverrides",x({})),Cs("layouts",A.none()),Cs("eventOrder",{}),Bs("model",{},[Cs("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),Cs("selectsOver",!0),Cs("populateFromBrowse",!0)]),xi("onSetValue"),wi("onExecute"),xi("onItemExecute"),Cs("inputClasses",[]),Cs("inputAttributes",{}),Cs("inputStyles",{}),Cs("matchWidth",!0),Cs("useMinWidth",!1),Cs("dismissOnBlur",!0),vi(["openClass"]),fs("initialData"),fs("listRole"),Yu("typeaheadBehaviours",[Rh,qu,sE,xh,Wh,tk]),rs("lazyTypeaheadComp",(()=>on(A.none))),rs("previewing",(()=>on(!0)))].concat(Ay()).concat(vk())),dE=x([Sm({schema:[bi()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlightItem:(t,o,n)=>{e.previewing.get()?e.lazyTypeaheadComp.get().each((t=>{((e,t,o)=>{if(e.selectsOver){const n=qu.getValue(t),s=e.getDisplayText(n),r=qu.getValue(o);return 0===e.getDisplayText(r).indexOf(s)?A.some((()=>{rE(0,t,o),((e,t)=>{aE(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,s.length)})):A.none()}return A.none()})(e.model,t,n).fold((()=>{e.model.selectsOver?(Cg.dehighlight(o,n),e.previewing.set(!0)):e.previewing.set(!1)}),(t=>{t(),e.previewing.set(!1)}))})):e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&rE(e.model,t,n),Tt(n.element,"id").each((e=>Ct(t.element,"aria-activedescendant",e)))}))},onExecute:(t,o)=>e.lazyTypeaheadComp.get().map((e=>(Lr(e,lE(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&rE(e.model,t,o)}))}})})]),uE=Km({name:"Typeahead",configFields:cE(),partFields:dE(),factory:(e,t,o,n)=>{const s=(t,o,s)=>{e.previewing.set(!1);const r=tk.getCoupled(t,"sandbox");if(Tu.isOpen(r))eg.getCurrent(r).each((e=>{Cg.getHighlighted(e).fold((()=>{s(e)}),(()=>{Ur(r,e.element,"keydown",o)}))}));else{const o=e=>{eg.getCurrent(e).each(s)};uk(e,a(t),t,r,n,o,vf.HighlightMenuAndItem).get(b)}},r=My(e),a=e=>t=>t.map((t=>{const o=he(t.menus),n=q(o,(e=>P(e.items,(e=>"item"===e.type))));return qu.getState(e).update(L(n,(e=>e.data))),t})),i=e=>eg.getCurrent(e),l="typeaheadevents",c=[Rh.config({}),qu.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>ol(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{nl(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>Fs("initialValue",e))).getOr({})}}),sE.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const s=tk.getCoupled(t,"sandbox");if(Rh.isFocused(t)&&ol(t.element).length>=e.minChars){const o=i(s).bind((e=>Cg.getHighlighted(e).map(qu.getValue)));e.previewing.set(!0);const r=t=>{i(s).each((t=>{o.fold((()=>{e.model.selectsOver&&Cg.highlightFirst(t)}),(e=>{Cg.highlightBy(t,(t=>qu.getValue(t).value===e.value)),Cg.getHighlighted(t).orThunk((()=>(Cg.highlightFirst(t),A.none())))}))}))};uk(e,a(t),t,s,n,r,vf.HighlightJustMenu).get(b)}},cancelEvent:xr()}),xh.config({mode:"special",onDown:(e,t)=>(s(e,t,Cg.highlightFirst),A.some(!0)),onEscape:e=>{const t=tk.getCoupled(e,"sandbox");return Tu.isOpen(t)?(Tu.close(t),A.some(!0)):A.none()},onUp:(e,t)=>(s(e,t,Cg.highlightLast),A.some(!0)),onEnter:t=>{const o=tk.getCoupled(t,"sandbox"),n=Tu.isOpen(o);if(n&&!e.previewing.get())return i(o).bind((e=>Cg.getHighlighted(e))).map((e=>(Lr(t,lE(),{item:e}),!0)));{const s=qu.getValue(t);return zr(t,xr()),e.onExecute(o,t,s),n&&Tu.close(o),A.some(!0)}}}),Wh.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),tk.config({others:{sandbox:t=>fk(e,t,{onOpen:()=>Wh.on(t),onClose:()=>{e.lazyTypeaheadComp.get().each((e=>At(e.element,"aria-activedescendant"))),Wh.off(t)}})}}),Mh(l,[ea((t=>{e.lazyTypeaheadComp.set(A.some(t))})),ta((t=>{e.lazyTypeaheadComp.set(A.none())})),na((t=>{const o=b;gk(e,a(t),t,n,o,vf.HighlightMenuAndItem).get(b)})),Gr(lE(),((t,o)=>{const n=tk.getCoupled(t,"sandbox");rE(e.model,t,o.event.item),zr(t,xr()),e.onItemExecute(t,n,o.event.item,qu.getValue(t)),Tu.close(n),iE(t)}))].concat(e.dismissOnBlur?[Gr(mr(),(e=>{const t=tk.getCoupled(e,"sandbox");yc(t.element).isNone()&&Tu.close(t)}))]:[]))],d={[Tr()]:[qu.name(),sE.name(),l],...e.eventOrder};return{uid:e.uid,dom:By(wn(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...r,...Ku(e.typeaheadBehaviours,c)},eventOrder:d}}}),mE=e=>({...e,toCached:()=>mE(e.toCached()),bindFuture:t=>mE(e.bind((e=>e.fold((e=>ik(dn.error(e))),(e=>t(e)))))),bindResult:t=>mE(e.map((e=>e.bind(t)))),mapResult:t=>mE(e.map((e=>e.map(t)))),mapError:t=>mE(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>mE(ak((n=>{let s=!1;const r=setTimeout((()=>{s=!0,n(dn.error(o()))}),t);e.get((e=>{s||(clearTimeout(r),n(e))}))})))}),gE=e=>mE(ak(e)),pE=(e,t,o=[],n,s,r,a)=>{const i=t.fold((()=>({})),(e=>({action:e}))),l={buttonBehaviours:ma([uw((()=>!e.enabled||a.isDisabled())),dw(),Ub.config({}),...r.map((e=>Zb.config(a.tooltips.getConfig({tooltipText:a.translate(e)})))).toArray(),Mh("button press",[$r("click"),$r("mousedown")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...i},c=wn(l,{dom:n});return wn(c,{components:s})},hE=(e,t,o,n=[],s)=>{const r={tag:"button",classes:["tox-tbtn"],attributes:{...e.tooltip.map((e=>({"aria-label":o.translate(e)}))).getOr({}),"data-mce-name":s}},a=e.icon.map((e=>nT(e,o.icons))),i=yw([a]);return pE(e,t,n,r,i,e.tooltip,o)},fE=e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}},bE=(e,t,o,n=[],s=[])=>{const r=o.translate(e.text),a=e.icon.map((e=>nT(e,o.icons))),i=[a.getOrThunk((()=>ul(r)))],l=e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary"),c={tag:"button",classes:[...fE(l),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...s],attributes:{"aria-label":r,"data-mce-name":e.text}},d=e.icon.map(x(r));return pE(e,t,n,c,i,d,o)},vE=(e,t,o,n=[],s=[])=>{const r=bE(e,A.some(t),o,n,s);return zb.sketch(r)},yE=(e,t)=>o=>{"custom"===t?Lr(o,tC,{name:e,value:{}}):"submit"===t?zr(o,oC):"cancel"===t?zr(o,eC):console.error("Unknown button type: ",t)},xE=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>r,n=e,s={...e,type:"menubutton",search:A.none(),onSetup:t=>(t.setEnabled(e.enabled),b),fetch:jT(n.items,t,o)},r=Vb(WT(s,"tox-tbtn",o,A.none(),!0,e.text.or(e.tooltip).getOrUndefined()));return r.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=yE(e.name,t),s={...e,borderless:!1};return vE(s,n,o.shared.providers,[])}if(((e,t)=>"togglebutton"===t)(0,t))return((e,t,o)=>{var n,s;const r=e.icon.map((e=>sT(e,t.icons))).map(Vb),a=e.buttonType.getOr(e.primary?"primary":"secondary"),i={...e,name:null!==(n=e.name)&&void 0!==n?n:"",primary:"primary"===a,tooltip:e.tooltip,enabled:null!==(s=e.enabled)&&void 0!==s&&s,borderless:!1},l=i.tooltip.or(e.text).map((e=>({"aria-label":t.translate(e)}))).getOr({}),c=fE(null!=a?a:"secondary"),d=e.icon.isSome()&&e.text.isSome(),u={tag:"button",classes:[...c.concat(e.icon.isSome()?["tox-button--icon"]:[]),...e.active?["tox-button--enabled"]:[],...d?["tox-button--icon-and-text"]:[]],attributes:{...l,...g(o)?{"data-mce-name":o}:{}}},m=t.translate(e.text.getOr("")),p=ul(m),h=[...yw([r.map((e=>e.asSpec()))]),...e.text.isSome()?[p]:[]],f=pE(i,A.some((o=>{Lr(o,tC,{name:e.name,value:{setIcon:e=>{r.map((n=>n.getOpt(o).each((o=>{Ah.set(o,[sT(e,t.icons)])}))))}}})})),[],u,h,e.tooltip,t);return zb.sketch(f)})(e,o.shared.providers,e.text.or(e.tooltip).getOrUndefined());throw console.error("Unknown footer button type: ",t),new Error("Unknown footer button type")},wE={type:"separator"},SE=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),kE=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),CE=(e,t)=>(e=>L(e,SE))(((e,t)=>P(t,(t=>t.type===e)))(e,t)),OE=e=>CE("header",e.targets),_E=e=>CE("anchor",e.targets),TE=e=>A.from(e.anchorTop).map((e=>kE("<top>",e))).toArray(),EE=e=>A.from(e.anchorBottom).map((e=>kE("<bottom>",e))).toArray(),AE=(e,t)=>{const o=e.toLowerCase();return P(t,(e=>{var t;const n=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text,s=null!==(t=e.value)&&void 0!==t?t:"";return _e(n.toLowerCase(),o)||_e(s.toLowerCase(),o)}))},ME=Bi("aria-invalid"),DE=(e,t)=>{e.dom.checked=t},BE=e=>e.dom.checked,IE=e=>(t,o,n,s,r)=>fe(o,"name").fold((()=>e(o,s,A.none(),r)),(a=>t.field(a,e(o,s,fe(n,a),r)))),FE={bar:IE(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:L(e.items,t.interpreter)}))(e,t.shared))),collection:IE(((e,t,o)=>iC(e,t.shared.providers,o))),alertbanner:IE(((e,t)=>((e,t)=>{const o=oy(e.icon,t.icons);return Uk.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:e.url?void 0:o},components:e.url?[zb.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:o,attributes:{title:t.translate(e.iconTooltip)}},action:t=>Lr(t,tC,{name:"alert-banner",value:e.url}),buttonBehaviours:ma([ny()])})]:void 0},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]})})(e,t.shared.providers))),input:IE(((e,t,o)=>((e,t,o)=>OT({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),textarea:IE(((e,t,o)=>((e,t,o)=>OT({name:e.name,multiline:!0,label:e.label,inputMode:A.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),label:IE(((e,t,o,n)=>((e,t,o)=>{const n="tox-label",s="center"===e.align?[`${n}--center`]:[],r="end"===e.align?[`${n}--end`]:[],a=Vb({dom:{tag:"label",classes:[n,...s,...r]},components:[ul(t.providers.translate(e.label))]}),i=L(e.items,t.interpreter);return{dom:{tag:"div",classes:["tox-form__group"]},components:[a.asSpec(),...i],behaviours:ma([v_(),Ah.config({}),(l=A.none(),k_(l,si,ri)),xh.config({mode:"acyclic"}),Mh("label",[ea((t=>{e.for.each((e=>{o(e).each((e=>{a.getOpt(t).each((t=>{var o;const n=null!==(o=_t(e.element,"id"))&&void 0!==o?o:Bi("form-field");Ct(e.element,"id",n),Ct(t.element,"for",n)}))}))}))}))])])};var l})(e,t.shared,n))),iframe:(iM=(e,t,o)=>((e,t,o)=>{const n="tox-dialog__iframe",s=e.transparent?[]:[`${n}--opaque`],r=e.border?["tox-navobj-bordered"]:[],a={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...e.sandboxed?{sandbox:"allow-scripts allow-same-origin"}:{}},i=((e,t)=>{const o=on(e.getOr(""));return{getValue:e=>o.get(),setValue:(e,n)=>{if(o.get()!==n){const o=e.element,s=()=>Ct(o,"srcdoc",n);t?J_.fold(x(K_),(e=>e.throttle))(o,n,s):s()}o.set(n)}}})(o,e.streamContent),l=e.label.map((e=>Jk(e,t))),c=Gk.parts.field({factory:{sketch:e=>I_(A.from(r),{uid:e.uid,dom:{tag:"iframe",attributes:a,classes:[n,...s]},behaviours:ma([Ub.config({}),Rh.config({}),S_(o,i.getValue,i.setValue),gc.config({channels:{[U_]:{onReceive:(e,t)=>{t.newFocus.each((t=>{at(e.element).each((o=>{(Ze(e.element,t)?ka:Oa)(o,"tox-navobj-bordered-focus")}))}))}}}})])})}});return Yk(l,c,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n,s)=>{const r=wn(t,{source:"dynamic"});return IE(iM)(e,r,o,n,s)}),button:IE(((e,t)=>((e,t)=>{const o=yE(e.name,"custom");return n=A.none(),s=Gk.parts.field({factory:zb,...bE(e,A.some(o),t,[C_(""),v_()])}),Yk(n,s,[],[]);var n,s})(e,t.shared.providers))),checkbox:IE(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),A.some(!0)),s=Gk.parts.field({factory:{sketch:w},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:ma([v_(),pg.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{at(e.element).each((e=>ka(e,"tox-checkbox--disabled")))},onEnabled:e=>{at(e.element).each((e=>Oa(e,"tox-checkbox--disabled")))}}),Ub.config({}),Rh.config({}),k_(o,BE,DE),xh.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),Mh("checkbox-events",[Gr(nr(),((t,o)=>{Lr(t,Qk,{name:e.name})}))])])}),r=Gk.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[ul(t.translate(e.label))],behaviours:ma([vC.config({})])}),a=e=>ry("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=Vb({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return Gk.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[s,i.asSpec(),r],fieldBehaviours:ma([pg.config({disabled:()=>!e.enabled||t.isDisabled()}),dw()])})})(e,t.shared.providers,o))),colorinput:IE(((e,t,o)=>((e,t,o,n)=>{const s=Gk.parts.field({factory:Iy,inputClasses:["tox-textfield"],data:n,onSetValue:e=>bC.run(e).get(b),inputBehaviours:ma([pg.config({disabled:t.providers.isDisabled}),dw(),Ub.config({}),bC.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>at(e.element),notify:{onValid:e=>{const t=qu.getValue(e);Lr(e,yC,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=qu.getValue(e);if(0===t.length)return ik(dn.value(!0));{const e=Re("span");Bt(e,"background-color",t);const o=zt(e,"background-color").fold((()=>dn.error("blah")),(e=>dn.value(t)));return ik(o)}}}})]),selectOnFocus:!1}),r=e.label.map((e=>Jk(e,t.providers))),a=(e,t)=>{Lr(e,xC,{value:t})},i=Vb(((e,t)=>wk.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:ma([uw(t.providers.isDisabled),dw(),vC.config({}),Ub.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>ak((t=>e.fetch(t))).map((n=>A.from(Ak(wn(US(Bi("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,dy.CLOSE_ON_EXECUTE,T,t.providers),{movement:jS(e.columns,e.presets)}))))),parts:{menu:Ey(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[Ql,Jl,oc],onLtr:()=>[Jl,Ql,oc]},components:[],fetch:FS(o.getColors(e.storageKey),e.storageKey,o.hasCustomColors()),columns:o.getColorCols(e.storageKey),presets:"color",onItemAction:(t,n)=>{i.getOpt(t).each((t=>{"custom"===n?o.colorPicker((o=>{o.fold((()=>zr(t,wC)),(o=>{a(t,o),gS(e.storageKey,o)}))}),"#ffffff"):a(t,"remove"===n?"":n)}))}},t));return Gk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[s,i.asSpec()]}]),fieldBehaviours:ma([Mh("form-field-events",[Gr(yC,((t,o)=>{i.getOpt(t).each((e=>{Bt(e.element,"background-color",o.event.color)})),Lr(t,Qk,{name:e.name})})),Gr(xC,((e,t)=>{Gk.getField(e).each((o=>{qu.setValue(o,t.event.value),eg.getCurrent(e).each(Rh.focus)}))})),Gr(wC,((e,t)=>{Gk.getField(e).each((t=>{eg.getCurrent(e).each(Rh.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:IE(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,s=b_((e=>t=>r(t)?e.translate(O_[t]):e.translate(t))(t),n),a=Vb(s.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{Lr(e,tC,{name:"hex-valid",value:!0})},onInvalidHex:e=>{Lr(e,tC,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[a.asSpec()],behaviours:ma([S_(o,(e=>{const t=a.get(e);return eg.getCurrent(t).bind((e=>qu.getValue(e).hex)).map((e=>"#"+Oe(e,"#"))).getOr("")}),((e,t)=>{const o=A.from(/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t)).bind((e=>ee(e,1))),n=a.get(e);eg.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{qu.setValue(e,{hex:o.getOr("")}),u_.getField(e,"hex").each((e=>{zr(e,or())}))}))})),v_()])}})(0,t.shared.providers,o))),dropzone:IE(((e,t,o)=>((e,t,o)=>{const n=(e,t)=>{t.stop()},s=e=>(t,o)=>{V(e,(e=>{e(t,o)}))},r=(e,t)=>{var o;if(!pg.isDisabled(e)){const n=t.event.raw;i(e,null===(o=n.dataTransfer)||void 0===o?void 0:o.files)}},a=(e,t)=>{const o=t.event.raw.target;i(e,o.files)},i=(o,n)=>{n&&(qu.setValue(o,((e,t)=>{const o=E_.explode(t.getOption("images_file_types"));return P(ne(e),(e=>R(o,(t=>Ee(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(n,t)),Lr(o,Qk,{name:e.name}))},l=Vb({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:ma([Mh("input-file-events",[Jr(sr()),Jr(br())])])}),c=e.label.map((e=>Jk(e,t))),d=Gk.parts.field({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:ma([C_(o.getOr([])),v_(),pg.config({}),Wh.config({toggleClass:"dragenter",toggleOnExecute:!1}),Mh("dropzone-events",[Gr("dragenter",s([n,Wh.toggle])),Gr("dragleave",s([n,Wh.toggle])),Gr("dragover",n),Gr("drop",s([n,r])),Gr(nr(),a)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[ul(t.translate("Drop an image here"))]},zb.sketch({dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[ul(t.translate("Browse for an image")),l.asSpec()],action:e=>{l.get(e).element.dom.click()},buttonBehaviours:ma([Ub.config({}),uw(t.isDisabled),dw()])})]}]})}});return Yk(c,d,["tox-form__group--stretched"],[])})(e,t.shared.providers,o))),grid:IE(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:L(e.items,t.interpreter)}))(e,t.shared))),listbox:IE(((e,t,o)=>((e,t,o)=>{const n=R(e.items,(e=>!gT(e))),s=t.shared.providers,r=o.bind((t=>fT(e.items,t))).orThunk((()=>te(e.items).filter(gT))),a=e.label.map((e=>Jk(e,s))),i=Gk.parts.field({dom:{},factory:{sketch:o=>lT({uid:o.uid,text:r.map((e=>e.text)),icon:A.none(),tooltip:A.none(),role:ke(!n,"combobox"),...n?{}:{listRole:"listbox"},ariaLabel:e.label,fetch:(o,s)=>{const r=hT(o,e.name,e.items,qu.getValue(o),n);s(mT(r,dy.CLOSE_ON_EXECUTE,t,{isHorizontalMenu:!1,search:A.none()}))},onSetup:x(b),getApi:x({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[Ub.config({}),S_(r.map((e=>e.value)),(e=>_t(e.element,pT)),((t,o)=>{fT(e.items,o).each((e=>{Ct(t.element,pT,e.value),Lr(t,aT,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),l={dom:{tag:"div",classes:["tox-listboxfield"]},components:[i]};return Gk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:G([a.toArray(),[l]]),fieldBehaviours:ma([pg.config({disabled:x(!e.enabled),onDisabled:e=>{Gk.getField(e).each(pg.disable)},onEnabled:e=>{Gk.getField(e).each(pg.enable)}})])})})(e,t,o))),selectbox:IE(((e,t,o)=>((e,t,o)=>{const n=L(e.items,(e=>({text:t.translate(e.text),value:e.value}))),s=e.label.map((e=>Jk(e,t))),r=Gk.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:bT,selectBehaviours:ma([pg.config({disabled:()=>!e.enabled||t.isDisabled()}),Ub.config({}),Mh("selectbox-change",[Gr(nr(),((t,o)=>{Lr(t,Qk,{name:e.name})}))])])}),a=e.size>1?A.none():A.some(ry("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:G([[r],a.toArray()])};return Gk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:G([s.toArray(),[i]]),fieldBehaviours:ma([pg.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{Gk.getField(e).each(pg.disable)},onEnabled:e=>{Gk.getField(e).each(pg.enable)}}),dw()])})})(e,t.shared.providers,o))),sizeinput:IE(((e,t)=>((e,t)=>{let o=CT;const n=Bi("ratio-event"),s=e=>ry(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),r=e.label.getOr("Constrain proportions"),a=t.translate(r),i=wT.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{"aria-label":a,"data-mce-name":r}},components:[s("lock"),s("unlock")],buttonBehaviours:ma([pg.config({disabled:()=>!e.enabled||t.isDisabled()}),dw(),Ub.config({}),Zb.config(t.tooltips.getConfig({tooltipText:a}))])}),l=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),c=o=>Gk.parts.field({factory:Iy,inputClasses:["tox-textfield"],inputBehaviours:ma([pg.config({disabled:()=>!e.enabled||t.isDisabled()}),dw(),Ub.config({}),Mh("size-input-events",[Gr(Qs(),((e,t)=>{Lr(e,n,{isField1:o})})),Gr(nr(),((t,o)=>{Lr(t,Qk,{name:e.name})}))])]),selectOnFocus:!1}),d=e=>({dom:{tag:"label",classes:["tox-label"]},components:[ul(t.translate(e))]}),u=wT.parts.field1(l([Gk.parts.label(d("Width")),c(!0)])),m=wT.parts.field2(l([Gk.parts.label(d("Height")),c(!1)]));return wT.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[u,m,l([d("\xa0"),i])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{ST(qu.getValue(e)).each((e=>{o(e).each((e=>{qu.setValue(t,(e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit})(e))}))}))},coupledFieldBehaviours:ma([pg.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{wT.getField1(e).bind(Gk.getField).each(pg.disable),wT.getField2(e).bind(Gk.getField).each(pg.disable),wT.getLock(e).each(pg.disable)},onEnabled:e=>{wT.getField1(e).bind(Gk.getField).each(pg.enable),wT.getField2(e).bind(Gk.getField).each(pg.enable),wT.getLock(e).each(pg.enable)}}),dw(),Mh("size-input-events2",[Gr(n,((e,t)=>{const n=t.event.isField1,s=n?wT.getField1(e):wT.getField2(e),r=n?wT.getField2(e):wT.getField1(e),a=s.map(qu.getValue).getOr(""),i=r.map(qu.getValue).getOr("");o=((e,t)=>{const o=ST(e).toOptional(),n=ST(t).toOptional();return we(o,n,((e,t)=>kT(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>kT(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(CT))).getOr(CT)})(a,i)}))])])})})(e,t.shared.providers))),slider:IE(((e,t,o)=>((e,t,o)=>{const n=n_.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ul(t.translate(e.label))]}),s=n_.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),r=n_.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return n_.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:x(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,s,r],sliderBehaviours:ma([v_(),Rh.config({})]),onChoose:(t,o,n)=>{Lr(t,Qk,{name:e.name,value:n})},onChange:(t,o,n)=>{Lr(t,Qk,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:IE(((e,t,o)=>((e,t,o,n)=>{const s=t.shared.providers,r=t=>{const n=qu.getValue(t);o.addToHistory(n.value,e.filetype)},a={...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":ME,type:"url"},minChars:0,responseTime:0,fetch:n=>{const s=((e,t,o)=>{var n,s;const r=qu.getValue(t),a=null!==(s=null===(n=null==r?void 0:r.meta)||void 0===n?void 0:n.text)&&void 0!==s?s:r.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=AE(a,(e=>L(e,(e=>kE(e,e))))(o.getHistory(e)));return"file"===e?(s=[n,AE(a,OE(t)),AE(a,G([TE(t),_E(t),EE(t)]))],W(s,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(wE,t)),[])):n;var s}))})(e.filetype,n,o),r=mT(s,dy.BUBBLE_TO_SANDBOX,t,{isHorizontalMenu:!1,search:A.none()});return ik(r)},getHotspot:e=>g.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(bC)&&bC.run(e).get(b)},typeaheadBehaviours:ma([...o.getValidationHandler().map((t=>bC.config({getRoot:e=>at(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{c.getOpt(e).each((e=>{Ct(e.element,"title",s.translate(t))}))}},validator:{validate:o=>{const n=qu.getValue(o);return gE((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=dn.error(e.message);o(t)}else{const t=dn.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),pg.config({disabled:()=>!e.enabled||s.isDisabled()}),Ub.config({}),Mh("urlinput-events",[Gr(or(),(t=>{const o=ol(t.element),n=o.trim();n!==o&&nl(t.element,n),"file"===e.filetype&&Lr(t,Qk,{name:e.name})})),Gr(nr(),(t=>{Lr(t,Qk,{name:e.name}),r(t)})),Gr(gr(),(t=>{Lr(t,Qk,{name:e.name}),r(t)}))])]),eventOrder:{[or()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:Ey(0,0,"normal")},onExecute:(e,t,o)=>{Lr(t,oC,{})},onItemExecute:(t,o,n,s)=>{r(t),Lr(t,Qk,{name:e.name})}},i=Gk.parts.field({...a,factory:uE}),l=e.label.map((e=>Jk(e,s))),c=Vb(((e,t,o=e,n=e)=>ry(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:s.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},s.icons))("invalid",A.some(ME),"warning")),d=Vb({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[c.asSpec()]}),u=o.getUrlPicker(e.filetype),m=Bi("browser.url.event"),g=Vb({dom:{tag:"div",classes:["tox-control-wrap"]},components:[i,d.asSpec()],behaviours:ma([pg.config({disabled:()=>!e.enabled||s.isDisabled()})])}),p=Vb(vE({name:e.name,icon:A.some("browse"),text:e.picker_text.or(e.label).getOr(""),enabled:e.enabled,primary:!1,buttonType:A.none(),borderless:!0},(e=>zr(e,m)),s,[],["tox-browse-url"]));return Gk.sketch({dom:Kk([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:G([[g.asSpec()],u.map((()=>p.asSpec())).toArray()])}]),fieldBehaviours:ma([pg.config({disabled:()=>!e.enabled||s.isDisabled(),onDisabled:e=>{Gk.getField(e).each(pg.disable),p.getOpt(e).each(pg.disable)},onEnabled:e=>{Gk.getField(e).each(pg.enable),p.getOpt(e).each(pg.enable)}}),dw(),Mh("url-input-events",[Gr(m,(t=>{eg.getCurrent(t).each((o=>{const n=qu.getValue(o),s={fieldname:e.name,...n};u.each((n=>{n(s).get((n=>{qu.setValue(o,n),Lr(t,Qk,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:IE((e=>{const t=rn(),o=Vb({dom:{tag:e.tag}}),n=rn(),s=!T_(e)&&e.onFocus.isSome()?[Rh.config({onFocus:t=>{e.onFocus.each((e=>{e(t.element.dom)}))}}),Ub.config({})]:[];return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:ma([Mh("custom-editor-events",[ea((s=>{o.getOpt(s).each((o=>{(T_(e)?e.init(o.element.dom):__.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),S_(A.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),v_()].concat(s)),components:[o.asSpec()]}})),htmlpanel:IE(((e,t)=>((e,t)=>{const o=["tox-form__group",...e.stretched?["tox-form__group--stretched"]:[]],n=Mh("htmlpanel",[ea((t=>{e.onInit(t.element.dom)}))]);return"presentation"===e.presets?Uk.sketch({dom:{tag:"div",classes:o,innerHtml:e.html},containerBehaviours:ma([Zb.config({...t.tooltips.getConfig({tooltipText:"",onShow:e=>{_l(e.element,"[data-mce-tooltip]:hover").orThunk((()=>yc(e.element))).each((o=>{Tt(o,"data-mce-tooltip").each((o=>{Zb.setComponents(e,t.tooltips.getComponents({tooltipText:o}))}))}))}}),mode:"children-normal",anchor:e=>({type:"node",node:_l(e.element,"[data-mce-tooltip]:hover").orThunk((()=>yc(e.element).filter((e=>Tt(e,"data-mce-tooltip").isSome())))),root:e.element,layouts:{onLtr:x([oc,tc,Jl,Zl,Ql,ec]),onRtl:x([oc,tc,Jl,Zl,Ql,ec])},bubble:Gc(0,-2,{})})}),n])}):Uk.sketch({dom:{tag:"div",classes:o,innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:ma([Ub.config({}),Rh.config({}),n])})})(e,t.shared.providers))),imagepreview:IE(((e,t,o)=>((e,t)=>{const o=on(t.getOr({url:""})),n=Vb({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),s=Vb({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),r={};e.height.each((e=>r.height=e));const a=t.map((e=>({url:e.url,zoom:A.from(e.zoom),cachedWidth:A.from(e.cachedWidth),cachedHeight:A.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:r,attributes:{role:"presentation"}},components:[s.asSpec()],behaviours:ma([v_(),S_(a,(()=>o.get()),((e,t)=>{const r={url:t.url};t.zoom.each((e=>r.zoom=e)),t.cachedWidth.each((e=>r.cachedWidth=e)),t.cachedHeight.each((e=>r.cachedHeight=e)),o.set(r);const a=()=>{const{cachedWidth:t,cachedHeight:o,zoom:n}=r;if(!u(t)&&!u(o)){if(u(n)){const n=((e,t,o)=>{const n=Qt(e),s=jt(e);return Math.min(n/t,s/o,1)})(e.element,t,o);r.zoom=n}const a=((e,t,o,n,s)=>{const r=o*s,a=n*s,i=Math.max(0,e/2-r/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:r.toString()+"px",height:a.toString()+"px"}})(Qt(e.element),jt(e.element),t,o,r.zoom);s.getOpt(e).each((e=>{It(e.element,a)}))}};n.getOpt(e).each((o=>{const n=o.element;var s;t.url!==_t(n,"src")&&(Ct(n,"src",t.url),Oa(e.element,"tox-imagepreview__loaded")),a(),(s=n,new Promise(((e,t)=>{const o=()=>{r(),e(s)},n=[Fc(s,"load",o),Fc(s,"error",(()=>{r(),t("Unable to load data from image: "+s.dom.src)}))],r=()=>V(n,(e=>e.unbind()));s.dom.complete&&o()}))).then((t=>{e.getSystem().isConnected()&&(ka(e.element,"tox-imagepreview__loaded"),r.cachedWidth=t.dom.naturalWidth,r.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:IE(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(s=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:L(s,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:L(n,(e=>({dom:{tag:"tr"},components:L(e,o)})))})],behaviours:ma([Ub.config({}),Rh.config({})])};var n,s})(e,t.shared.providers))),tree:IE(((e,t)=>((e,t)=>{const o=e.onLeafAction.getOr(b),n=e.onToggleExpand.getOr(b),s=e.defaultExpandedIds,r=on(s),a=on(e.defaultSelectedId),i=Bi("tree-id"),l=(n,s)=>e.items.map((e=>"leaf"===e.type?qT({leaf:e,selectedId:n,onLeafAction:o,visible:!0,treeId:i,backstage:t}):QT({directory:e,selectedId:n,onLeafAction:o,expandedIds:s,labelTabstopping:!0,treeId:i,backstage:t})));return{dom:{tag:"div",classes:["tox-tree"],attributes:{role:"tree"}},components:l(a.get(),r.get()),behaviours:ma([xh.config({mode:"flow",selector:".tox-tree--leaf__label--visible, .tox-tree--directory__label--visible",cycles:!1}),Mh(ZT,[Gr("expand-tree-node",((e,t)=>{const{expanded:o,node:s}=t.event;r.set(o?[...r.get(),s]:r.get().filter((e=>e!==s))),n(r.get(),{expanded:o,node:s})}))]),gc.config({channels:{[`update-active-item-${i}`]:{onReceive:(e,t)=>{a.set(A.some(t.value)),Ah.set(e,l(A.some(t.value),r.get()))}}}}),Ah.config({})])}})(e,t))),panel:IE(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:L(e.items,t.shared.interpreter)}))(e,t)))},RE={field:(e,t)=>t,record:x([])},NE=(e,t,o,n,s)=>{const r=wn(n,{shared:{interpreter:t=>zE(e,t,o,r,s)}});return zE(e,t,o,r,s)},zE=(e,t,o,n,s)=>fe(FE,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(r=>r(e,t,o,n,s))),LE=(e,t,o,n)=>zE(RE,e,t,o,n),VE="layout-inset",HE=e=>e.x,PE=(e,t)=>e.x+e.width/2-t.width/2,UE=(e,t)=>e.x+e.width-t.width,WE=e=>e.y,jE=(e,t)=>e.y+e.height-t.height,$E=(e,t)=>e.y+e.height/2-t.height/2,GE=(e,t,o)=>Dl(UE(e,t),jE(e,t),o.insetSouthwest(),Nl(),"southwest",Wl(e,{right:0,bottom:3}),VE),qE=(e,t,o)=>Dl(HE(e),jE(e,t),o.insetSoutheast(),Rl(),"southeast",Wl(e,{left:1,bottom:3}),VE),YE=(e,t,o)=>Dl(UE(e,t),WE(e),o.insetNorthwest(),Fl(),"northwest",Wl(e,{right:0,top:2}),VE),XE=(e,t,o)=>Dl(HE(e),WE(e),o.insetNortheast(),Il(),"northeast",Wl(e,{left:1,top:2}),VE),KE=(e,t,o)=>Dl(PE(e,t),WE(e),o.insetNorth(),zl(),"north",Wl(e,{top:2}),VE),JE=(e,t,o)=>Dl(PE(e,t),jE(e,t),o.insetSouth(),Ll(),"south",Wl(e,{bottom:3}),VE),QE=(e,t,o)=>Dl(UE(e,t),$E(e,t),o.insetEast(),Hl(),"east",Wl(e,{right:0}),VE),ZE=(e,t,o)=>Dl(HE(e),$E(e,t),o.insetWest(),Vl(),"west",Wl(e,{left:1}),VE),eA=e=>{switch(e){case"north":return KE;case"northeast":return XE;case"northwest":return YE;case"south":return JE;case"southeast":return qE;case"southwest":return GE;case"east":return QE;case"west":return ZE}},tA=(e,t,o,n,s)=>Mc(n).map(eA).getOr(KE)(e,t,o,n,s),oA=e=>{switch(e){case"north":return JE;case"northeast":return qE;case"northwest":return GE;case"south":return KE;case"southeast":return XE;case"southwest":return YE;case"east":return ZE;case"west":return QE}},nA=(e,t,o,n,s)=>Mc(n).map(oA).getOr(KE)(e,t,o,n,s),sA={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},rA=(e,t,o)=>{const n={maxHeightFunction:Uc()};return()=>o()?{type:"node",root:bt(ft(e())),node:A.from(e()),bubble:Gc(12,12,sA),layouts:{onRtl:()=>[XE],onLtr:()=>[YE]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:Gc(-12,12,sA),layouts:{onRtl:()=>[Jl,Ql,oc],onLtr:()=>[Ql,Jl,oc]},overrides:n}},aA=(e,t,o,n)=>{const s={maxHeightFunction:Uc()};return()=>n()?{type:"node",root:bt(ft(t())),node:A.from(t()),bubble:Gc(12,12,sA),layouts:{onRtl:()=>[KE],onLtr:()=>[KE]},overrides:s}:e?{type:"node",root:bt(ft(t())),node:A.from(t()),bubble:Gc(0,-$t(t()),sA),layouts:{onRtl:()=>[tc],onLtr:()=>[tc]},overrides:s}:{type:"hotspot",hotspot:o(),bubble:Gc(0,0,sA),layouts:{onRtl:()=>[tc],onLtr:()=>[tc]},overrides:s}},iA=(e,t,o)=>()=>o()?{type:"node",root:bt(ft(e())),node:A.from(e()),layouts:{onRtl:()=>[KE],onLtr:()=>[KE]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[oc],onLtr:()=>[oc]}},lA=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng(),o=e.model.table.getSelectedCells();if(o.length>1){const e=o[0],t=o[o.length-1],n={firstCell:ze(e),lastCell:ze(t)};return A.some(n)}return A.some(yd.range(ze(t.startContainer),t.startOffset,ze(t.endContainer),t.endOffset))}}),cA=e=>t=>({type:"node",root:e(),node:t}),dA=(e,t,o,n)=>{const s=Ob(e),r=()=>ze(e.getBody()),a=()=>ze(e.getContentAreaContainer()),i=()=>s||!n();return{inlineDialog:rA(a,t,i),inlineBottomDialog:aA(e.inline,a,o,i),banner:iA(a,t,i),cursor:lA(e,r),node:cA(r)}},uA=e=>(t,o)=>{PS(e)(t,o)},mA=e=>()=>TS(e),gA=e=>t=>kS(e,t),pA=e=>t=>_S(e,t),hA=e=>()=>rb(e),fA=e=>ve(e,"items"),bA=e=>ve(e,"format"),vA=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],yA=e=>W(e,((e,t)=>{if(be(t,"items")){const o=yA(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(be(t,"inline")||(e=>be(e,"block"))(t)||(e=>be(e,"selector"))(t)){const o=`custom-${r(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),xA=e=>Lf(e).map((t=>{const o=((e,t)=>{const o=yA(t),n=t=>{V(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return Vf(e)?vA.concat(o):o})).getOr(vA),wA=(e,t,o)=>({...e,type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)}),SA=(e,t,o,n)=>{const s=t=>L(t,(t=>fA(t)?(e=>{const t=s(e.items);return{...e,type:"submenu",getStyleItems:x(t)}})(t):bA(t)?(e=>wA(e,o,n))(t):(e=>{const t=re(e);return 1===t.length&&F(t,"title")})(t)?{...t,type:"separator"}:(t=>{const s=r(t.name)?t.name:Bi(t.title),a=`custom-${s}`,i={...t,type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)};return e.formatter.register(s,i),i})(t)));return s(t)},kA=e=>{let t=0;const o=e=>[{dom:{tag:"div",classes:["tox-tooltip__body"]},components:[ul(e.tooltipText)]}];return{getConfig:n=>({delayForShow:()=>t>0?60:300,delayForHide:x(300),exclusive:!0,lazySink:e,tooltipDom:{tag:"div",classes:["tox-tooltip","tox-tooltip--up"]},tooltipComponents:o(n),onShow:(e,o)=>{t++,n.onShow&&n.onShow(e,o)},onHide:(e,o)=>{t--,n.onHide&&n.onHide(e,o)}}),getComponents:o}},CA=E_.trim,OA=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},_A=OA("true"),TA=OA("false"),EA=(e,t,o,n,s)=>({type:e,title:t,url:o,level:n,attach:s}),AA=e=>e.innerText||e.textContent,MA=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&BA(e),DA=e=>e&&/^(H[1-6])$/.test(e.nodeName),BA=e=>(e=>{let t=e;for(;t=t.parentNode;){const e=t.contentEditable;if(e&&"inherit"!==e)return _A(t)}return!1})(e)&&!TA(e),IA=e=>DA(e)&&BA(e),FA=e=>{var t;const o=(e=>e.id?e.id:Bi("h"))(e);return EA("header",null!==(t=AA(e))&&void 0!==t?t:"","#"+o,(e=>DA(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=o}))},RA=e=>{const t=e.id||e.name,o=AA(e);return EA("anchor",o||"#"+t,"#"+t,0,b)},NA=e=>CA(e.title).length>0,zA=e=>{const t=(e=>{const t=L(Td(ze(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return P((e=>L(P(e,IA),FA))(t).concat((e=>L(P(e,MA),RA))(t)),NA)},LA="tinymce-url-history",VA=e=>r(e)&&/^https?/.test(e),HA=e=>a(e)&&pe(e,(e=>{return!(l(t=e)&&t.length<=5&&Y(t,VA));var t})).isNone(),PA=()=>{const e=dS.getItem(LA);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+LA+" was not valid JSON",e),{};throw e}return HA(t)?t:(console.log("Local storage "+LA+" was not valid format",t),{})},UA=e=>{const t=PA();return fe(t,e).getOr([])},WA=(e,t)=>{if(!VA(e))return;const o=PA(),n=fe(o,t).getOr([]),s=P(n,(t=>t!==e));o[t]=[e].concat(s).slice(0,5),(e=>{if(!HA(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));dS.setItem(LA,JSON.stringify(e))})(o)},jA=e=>!!e,$A=e=>le(E_.makeMap(e,/[, ]/),jA),GA=e=>A.from(Qf(e)),qA=e=>A.from(e).filter(r).getOrUndefined(),YA=e=>({getHistory:UA,addToHistory:WA,getLinkInformation:()=>(e=>ob(e)?A.some({targets:zA(e.getBody()),anchorTop:qA(nb(e)),anchorBottom:qA(sb(e))}):A.none())(e),getValidationHandler:()=>(e=>A.from(Zf(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=A.from(tb(e)).filter(jA).map($A);return GA(e).fold(T,(e=>t.fold(E,(e=>re(e).length>0&&e))))})(e);return d(o)?o?GA(e):A.none():o[t]?GA(e):A.none()})(e,t).map((o=>n=>ak((s=>{const i={filetype:t,fieldname:n.fieldname,...A.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!r(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");s({value:e,meta:t})}),n.value,i)})))))(e,t)}),XA=Wm,KA=_m,JA=x([Cs("shell",!1),is("makeItem"),Cs("setupItem",b),Ju("listBehaviours",[Ah])]),QA=km({name:"items",overrides:()=>({behaviours:ma([Ah.config({})])})}),ZA=x([QA]),eM=Km({name:x("CustomList")(),configFields:JA(),partFields:ZA(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Ah.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Ku(e.listBehaviours,s.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?A.some(n):Rm(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const s=Ah.contents(n),r=o.length,a=r-s.length,i=a>0?N(a,(()=>e.makeItem())):[],l=s.slice(r);V(l,(e=>Ah.remove(n,e))),V(i,(e=>Ah.append(n,e)));const c=Ah.contents(n);V(c,((n,s)=>{e.setupItem(t,n,o[s],s)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),tM=x([is("dom"),Cs("shell",!0),Yu("toolbarBehaviours",[Ah])]),oM=x([km({name:"groups",overrides:()=>({behaviours:ma([Ah.config({})])})})]),nM=Km({name:"Toolbar",configFields:tM(),partFields:oM(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Ah.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Ku(e.toolbarBehaviours,s.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?A.some(n):Rm(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{Ah.set(e,o)}))},refresh:b},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),sM=b,rM=T,aM=x([]);var iM,lM=Object.freeze({__proto__:null,setup:sM,isDocked:rM,getBehaviours:aM});const cM=x(Bi("toolbar-height-change")),dM={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},uM="tox-tinymce--toolbar-sticky-on",mM="tox-tinymce--toolbar-sticky-off",gM=(e,t)=>F(Ei.getModes(e),t),pM=e=>{const t=e.element;at(t).each((o=>{const n="padding-"+Ei.getModes(e)[0];if(Ei.isDocked(e)){const e=Qt(o);Bt(t,"width",e+"px"),Bt(o,n,(e=>$t(e)+(parseInt(Rt(e,"margin-top"),10)||0)+(parseInt(Rt(e,"margin-bottom"),10)||0))(t)+"px")}else Ht(t,"width"),Ht(o,n)}))},hM=(e,t)=>{t?(Oa(e,dM.fadeOutClass),Ta(e,[dM.transitionClass,dM.fadeInClass])):(Oa(e,dM.fadeInClass),Ta(e,[dM.fadeOutClass,dM.transitionClass]))},fM=(e,t)=>{const o=ze(e.getContainer());t?(ka(o,uM),Oa(o,mM)):(ka(o,mM),Oa(o,uM))},bM=(e,t)=>{const o=rn(),n=t.getSink,s=e=>{n().each((t=>e(t.element)))},r=t=>{e.inline||pM(t),fM(e,Ei.isDocked(t)),t.getSystem().broadcastOn([Au()],{}),n().each((e=>e.getSystem().broadcastOn([Au()],{})))},a=e.inline?[]:[gc.config({channels:{[cM()]:{onReceive:pM}}})];return[Rh.config({}),Ei.config({contextual:{lazyContext:t=>{const o=$t(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer();return A.from(n).map((n=>{const s=Qo(ze(n));return Rb(e,t.element).fold((()=>{const e=s.height-o,n=s.y+(gM(t,"top")?0:o);return Jo(s.x,n,s.width,e)}),(e=>{const n=en(s,Nb(e)),r=gM(t,"top")?n.y:n.y+o;return Jo(n.x,r,n.width,n.height-o)}))}))},onShow:()=>{s((e=>hM(e,!0)))},onShown:e=>{s((e=>Ea(e,[dM.transitionClass,dM.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=tt(t);vc(o).filter((e=>!Ze(t,e))).filter((t=>Ze(t,ze(o.dom.body))||et(e,t))).each((()=>hc(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>yc(e).orThunk((()=>t().toOptional().bind((e=>yc(e.element))))))(e.element,n).fold(o.clear,o.set),s((e=>hM(e,!1)))},onHidden:()=>{s((e=>Ea(e,[dM.transitionClass])))},...dM},lazyViewport:t=>Rb(e,t.element).fold((()=>{const o=tn(),n=Xf(e),s=o.y+(gM(t,"top")?n:0),r=o.height-(gM(t,"bottom")?n:0);return{bounds:Jo(o.x,s,o.width,r),optScrollEnv:A.none()}}),(e=>({bounds:Nb(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Xt(e.element).top})}))),modes:[t.header.getDockingMode()],onDocked:r,onUndocked:r}),...a]};var vM=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each(Ei.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(pM)})),e.on("SkinLoaded",(()=>{o().each((e=>{Ei.isDocked(e)?Ei.reset(e):Ei.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each(Ei.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{Ei.refresh(t);const o=t.element;gp(o)&&((e,t)=>{const o=tt(t),n=st(t).dom.innerHeight,s=Wo(o),r=ze(e.elm),a=Zo(r),i=jt(r),l=a.y,c=l+i,d=Xt(t),u=jt(t),m=d.top,g=m+u,p=Math.abs(m-s.top)<2,h=Math.abs(g-(s.top+n))<2;if(p&&l<g)jo(s.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;jo(s.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{fM(e,!1)}))},isDocked:e=>e().map(Ei.isDocked).getOr(!1),getBehaviours:bM});const yM=Nn([Jy,ls("items",Ln([Hn([Qy,hs("items",jn)]),jn]))].concat(Ex)),xM=[ys("text"),ys("tooltip"),ys("icon"),Os("search",!1,Ln([$n,Nn([ys("placeholder")])],(e=>d(e)?e?A.some({placeholder:A.none()}):A.none():A.some(e)))),ms("fetch"),Ms("onSetup",(()=>b))],wM=Nn([Jy,...xM]),SM=e=>Qn("menubutton",wM,e),kM=Nn([Jy,gx,mx,dx,fx,sx,lx,Es("presets","normal",["normal","color","listpreview"]),wx(1),ax,ix]);var CM=Xm({factory:(e,t)=>{const o={focus:xh.focusIn,setMenus:(e,o)=>{const n=L(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())}},n=SM(o).mapError((e=>ts(e))).getOrDie();return WT(n,"tox-mbtn",t.backstage,A.some("menuitem"))}));Ah.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:ma([Ah.config({}),Mh("menubar-events",[ea((t=>{e.onSetup(t)})),Gr(Js(),((e,t)=>{_l(e.element,".tox-mbtn--active").each((o=>{Tl(t.event.target,".tox-mbtn").each((t=>{Ze(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{wk.expand(e),wk.close(o),Rh.focus(e)}))}))}))}))})),Gr(Mr(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{wk.isOpen(o)&&(wk.expand(e),wk.close(o))}))}))}))]),xh.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),A.some(!0))}),Ub.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[is("dom"),is("uid"),is("onEscape"),is("backstage"),Cs("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const OM="container",_M=[Yu("slotBehaviours",[])],TM=e=>"<alloy.field."+e+">",EM=(e,t)=>{const o=t=>Vm(e),n=(t,o)=>(n,s)=>Rm(n,e,s).map((e=>t(e,s))).getOr(o),s=(e,t)=>"true"!==_t(e.element,"aria-hidden"),r=n(s,!1),a=n(((e,t)=>{if(s(e)){const o=e.element;Bt(o,"display","none"),Ct(o,"aria-hidden","true"),Lr(e,Dr(),{name:t,visible:!1})}})),i=(e=>(t,o)=>{V(o,(o=>e(t,o)))})(a),l=n(((e,t)=>{if(!s(e)){const o=e.element;Ht(o,"display"),At(o,"aria-hidden"),Lr(e,Dr(),{name:t,visible:!0})}})),c={getSlotNames:o,getSlot:(t,o)=>Rm(t,e,o),isShowing:r,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:l};return{uid:e.uid,dom:e.dom,components:t,behaviours:Xu(e.slotBehaviours),apis:c}},AM=le({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>Gi(e))),MM={...AM,sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),Mm(OM,TM(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=L(n,(e=>wm({name:e,pname:TM(e)})));return $m(OM,_M,s,EM,o)}},DM=Nn([mx,gx,Ms("onShow",b),Ms("onHide",b),lx]),BM=e=>({element:()=>e.element.dom}),IM=(e,t)=>{const o=L(re(t),(e=>{const o=t[e],n=Zn((e=>Qn("sidebar",DM,e))(o));return{name:e,getApi:BM,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return L(o,(t=>{const n=on(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:rw([hw(t,n),fw(t,n),Gr(Dr(),((e,t)=>{const n=t.event,s=j(o,(e=>e.name===n.name));s.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},FM=e=>MM.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:IM(t,e),slotBehaviours:rw([ea((e=>MM.hideAllSlots(e)))])}))),RM=(e,t)=>{Ct(e,"role",t)},NM=e=>eg.getCurrent(e).bind((e=>PT.isGrowing(e)||PT.hasGrown(e)?eg.getCurrent(e).bind((e=>j(MM.getSlotNames(e),(t=>MM.isShowing(e,t))))):A.none())),zM=Bi("FixSizeEvent"),LM=Bi("AutoSizeEvent");var VM=Object.freeze({__proto__:null,block:(e,t,o,n)=>{Ct(e.element,"aria-busy",!0);const s=t.getRoot(e).getOr(e),r=ma([xh.config({mode:"special",onTab:()=>A.some(!0),onShiftTab:()=>A.some(!0)}),Rh.config({})]),a=n(s,r),i=s.getSystem().build(a);Ah.append(s,fl(i)),i.hasConfigured(xh)&&t.focus&&xh.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>Ah.remove(s,i)))},unblock:(e,t,o)=>{At(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()},isBlocked:(e,t,o)=>o.isBlocked()}),HM=[Ms("getRoot",A.none),As("focus",!0),xi("onBlock"),xi("onUnblock")];const PM=pa({fields:HM,name:"blocking",apis:VM,state:Object.freeze({__proto__:null,init:()=>{const e=nn((e=>e.destroy()));return ua({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})}),UM=e=>eg.getCurrent(e).each((e=>hc(e.element,!0))),WM=(e,t,o)=>{const n=on(!1),s=rn(),r=o=>{var s;n.get()&&(!(e=>"focusin"===e.type)(s=o)||!(s.composed?te(s.composedPath()):A.from(s.target)).map(ze).filter($e).exists((e=>_a(e,"mce-pastebin"))))&&(o.preventDefault(),UM(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",r),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&r(e)}))}));const a=s=>{s!==n.get()&&(n.set(s),((e,t,o,n)=>{const s=t.element;if(((e,t)=>{const o="tabindex",n=`data-mce-${o}`;A.from(e.iframeElement).map(ze).each((e=>{t?(Tt(e,o).each((t=>Ct(e,n,t))),Ct(e,o,-1)):(At(e,o),Tt(e,n).each((t=>{Ct(e,o,t),At(e,n)})))}))})(e,o),o)PM.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:Lb('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),Ht(s,"display"),At(s,"aria-hidden"),e.hasFocus()&&UM(t);else{const o=eg.getCurrent(t).exists((e=>bc(e.element)));PM.unblock(t),Bt(s,"display","none"),Ct(s,"aria-hidden","true"),o&&e.focus()}})(e,t(),s,o.providers),((e,t)=>{e.dispatch("AfterProgressState",{state:t})})(e,s))};e.on("ProgressState",(t=>{if(s.on(clearTimeout),h(t.time)){const o=Cf.setEditorTimeout(e,(()=>a(t.state)),t.time);s.set(o)}else a(t.state),s.clear()}))},jM=(e,t,o)=>({within:e,extra:t,withinWidth:o}),$M=(e,t,o)=>{const n=W(e,((e,t)=>((e,t)=>{const n=o(e);return A.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(x(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,s=P(n,(e=>e.finish<=t)),r=U(s,((e,t)=>e+t.width),0);return{within:s,extra:n.slice(s.length),withinWidth:r}},GM=e=>L(e,(e=>e.element)),qM=(e,t)=>{const o=L(t,(e=>fl(e)));nM.setGroups(e,o)},YM=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const s=Nm(e,t,"primary"),r=tk.getCoupled(e,"overflowGroup");Bt(s.element,"visibility","hidden");const a=n.concat([r]),i=se(a,(e=>yc(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),qM(s,a);const l=((e,t,o,n)=>{const s=((e,t,o)=>{const n=$M(t,e,o);return 0===n.extra.length?A.some(n):A.none()})(e,t,o).getOrThunk((()=>$M(t,e-o(n),o))),r=s.within,a=s.extra,i=s.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=GM(e.concat(t));return jM(n,[],o)})(r,a,i):a.length>=1?((e,t,o,n)=>{const s=GM(e).concat([o]);return jM(s,GM(t),n)})(r,a,n,i):((e,t,o)=>jM(GM(e),[],o))(r,0,i)})(Qt(s.element),t.builtGroups.get(),(e=>Math.ceil(e.element.dom.getBoundingClientRect().width)),r);0===l.extra.length?(Ah.remove(s,r),o([])):(qM(s,l.within),o(l.extra)),Ht(s.element,"visibility"),Pt(s.element),i.each(Rh.focus)},XM=x([Yu("splitToolbarBehaviours",[tk]),rs("builtGroups",(()=>on([])))]),KM=x([vi(["overflowToggledClass"]),ws("getOverflowBounds"),is("lazySink"),rs("overflowGroups",(()=>on([]))),xi("onOpened"),xi("onClosed")].concat(XM())),JM=x([wm({factory:nM,schema:tM(),name:"primary"}),Sm({schema:tM(),name:"overflow"}),Sm({name:"overflow-button"}),Sm({name:"overflow-group"})]),QM=x(((e,t)=>{((e,t)=>{const o=Jt.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);Bt(e,"max-width",o+"px")})(e,Math.floor(t))})),ZM=x([vi(["toggledClass"]),is("lazySink"),ms("fetch"),ws("getBounds"),ks("fireDismissalEventInstead",[Cs("event",Er())]),ed(),xi("onToggled")]),eD=x([Sm({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:ma([Wh.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1,onToggled:e.onToggled})])})}),Sm({factory:nM,schema:tM(),name:"toolbar",overrides:e=>({toolbarBehaviours:ma([xh.config({mode:"cyclic",onEscape:t=>(Rm(t,e,"button").each(Rh.focus),A.none())})])})})]),tD=rn(),oD=(e,t)=>{const o=tk.getCoupled(e,"toolbarSandbox");Tu.isOpen(o)?Tu.close(o):Tu.open(o,t.toolbar())},nD=(e,t,o,n)=>{const s=o.getBounds.map((e=>e())),r=o.lazySink(e).getOrDie();tu.positionWithinBounds(r,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:QM()}}},s)},sD=(e,t,o,n,s)=>{nM.setGroups(t,s),nD(e,t,o,n),Wh.on(e)},rD=Km({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({...zb.sketch({...n.button(),action:e=>{oD(e,n)},buttonBehaviours:Qu({dump:n.button().buttonBehaviours},[tk.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=Al();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:ma([xh.config({mode:"special",onEscape:e=>(Tu.close(e),A.some(!0))}),Tu.config({onOpen:(s,r)=>{const a=tD.get().getOr(!1);o.fetch().get((s=>{sD(e,r,o,t.layouts,s),n.link(e.element),a||xh.focusIn(r)}))},onClose:()=>{Wh.off(e),tD.get().getOr(!1)||Rh.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>Ml(o,n)||Ml(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),gc.config({channels:{...Bu({isExtraPart:T,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...Fu({doReposition:()=>{Tu.getState(tk.getCoupled(e,"toolbarSandbox")).each((n=>{nD(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{Tu.getState(tk.getCoupled(t,"toolbarSandbox")).each((s=>{sD(t,s,e,o.layouts,n)}))},reposition:t=>{Tu.getState(tk.getCoupled(t,"toolbarSandbox")).each((n=>{nD(t,n,e,o.layouts)}))},toggle:e=>{oD(e,n)},toggleWithoutFocusing:e=>{((e,t)=>{tD.set(!0),oD(e,t),tD.clear()})(e,n)},getToolbar:e=>Tu.getState(tk.getCoupled(e,"toolbarSandbox")),isOpen:e=>Tu.isOpen(tk.getCoupled(e,"toolbarSandbox"))}}),configFields:ZM(),partFields:eD(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggleWithoutFocusing(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),aD=x([is("items"),vi(["itemSelector"]),Yu("tgroupBehaviours",[xh])]),iD=x([Cm({name:"items",unit:"item"})]),lD=Km({name:"ToolbarGroup",configFields:aD(),partFields:iD(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Ku(e.tgroupBehaviours,[xh.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),cD=e=>L(e,(e=>fl(e))),dD=(e,t,o)=>{YM(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{rD.setGroups(e,cD(n))}))}))},uD=Km({name:"SplitFloatingToolbar",configFields:KM(),partFields:JM(),factory:(e,t,o,n)=>{const s=Vb(rD.sketch({fetch:()=>ak((t=>{t(cD(e.overflowGroups.get()))})),layouts:{onLtr:()=>[Ql,Jl],onRtl:()=>[Jl,Ql],onBottomLtr:()=>[ec,Zl],onBottomRtl:()=>[Zl,ec]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()},onToggled:(t,o)=>e[o?"onOpened":"onClosed"](t)}));return{uid:e.uid,dom:e.dom,components:t,behaviours:Ku(e.splitToolbarBehaviours,[tk.config({others:{overflowGroup:()=>lD.sketch({...n["overflow-group"](),items:[s.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(L(o,t.getSystem().build)),dD(t,s,e)},refresh:t=>dD(t,s,e),toggle:e=>{s.getOpt(e).each((e=>{rD.toggle(e)}))},toggleWithoutFocusing:e=>{s.getOpt(e).each(rD.toggleWithoutFocusing)},isOpen:e=>s.getOpt(e).map(rD.isOpen).getOr(!1),reposition:e=>{s.getOpt(e).each((e=>{rD.reposition(e)}))},getOverflow:e=>s.getOpt(e).bind(rD.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),mD=x([vi(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),xi("onOpened"),xi("onClosed")].concat(XM())),gD=x([wm({factory:nM,schema:tM(),name:"primary"}),wm({factory:nM,schema:tM(),name:"overflow",overrides:e=>({toolbarBehaviours:ma([PT.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{Rm(t,e,"overflow-button").each((e=>{Wh.off(e)})),e.onClosed(t)},onGrown:t=>{e.onOpened(t)},onStartGrow:t=>{Rm(t,e,"overflow-button").each(Wh.on)}}),xh.config({mode:"acyclic",onEscape:t=>(Rm(t,e,"overflow-button").each(Rh.focus),A.some(!0))})])})}),Sm({name:"overflow-button",overrides:e=>({buttonBehaviours:ma([Wh.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])})}),Sm({name:"overflow-group"})]),pD=(e,t,o)=>{Rm(e,t,"overflow-button").each((n=>{Rm(e,t,"overflow").each((s=>{if(hD(e,t),PT.hasShrunk(s)){const e=t.onOpened;t.onOpened=n=>{o||xh.focusIn(s),e(n),t.onOpened=e}}else{const e=t.onClosed;t.onClosed=s=>{o||Rh.focus(n),e(s),t.onClosed=e}}PT.toggleGrow(s)}))}))},hD=(e,t)=>{Rm(e,t,"overflow").each((o=>{YM(e,t,(e=>{const t=L(e,(e=>fl(e)));nM.setGroups(o,t)})),Rm(e,t,"overflow-button").each((e=>{PT.hasGrown(o)&&Wh.on(e)})),PT.refresh(o)}))},fD=Km({name:"SplitSlidingToolbar",configFields:mD(),partFields:gD(),factory:(e,t,o,n)=>{const s="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:Ku(e.splitToolbarBehaviours,[tk.config({others:{overflowGroup:e=>lD.sketch({...n["overflow-group"](),items:[zb.sketch({...n["overflow-button"](),action:t=>{zr(e,s)}})]})}}),Mh("toolbar-toggle-events",[Gr(s,(t=>{pD(t,e,!1)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=L(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),hD(t,e)},refresh:t=>hD(t,e),toggle:t=>{pD(t,e,!1)},toggleWithoutFocusing:t=>{pD(t,e,!0)},isOpen:t=>((e,t)=>Rm(e,t,"overflow").map(PT.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),bD=e=>{const t=e.title.fold((()=>({})),(e=>({attributes:{title:e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"],...t},components:[lD.parts.items({})],items:e.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled]), .tox-number-input:not([disabled])"},tgroupBehaviours:ma([Ub.config({}),Rh.config({})])}},vD=e=>lD.sketch(bD(e)),yD=(e,t)=>{const o=ea((t=>{const o=L(e.initGroups,vD);nM.setGroups(t,o)}));return ma([gw(e.providers.isDisabled),dw(),xh.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),Mh("toolbar-events",[o])])},xD=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":bD({title:A.none(),items:[]}),"overflow-button":hE({name:"more",icon:A.some("more-drawer"),enabled:!0,tooltip:A.some("Reveal or hide additional toolbar items"),primary:!1,buttonType:A.none(),borderless:!1},A.none(),e.providers,[],"overflow-button")},splitToolbarBehaviours:yD(e,t)}},wD=e=>{const t=xD(e),o=uD.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return uD.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=Zo(t),n=nt(t),s=Zo(n),r=Math.max(n.dom.scrollHeight,s.height);return Jo(o.x+4,s.y,o.width-8,r)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>e.onToggled(t,!0),onClosed:t=>e.onToggled(t,!1)})},SD=e=>{const t=fD.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=fD.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=xD(e);return fD.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>{t.getSystem().broadcastOn([cM()],{type:"opened"}),e.onToggled(t,!0)},onClosed:t=>{t.getSystem().broadcastOn([cM()],{type:"closed"}),e.onToggled(t,!1)}})},kD=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return nM.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===Sf.scrolling?["tox-toolbar--scrolling"]:[])},components:[nM.parts.groups({})],toolbarBehaviours:yD(e,t)})},CD=[dx,mx,ys("tooltip"),Es("buttonType","secondary",["primary","secondary"]),As("borderless",!1),ms("onAction")],OD={button:[...CD,ex,us("type",["button"])],togglebutton:[...CD,As("active",!1),us("type",["togglebutton"])]},_D=[us("type",["group"]),Ds("buttons",[],os("type",OD))],TD=os("type",{...OD,group:_D}),ED=Nn([Ds("buttons",[],TD),ms("onShow"),ms("onHide")]),AD=(e,t)=>((e,t)=>{var o,n;const s="togglebutton"===e.type,r=e.icon.map((e=>sT(e,t.icons))).map(Vb),a={...e,name:s?e.text.getOr(e.icon.getOr("")):null!==(o=e.text)&&void 0!==o?o:e.icon.getOr(""),primary:"primary"===e.buttonType,buttonType:A.from(e.buttonType),tooltip:e.tooltip,icon:e.icon,enabled:!0,borderless:e.borderless},i=fE(null!==(n=e.buttonType)&&void 0!==n?n:"secondary"),l=s?e.text.map(t.translate):A.some(t.translate(e.text)),c=l.map(ul),d=a.tooltip.or(l).map((e=>({"aria-label":t.translate(e)}))).getOr({}),u=r.map((e=>e.asSpec())),m=yw([u,c]),g=e.icon.isSome()&&c.isSome(),p={tag:"button",classes:i.concat(...e.icon.isSome()&&!g?["tox-button--icon"]:[]).concat(...g?["tox-button--icon-and-text"]:[]).concat(...e.borderless?["tox-button--naked"]:[]).concat(..."togglebutton"===e.type&&e.active?["tox-button--enabled"]:[]),attributes:d},h=pE(a,A.some((o=>{const n=e=>{r.map((n=>n.getOpt(o).each((o=>{Ah.set(o,[sT(e,t.icons)])}))))};return s?e.onAction({setIcon:n,setActive:e=>{const t=o.element;e?(ka(t,"tox-button--enabled"),Ct(t,"aria-pressed",!0)):(Oa(t,"tox-button--enabled"),At(t,"aria-pressed"))},isActive:()=>_a(o.element,"tox-button--enabled")}):"button"===e.type?e.onAction({setIcon:n}):void 0})),[],p,m,e.tooltip,t);return zb.sketch(h)})(e,t),MD=Bo().deviceType,DD=MD.isPhone(),BD=MD.isTablet();var ID=Km({name:"silver.View",configFields:[is("viewConfig")],partFields:[km({factory:{sketch:e=>{let t=!1;const o=L(e.buttons,(o=>"group"===o.type?(t=!0,((e,t)=>({dom:{tag:"div",classes:["tox-view__toolbar__group"]},components:L(e.buttons,(e=>AD(e,t)))}))(o,e.providers)):AD(o,e.providers)));return{uid:e.uid,dom:{tag:"div",classes:[t?"tox-view__toolbar":"tox-view__header",...DD||BD?["tox-view--mobile","tox-view--scrolling"]:[]]},behaviours:ma([Rh.config({}),xh.config({mode:"flow",selector:"button, .tox-button",focusInside:qg.OnEnterOrSpaceMode})]),components:t?o:[Uk.sketch({dom:{tag:"div",classes:["tox-view__header-start"]},components:[]}),Uk.sketch({dom:{tag:"div",classes:["tox-view__header-end"]},components:o})]}}},schema:[is("buttons"),is("providers")],name:"header"}),km({factory:{sketch:e=>({uid:e.uid,behaviours:ma([Rh.config({}),Ub.config({})]),dom:{tag:"div",classes:["tox-view__pane"]}})},schema:[],name:"pane"})],factory:(e,t,o,n)=>{const s={getPane:t=>XA.getPart(t,e,"pane"),getOnShow:t=>e.viewConfig.onShow,getOnHide:t=>e.viewConfig.onHide};return{uid:e.uid,dom:e.dom,components:t,behaviours:ma([Rh.config({}),xh.config({mode:"cyclic",focusInside:qg.OnEnterOrSpaceMode})]),apis:s}},apis:{getPane:(e,t)=>e.getPane(t),getOnShow:(e,t)=>e.getOnShow(t),getOnHide:(e,t)=>e.getOnHide(t)}});const FD=(e,t,o)=>ge(t,((t,n)=>{const s=Zn(Qn("view",ED,t));return e.slot(n,ID.sketch({dom:{tag:"div",classes:["tox-view"]},viewConfig:s,components:[...s.buttons.length>0?[ID.parts.header({buttons:s.buttons,providers:o})]:[],ID.parts.pane({})]}))})),RD=(e,t)=>MM.sketch((o=>({dom:{tag:"div",classes:["tox-view-wrap__slot-container"]},components:FD(o,e,t),slotBehaviours:rw([ea((e=>MM.hideAllSlots(e)))])}))),ND=e=>j(MM.getSlotNames(e),(t=>MM.isShowing(e,t))),zD=(e,t,o)=>{MM.getSlot(e,t).each((e=>{ID.getPane(e).each((t=>{var n;o(e)((n=t.element.dom,{getContainer:x(n)}))}))}))};var LD=Xm({factory:(e,t)=>{const o={setViews:(e,o)=>{Ah.set(e,[RD(o,t.backstage.shared.providers)])},whichView:e=>eg.getCurrent(e).bind(ND),toggleView:(e,t,o,n)=>eg.getCurrent(e).exists((s=>{const r=ND(s),a=r.exists((e=>n===e)),i=MM.getSlot(s,n).isSome();return i&&(MM.hideAllSlots(s),a?((e=>{const t=e.element;Bt(t,"display","none"),Ct(t,"aria-hidden","true")})(e),t()):(o(),(e=>{const t=e.element;Ht(t,"display"),At(t,"aria-hidden")})(e),MM.showSlot(s,n),((e,t)=>{zD(e,t,ID.getOnShow)})(s,n)),r.each((e=>((e,t)=>zD(e,t,ID.getOnHide))(s,e)))),i}))};return{uid:e.uid,dom:{tag:"div",classes:["tox-view-wrap"],attributes:{"aria-hidden":"true"},styles:{display:"none"}},components:[],behaviours:ma([Ah.config({}),eg.config({find:e=>{const t=Ah.contents(e);return te(t)}})]),apis:o}},name:"silver.ViewWrapper",configFields:[is("backstage")],apis:{setViews:(e,t,o)=>e.setViews(t,o),toggleView:(e,t,o,n,s)=>e.toggleView(t,o,n,s),whichView:(e,t)=>e.whichView(t)}});const VD=KA.optional({factory:CM,name:"menubar",schema:[is("backstage")]}),HD=KA.optional({factory:{sketch:e=>eM.sketch({uid:e.uid,dom:e.dom,listBehaviours:ma([xh.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>kD({type:e.type,uid:Bi("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),A.some(!0))}),setupItem:(e,t,o,n)=>{nM.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[is("dom"),is("onEscape")]}),PD=KA.optional({factory:{sketch:e=>{const t=(e=>e.type===Sf.sliding?SD:e.type===Sf.floating?wD:kD)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),A.some(!0)),onToggled:(t,o)=>e.onToolbarToggled(o),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[is("dom"),is("onEscape"),is("getSink")]}),UD=KA.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?bM:aM;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:ma(o(t,e.sharedBackstage))}}},name:"header",schema:[is("dom")]}),WD=KA.optional({factory:{sketch:e=>({uid:e.uid,dom:e.dom,components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/tinymce-self-hosted-premium-features/?utm_campaign=self_hosted_upgrade_promo&utm_source=tiny&utm_medium=referral",rel:"noopener",target:"_blank","aria-hidden":"true"},classes:["tox-promotion-link"],innerHtml:"\u26a1\ufe0fUpgrade"}}]})},name:"promotion",schema:[is("dom")]}),jD=KA.optional({name:"socket",schema:[is("dom")]}),$D=KA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"presentation"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:ma([Ub.config({}),Rh.config({}),PT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{eg.getCurrent(e).each(MM.hideAllSlots),zr(e,LM)},onGrown:e=>{zr(e,LM)},onStartGrow:e=>{Lr(e,zM,{width:zt(e.element,"width").getOr("")})},onStartShrink:e=>{Lr(e,zM,{width:Qt(e.element)+"px"})}}),Ah.config({}),eg.config({find:e=>{const t=Ah.contents(e);return te(t)}})])}],behaviours:ma([y_(0),Mh("sidebar-sliding-events",[Gr(zM,((e,t)=>{Bt(e.element,"width",t.event.width)})),Gr(LM,((e,t)=>{Ht(e.element,"width")}))])])})},name:"sidebar",schema:[is("dom")]}),GD=KA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:ma([Ah.config({}),PM.config({focus:!1}),eg.config({find:e=>te(e.components())})]),components:[]})},name:"throbber",schema:[is("dom")]}),qD=KA.optional({factory:LD,name:"viewWrapper",schema:[is("backstage")]}),YD=KA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-editor-container"]},components:e.components})},name:"editorContainer",schema:[]});var XD=Km({name:"OuterContainer",factory:(e,t,o)=>{let n=!1;const s=e=>{Cl(e,".tox-statusbar").each((e=>{"none"===Rt(e,"display")&&"true"===_t(e,"aria-hidden")?(Ht(e,"display"),At(e,"aria-hidden")):(Bt(e,"display","none"),Ct(e,"aria-hidden","true"))}))},a={getSocket:t=>XA.getPart(t,e,"socket"),setSidebar:(t,o,n)=>{XA.getPart(t,e,"sidebar").each((e=>((e,t,o)=>{eg.getCurrent(e).each((n=>{Ah.set(n,[FM(t)]);const s=null==o?void 0:o.toLowerCase();r(s)&&be(t,s)&&eg.getCurrent(n).each((t=>{MM.showSlot(t,s),PT.immediateGrow(n),Ht(n.element,"width"),RM(e.element,"region")}))}))})(e,o,n)))},toggleSidebar:(t,o)=>{XA.getPart(t,e,"sidebar").each((e=>((e,t)=>{eg.getCurrent(e).each((o=>{eg.getCurrent(o).each((n=>{PT.hasGrown(o)?MM.isShowing(n,t)?(PT.shrink(o),RM(e.element,"presentation")):(MM.hideAllSlots(n),MM.showSlot(n,t),RM(e.element,"region")):(MM.hideAllSlots(n),MM.showSlot(n,t),PT.grow(o),RM(e.element,"region"))}))}))})(e,o)))},whichSidebar:t=>XA.getPart(t,e,"sidebar").bind(NM).getOrNull(),getHeader:t=>XA.getPart(t,e,"header"),getToolbar:t=>XA.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{XA.getPart(t,e,"toolbar").each((e=>{const t=L(o,vD);e.getApis().setGroups(e,t)}))},setToolbars:(t,o)=>{XA.getPart(t,e,"multiple-toolbar").each((e=>{const t=L(o,(e=>L(e,vD)));eM.setItems(e,t)}))},refreshToolbar:t=>{XA.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{XA.getPart(t,e,"toolbar").each((e=>{Se(e.getApis().toggle,(t=>t(e)))}))},toggleToolbarDrawerWithoutFocusing:t=>{XA.getPart(t,e,"toolbar").each((e=>{Se(e.getApis().toggleWithoutFocusing,(t=>t(e)))}))},isToolbarDrawerToggled:t=>XA.getPart(t,e,"toolbar").bind((e=>A.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>XA.getPart(t,e,"throbber"),focusToolbar:t=>{XA.getPart(t,e,"toolbar").orThunk((()=>XA.getPart(t,e,"multiple-toolbar"))).each((e=>{xh.focusIn(e)}))},setMenubar:(t,o)=>{XA.getPart(t,e,"menubar").each((e=>{CM.setMenus(e,o)}))},focusMenubar:t=>{XA.getPart(t,e,"menubar").each((e=>{CM.focus(e)}))},setViews:(t,o)=>{XA.getPart(t,e,"viewWrapper").each((e=>{LD.setViews(e,o)}))},toggleView:(t,o)=>XA.getPart(t,e,"viewWrapper").exists((e=>LD.toggleView(e,(()=>a.showMainView(t)),(()=>a.hideMainView(t)),o))),whichView:t=>XA.getPart(t,e,"viewWrapper").bind(LD.whichView).getOrNull(),hideMainView:t=>{n=a.isToolbarDrawerToggled(t),n&&a.toggleToolbarDrawer(t),XA.getPart(t,e,"editorContainer").each((e=>{const t=e.element;s(t),Bt(t,"display","none"),Ct(t,"aria-hidden","true")}))},showMainView:t=>{n&&a.toggleToolbarDrawer(t),XA.getPart(t,e,"editorContainer").each((e=>{const t=e.element;s(t),Ht(t,"display"),At(t,"aria-hidden")}))}};return{uid:e.uid,dom:e.dom,components:t,apis:a,behaviours:e.behaviours}},configFields:[is("dom"),is("behaviours")],partFields:[UD,VD,PD,HD,jD,$D,WD,GD,qD,YD],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o,n)=>{e.setSidebar(t,o,n)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{e.setToolbar(t,o)},setToolbars:(e,t,o)=>{e.setToolbars(t,o)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},toggleToolbarDrawerWithoutFocusing:(e,t)=>{e.toggleToolbarDrawerWithoutFocusing(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)},setViews:(e,t,o)=>{e.setViews(t,o)},toggleView:(e,t,o)=>e.toggleView(t,o),whichView:(e,t)=>e.whichView(t)}});const KD={file:{title:"File",items:"newdocument restoredraft | preview | importword exportpdf exportword | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code revisionhistory | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed inserttemplate codesample inserttable accordion math | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents footnotes | mergetags | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"aidialog aishortcuts | spellchecker spellcheckerlanguage | autocorrect capitalization | a11ycheck code typography wordcount addtemplate"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},JD=e=>e.split(" "),QD=(e,t)=>{const o={...KD,...t.menus},n=re(t.menus).length>0,s=void 0===t.menubar||!0===t.menubar?JD("file edit view insert format tools table help"):JD(!1===t.menubar?"":t.menubar),a=P(s,(e=>{const o=be(KD,e);return n?o||fe(t.menus,e).exists((e=>be(e,"items"))):o})),i=L(a,(n=>{const s=o[n];return((e,t,o)=>{const n=Uf(o).split(/[ ,]/);return{text:e.title,getItems:()=>q(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||R(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:s.title,items:JD(s.items)},t,e)}));return P(i,(e=>e.getItems().length>0&&R(e.getItems(),(e=>r(e)||"separator"!==e.type))))},ZD=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),eB=(e,t,o,n)=>(e.on("remove",(()=>n.unloadRawCss(t))),n.loadRawCss(t,o)),tB=async(e,t)=>{const o="ui/"+vb(e).getOr("default")+"/skin.css",n=tinymce.Resource.get(o);if(!r(n)){const o=e.editorManager.suffix;return ZD(e,t+`/skin${o}.css`,e.ui.styleSheetLoader)}eB(e,o,n,e.ui.styleSheetLoader)},oB=async(e,t)=>{var o;if(o=ze(e.getElement()),vt(o).isSome()){const o="ui/"+vb(e).getOr("default")+"/skin.shadowdom.css",n=tinymce.Resource.get(o);if(!r(n)){const o=e.editorManager.suffix;return ZD(e,t+`/skin.shadowdom${o}.css`,Of.DOM.styleSheetLoader)}eB(e,o,n,Of.DOM.styleSheetLoader)}},nB=(e,t)=>(async(e,t)=>{const o=()=>{const o=bb(t),n=t.editorManager.suffix;o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+`${n}.css`)};vb(t).fold(o,(n=>{const s="ui/"+n+(e?"/content.inline":"/content")+".css",a=tinymce.Resource.get(s);r(a)?eB(t,s,a,t.ui.styleSheetLoader):o()}));const n=bb(t);if(!hb(t)&&r(n))return Promise.all([tB(t,n),oB(t,n)]).then()})(e,t).then((e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}})(t),((e,t)=>()=>((e,t)=>{e.dispatch("SkinLoadError",t)})(e,{message:"Skin could not be loaded"}))(t)),sB=k(nB,!1),rB=k(nB,!0),aB=(e,t,o)=>De(o)?e.translate(t):e.translate([t,e.translate(o)]),iB=(e,t)=>{const o=(o,s,r,a)=>{const i=e.shared.providers.translate(o.title);if("separator"===o.type)return A.some({type:"separator",text:i});if("submenu"===o.type){const e=q(o.getStyleItems(),(e=>n(e,s,a)));return 0===s&&e.length<=0?A.none():A.some({type:"nestedmenuitem",text:i,enabled:e.length>0,getSubmenuItems:()=>q(o.getStyleItems(),(e=>n(e,s,a)))})}return A.some({type:"togglemenuitem",text:i,icon:o.icon,active:o.isSelected(a),enabled:!r,onAction:t.onAction(o),...o.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},n=(e,n,s)=>{const r="formatter"===e.type&&t.isInvalid(e);return 0===n?r?[]:o(e,n,!1,s).toArray():o(e,n,r,s).toArray()},s=e=>{const o=t.getCurrentValue(),s=t.shouldHide?0:1;return q(e,(e=>n(e,s,o)))};return{validateItems:s,getFetch:(e,t)=>(o,n)=>{const r=t(),a=s(r);n(mT(a,dy.CLOSE_ON_EXECUTE,e,{isHorizontalMenu:!1,search:A.none()}))}}},lB=(e,t)=>{const o=t.dataset,n="basic"===o.type?()=>L(o.data,(e=>wA(e,t.isSelectedFor,t.getPreviewFor))):o.getData;return{items:iB(e,t),getStyleItems:n}},cB=(e,t,o,n,s,r)=>{const{items:a,getStyleItems:i}=lB(t,o),l=on(o.tooltip);return lT({text:o.icon.isSome()?A.none():o.text,icon:o.icon,ariaLabel:A.some(o.tooltip),tooltip:A.none(),role:A.none(),fetch:a.getFetch(t,i),onSetup:t=>{const r=o=>t.setTooltip(aB(e,n(o.value),o.value));return e.on(s,r),sS(iS(e,"NodeChange",(t=>{const n=t.getComponent();o.updateText(n),pg.set(t.getComponent(),!e.selection.isEditable())}))(t),(()=>e.off(s,r)))},getApi:e=>({getComponent:x(e),setTooltip:o=>{const n=t.shared.providers.translate(o);Ct(e.element,"aria-label",n),l.set(o)}}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[Zb.config({...t.shared.providers.tooltips.getConfig({tooltipText:t.shared.providers.translate(o.tooltip),onShow:e=>{if(o.tooltip!==l.get()){const o=t.shared.providers.translate(l.get());Zb.setComponents(e,t.shared.providers.tooltips.getComponents({tooltipText:o}))}}})})]},"tox-tbtn",t.shared,r)};var dB;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(dB||(dB={}));const uB=(e,t,o)=>{const n=(s=((e,t)=>t===dB.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),L(s,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var s;return{type:"basic",data:n}},mB=x("Alignment {0}"),gB="left",pB=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],hB=e=>{const t={type:"basic",data:pB};return{tooltip:aB(e,mB(),gB),text:A.none(),icon:A.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:e=>A.none,onAction:t=>()=>j(pB,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=j(pB,(t=>e.formatter.match(t.format))).fold(x(gB),(e=>e.title.toLowerCase()));Lr(t,iT,{icon:`align-${o}`}),((e,t)=>{e.dispatch("AlignTextUpdate",t)})(e,{value:o})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},fB=(e,t)=>{const o=t(),n=L(o,(e=>e.format));return A.from(e.formatter.closest(n)).bind((e=>j(o,(t=>t.format===e))))},bB=x("Block {0}"),vB="Paragraph",yB=e=>{const t=uB(e,"block_formats",dB.SemiColon);return{tooltip:aB(e,bB(),vB),text:A.some(vB),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:lS(e),updateText:o=>{const n=fB(e,(()=>t.data)).fold(x(vB),(e=>e.title));Lr(o,aT,{text:n}),((e,t)=>{e.dispatch("BlocksTextUpdate",t)})(e,{value:n})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},xB=x("Font {0}"),wB="System Font",SB=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],kB=e=>{const t=e.split(/\s*,\s*/);return L(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},CB=(e,t)=>t.length>0&&Y(t,(t=>e.indexOf(t.toLowerCase())>-1)),OB=e=>{const t=()=>{const t=e=>e?kB(e)[0]:"",n=e.queryCommandValue("FontName"),s=o.data,r=n?n.toLowerCase():"",a=pb(e),i=j(s,(e=>{const o=e.format;return o.toLowerCase()===r||t(o).toLowerCase()===t(r).toLowerCase()})).orThunk((()=>ke(((e,t)=>{if(0===e.indexOf("-apple-system")||t.length>0){const o=kB(e.toLowerCase());return CB(o,SB)||CB(o,t)}return!1})(r,a),{title:wB,format:r})));return{matchOpt:i,font:n}},o=uB(e,"font_family_formats",dB.SemiColon);return{tooltip:aB(e,xB(),wB),text:A.some(wB),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=t();return e},getPreviewFor:e=>()=>A.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:o=>{const{matchOpt:n,font:s}=t(),r=n.fold(x(s),(e=>e.title));Lr(o,aT,{text:r}),((e,t)=>{e.dispatch("FontFamilyTextUpdate",t)})(e,{value:r})},dataset:o,shouldHide:!1,isInvalid:T}},_B={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},TB=(()=>{const e="[0-9]+",t="[eE][+-]?"+e,o=e=>`(?:${e})?`,n=["Infinity",e+"\\."+o(e)+o(t),"\\."+e+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),EB=(e,t)=>A.from(TB.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>R(t,(t=>R(_B[t],(t=>e===t)))))(n,t)?A.some({value:o,unit:n}):A.none()})),AB={tab:x(9),escape:x(27),enter:x(13),backspace:x(8),delete:x(46),left:x(37),up:x(38),right:x(39),down:x(40),space:x(32),home:x(36),end:x(35),pageUp:x(33),pageDown:x(34)},MB=x("Font size {0}"),DB="12pt",BB={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},IB={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},FB=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":fe(IB,e).getOr(e),RB=e=>fe(BB,e).getOr(""),NB=e=>{const t=()=>{let t=A.none();const o=n.data,s=e.queryCommandValue("FontSize");if(s)for(let e=3;t.isNone()&&e>=0;e--){const n=FB(s,e),r=RB(n);t=j(o,(e=>e.format===s||e.format===n||e.format===r))}return{matchOpt:t,size:s}},o=x(A.none),n=uB(e,"font_size_formats",dB.Space);return{tooltip:aB(e,MB(),DB),text:A.some(DB),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:o=>{const{matchOpt:n,size:s}=t(),r=n.fold(x(s),(e=>e.title));Lr(o,aT,{text:r}),((e,t)=>{e.dispatch("FontSizeTextUpdate",t)})(e,{value:r})},dataset:n,shouldHide:!1,isInvalid:T}},zB=e=>De(e)?"Formats":"Format {0}",LB=(e,t)=>{const o="Formats";return{tooltip:aB(e,zB(""),""),text:A.some(o),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:lS(e),updateText:t=>{const n=e=>fA(e)?q(e.items,n):bA(e)?[{title:e.title,format:e.format}]:[],s=q(xA(e),n),r=fB(e,x(s)).fold(x({title:o,tooltipLabel:""}),(e=>({title:e.title,tooltipLabel:e.title})));Lr(t,aT,{text:r.title}),((e,t)=>{e.dispatch("StylesTextUpdate",t)})(e,{value:r.tooltipLabel})},shouldHide:Hf(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}},VB=x([is("toggleClass"),is("fetch"),Si("onExecute"),Cs("getHotspot",A.some),Cs("getAnchorOverrides",x({})),ed(),Si("onItemExecute"),fs("lazySink"),is("dom"),xi("onOpen"),Yu("splitDropdownBehaviours",[tk,xh,Rh]),Cs("matchWidth",!1),Cs("useMinWidth",!1),Cs("eventOrder",{}),fs("role"),fs("listRole")].concat(vk())),HB=wm({factory:zb,schema:[is("dom")],name:"arrow",defaults:()=>({buttonBehaviours:ma([Rh.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each(Vr)},buttonBehaviours:ma([Wh.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])})}),PB=wm({factory:zb,schema:[is("dom")],name:"button",defaults:()=>({buttonBehaviours:ma([Rh.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each((o=>{e.onExecute(o,t)}))}})}),UB=x([HB,PB,km({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[is("text")],name:"aria-descriptor"}),Sm({schema:[bi()],name:"menu",defaults:e=>({onExecute:(t,o)=>{t.getSystem().getByUid(e.uid).each((n=>{e.onItemExecute(n,t,o)}))}})}),ck()]),WB=Km({name:"SplitDropdown",configFields:VB(),partFields:UB(),factory:(e,t,o,n)=>{const s=e=>{eg.getCurrent(e).each((e=>{Cg.highlightFirst(e),xh.focusIn(e)}))},r=t=>{gk(e,w,t,n,s,vf.HighlightMenuAndItem).get(b)},a=t=>{const o=Nm(t,e,"button");return Vr(o),A.some(!0)},i={...Wr([ea(((t,o)=>{Rm(t,e,"aria-descriptor").each((e=>{const o=Bi("aria");Ct(e.element,"id",o),Ct(t.element,"aria-describedby",o)}))}))]),...$h(A.some(r))},l={repositionMenus:e=>{Wh.isOn(e)&&bk(e)}};return{uid:e.uid,dom:e.dom,components:t,apis:l,eventOrder:{...e.eventOrder,[hr()]:["disabling","toggling","alloy.base.behaviour"]},events:i,behaviours:Ku(e.splitDropdownBehaviours,[tk.config({others:{sandbox:t=>{const o=Nm(t,e,"arrow");return fk(e,t,{onOpen:()=>{Wh.on(o),Wh.on(t)},onClose:()=>{Wh.off(o),Wh.off(t)}})}}}),xh.config({mode:"special",onSpace:a,onEnter:a,onDown:e=>(r(e),A.some(!0))}),Rh.config({}),Wh.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:e.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:(e,t)=>e.repositionMenus(t)}}),jB=e=>({isEnabled:()=>!pg.isDisabled(e),setEnabled:t=>pg.set(e,!t),setText:t=>Lr(e,aT,{text:t}),setIcon:t=>Lr(e,iT,{icon:t})}),$B=e=>({setActive:t=>{Wh.set(e,t)},isActive:()=>Wh.isOn(e),isEnabled:()=>!pg.isDisabled(e),setEnabled:t=>pg.set(e,!t),setText:t=>Lr(e,aT,{text:t}),setIcon:t=>Lr(e,iT,{icon:t})}),GB=(e,t)=>e.map((e=>({"aria-label":t.translate(e)}))).getOr({}),qB=Bi("focus-button"),YB=(e,t,o,n,s,r)=>{const a=t.map((e=>Vb(rT(e,"tox-tbtn",s)))),i=e.map((e=>Vb(sT(e,s.icons))));return{dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:{...GB(o,s),...g(r)?{"data-mce-name":r}:{}}},components:yw([i.map((e=>e.asSpec())),a.map((e=>e.asSpec()))]),eventOrder:{[qs()]:["focusing","alloy.base.behaviour",Z_],[_r()]:[Z_,"toolbar-group-button-events"]},buttonBehaviours:ma([gw(s.isDisabled),dw(),Mh(Z_,[ea(((e,t)=>tT(e))),Gr(aT,((e,t)=>{a.bind((t=>t.getOpt(e))).each((e=>{Ah.set(e,[ul(s.translate(t.event.text))])}))})),Gr(iT,((e,t)=>{i.bind((t=>t.getOpt(e))).each((e=>{Ah.set(e,[sT(t.event.icon,s.icons)])}))})),Gr(qs(),((e,t)=>{t.event.prevent(),zr(e,qB)}))])].concat(n.getOr([])))}},XB=(e,t,o,n)=>{var s;const r=on(b),a=YB(e.icon,e.text,e.tooltip,A.none(),o,n);return zb.sketch({dom:a.dom,components:a.components,eventOrder:eT,buttonBehaviours:{...ma([Mh("toolbar-button-events",[(i={onAction:e.onAction,getApi:t.getApi},na(((e,t)=>{pw(i,e)((t=>{Lr(e,Q_,{buttonApi:t}),i.onAction(t)}))}))),hw(t,r),fw(t,r)]),...e.tooltip.map((t=>Zb.config(o.tooltips.getConfig({tooltipText:o.translate(t)+e.shortcut.map((e=>` (${Sw(e)})`)).getOr("")})))).toArray(),gw((()=>!e.enabled||o.isDisabled())),dw()].concat(t.toolbarButtonBehaviours)),[Z_]:null===(s=a.buttonBehaviours)||void 0===s?void 0:s[Z_]}});var i},KB=(e,t,o,n)=>XB(e,{toolbarButtonBehaviours:o.length>0?[Mh("toolbarButtonWith",o)]:[],getApi:jB,onSetup:e.onSetup},t,n),JB=(e,t,o,n)=>XB(e,{toolbarButtonBehaviours:[Ah.config({}),Wh.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[Mh("toolbarToggleButtonWith",o)]:[]),getApi:$B,onSetup:e.onSetup},t,n),QB=(e,t,o)=>n=>ak((e=>t.fetch(e))).map((s=>A.from(Ak(wn(US(Bi("menu-value"),s,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,dy.CLOSE_ON_EXECUTE,t.select.getOr(T),o),{movement:jS(t.columns,t.presets),menuBehaviours:rw("auto"!==t.columns?[]:[ea(((e,o)=>{sw(e,4,Sy(t.presets)).each((({numRows:t,numColumns:o})=>{xh.setGridSize(e,t,o)}))}))])}))))),ZB=[{name:"history",items:["undo","redo"]},{name:"ai",items:["aidialog","aishortcuts"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],eI=(e,t)=>(o,n,s,r)=>{const a=e(o).mapError((e=>ts(e))).getOrDie();return t(a,n,s,r)},tI={button:eI(Mx,((e,t,o,n)=>((e,t,o)=>KB(e,t,[],o))(e,t.shared.providers,n))),togglebutton:eI(Ix,((e,t,o,n)=>((e,t,o)=>JB(e,t,[],o))(e,t.shared.providers,n))),menubutton:eI(SM,((e,t,o,n)=>WT(e,"tox-tbtn",t,A.none(),!1,n))),splitbutton:eI((e=>Qn("SplitButton",kM,e)),((e,t,o,n)=>((e,t,o)=>{const n=on(e.tooltip.getOr("")),s=e=>({isEnabled:()=>!pg.isDisabled(e),setEnabled:t=>pg.set(e,!t),setIconFill:(t,o)=>{_l(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{Ct(e,"fill",o)}))},setActive:t=>{Ct(e.element,"aria-pressed",t),_l(e.element,"span").each((o=>{e.getSystem().getByDom(o).each((e=>Wh.set(e,t)))}))},isActive:()=>_l(e.element,"span").exists((t=>e.getSystem().getByDom(t).exists(Wh.isOn))),setText:t=>_l(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Lr(e,aT,{text:t}))))),setIcon:t=>_l(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Lr(e,iT,{icon:t}))))),setTooltip:o=>{const s=t.providers.translate(o);Ct(e.element,"aria-label",s),n.set(o)}}),r=on(b),a={getApi:s,onSetup:e.onSetup};return WB.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:{"aria-pressed":!1,...GB(e.tooltip,t.providers),...g(o)?{"data-mce-name":o}:{}}},onExecute:t=>{const o=s(t);o.isEnabled()&&e.onAction(o)},onItemExecute:(e,t,o)=>{},splitDropdownBehaviours:ma([mw(t.providers.isDisabled),dw(),Mh("split-dropdown-events",[ea(((e,t)=>tT(e))),Gr(qB,Rh.focus),hw(a,r),fw(a,r)]),vC.config({}),...e.tooltip.map((e=>Zb.config({...t.providers.tooltips.getConfig({tooltipText:t.providers.translate(e),onShow:o=>{if(n.get()!==e){const e=t.providers.translate(n.get());Zb.setComponents(o,t.providers.tooltips.getComponents({tooltipText:e}))}}})}))).toArray()]),eventOrder:{[_r()]:["alloy.base.behaviour","split-dropdown-events","tooltipping"],[Tr()]:["split-dropdown-events","tooltipping"]},toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:QB(s,e,t.providers),parts:{menu:Ey(0,e.columns,e.presets)},components:[WB.parts.button(YB(e.icon,e.text,A.none(),A.some([Wh.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),WB.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:oy("chevron-down",t.providers.icons)},buttonBehaviours:ma([mw(t.providers.isDisabled),dw(),ny()])}),WB.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})})(e,t.shared,n))),grouptoolbarbutton:eI((e=>Qn("GroupToolbarButton",yM,e)),((e,t,o,n)=>{const s=o.ui.registry.getAll().buttons,r={[Qc]:t.shared.header.isPositionedAtTop()?Jc.TopToBottom:Jc.BottomToTop};if(Wf(o)===Sf.floating)return((e,t,o,n,s)=>{const r=t.shared,a=on(b),i={toolbarButtonBehaviours:[],getApi:jB,onSetup:e.onSetup},l=[Mh("toolbar-group-button-events",[hw(i,a),fw(i,a)])];return rD.sketch({lazySink:r.getSink,fetch:()=>ak((t=>{t(L(o(e.items),vD))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:YB(e.icon,e.text,e.tooltip,A.some(l),r.providers,s),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t,(e=>nI(o,{buttons:s,toolbar:e,allowToolbarGroups:!1},t,A.none())),r,n);throw new Error("Toolbar groups are only supported when using floating toolbar mode")}))},oI={styles:(e,t)=>{const o={type:"advanced",...t.styles};return cB(e,t,LB(e,o),zB,"StylesTextUpdate","styles")},fontsize:(e,t)=>cB(e,t,NB(e),MB,"FontSizeTextUpdate","fontsize"),fontsizeinput:(e,t)=>((e,t,o,n)=>{let s=A.none();const r=iS(e,"NodeChange SwitchMode",(t=>{const n=t.getComponent();s=A.some(n),o.updateInputValue(n),pg.set(n,!e.selection.isEditable())})),a=e=>({getComponent:x(e)}),i=on(b),l=Bi("custom-number-input-events"),c=(e,t,n)=>{const r=s.map((e=>qu.getValue(e))).getOr(""),a=o.getNewValue(r,e),i=r.length-`${a}`.length,l=s.map((e=>e.element.dom.selectionStart-i)),c=s.map((e=>e.element.dom.selectionEnd-i));o.onAction(a,n),s.each((e=>{qu.setValue(e,a),t&&(l.each((t=>e.element.dom.selectionStart=t)),c.each((t=>e.element.dom.selectionEnd=t)))}))},d=(e,t)=>c(((e,t)=>e-t),e,t),u=(e,t)=>c(((e,t)=>e+t),e,t),m=e=>at(e.element).fold(A.none,(e=>(hc(e),A.some(!0)))),p=e=>bc(e.element)?(dt(e.element).each((e=>hc(e))),A.some(!0)):A.none(),h=(o,n,s,r)=>{const i=on(b),l=t.shared.providers.translate(s),c=Bi("altExecuting"),d=iS(e,"NodeChange SwitchMode",(t=>{pg.set(t.getComponent(),!e.selection.isEditable())})),u=e=>{pg.isDisabled(e)||o(!0)};return zb.sketch({dom:{tag:"button",attributes:{"aria-label":l,"data-mce-name":n},classes:r.concat(n)},components:[nT(n,t.shared.providers.icons)],buttonBehaviours:ma([pg.config({}),Zb.config(t.shared.providers.tooltips.getConfig({tooltipText:l})),Mh(c,[hw({onSetup:d,getApi:a},i),fw({getApi:a},i),Gr(er(),((e,t)=>{t.event.raw.keyCode!==AB.space()&&t.event.raw.keyCode!==AB.enter()||pg.isDisabled(e)||o(!1)})),Gr(sr(),u),Gr($s(),u)])]),eventOrder:{[er()]:[c,"keying"],[sr()]:[c,"alloy.base.behaviour"],[$s()]:[c,"alloy.base.behaviour"],[_r()]:["alloy.base.behaviour",c,"tooltipping"],[Tr()]:[c,"tooltipping"]}})},f=Vb(h((e=>d(!1,e)),"minus","Decrease font size",[])),v=Vb(h((e=>u(!1,e)),"plus","Increase font size",[])),y=Vb({dom:{tag:"div",classes:["tox-input-wrapper"]},components:[Iy.sketch({inputBehaviours:ma([pg.config({}),Mh(l,[hw({onSetup:r,getApi:a},i),fw({getApi:a},i)]),Mh("input-update-display-text",[Gr(aT,((e,t)=>{qu.setValue(e,t.event.text)})),Gr(Zs(),(e=>{o.onAction(qu.getValue(e))})),Gr(nr(),(e=>{o.onAction(qu.getValue(e))}))]),xh.config({mode:"special",onEnter:e=>(c(w,!0,!0),A.some(!0)),onEscape:m,onUp:e=>(u(!0,!1),A.some(!0)),onDown:e=>(d(!0,!1),A.some(!0)),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})],behaviours:ma([Rh.config({}),xh.config({mode:"special",onEnter:p,onSpace:p,onEscape:m}),Mh("input-wrapper-events",[Gr(Js(),(e=>{V([f,v],(t=>{const o=ze(t.get(e).element.dom);bc(o)&&fc(o)}))}))])])});return{dom:{tag:"div",classes:["tox-number-input"],attributes:{...g(n)?{"data-mce-name":n}:{}}},components:[f.asSpec(),y.asSpec(),v.asSpec()],behaviours:ma([Rh.config({}),xh.config({mode:"flow",focusInside:qg.OnEnterOrSpaceMode,cycles:!1,selector:"button, .tox-input-wrapper",onEscape:e=>bc(e.element)?A.none():(hc(e.element),A.some(!0))})])}})(e,t,(e=>{const t=()=>e.queryCommandValue("FontSize");return{updateInputValue:e=>Lr(e,aT,{text:t()}),onAction:(t,o)=>e.execCommand("FontSize",!1,t,{skip_focus:!o}),getNewValue:(o,n)=>{EB(o,["unsupportedLength","empty"]);const s=t(),r=EB(o,["unsupportedLength","empty"]).or(EB(s,["unsupportedLength","empty"])),a=r.map((e=>e.value)).getOr(16),i=eb(e),l=r.map((e=>e.unit)).filter((e=>""!==e)).getOr(i),c=n(a,(e=>{var t;return null!==(t={em:{step:.1},cm:{step:.1},in:{step:.1},pc:{step:.1},ch:{step:.1},rem:{step:.1}}[e])&&void 0!==t?t:{step:1}})(l).step),d=`${(e=>e>=0)(c)?c:a}${l}`;return d!==s&&((e,t)=>{e.dispatch("FontSizeInputTextUpdate",t)})(e,{value:d}),d}}})(e),"fontsizeinput"),fontfamily:(e,t)=>cB(e,t,OB(e),xB,"FontFamilyTextUpdate","fontfamily"),blocks:(e,t)=>cB(e,t,yB(e),bB,"BlocksTextUpdate","blocks"),align:(e,t)=>cB(e,t,hB(e),mB,"AlignTextUpdate","align")},nI=(e,t,o,n)=>{const s=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=L(ZB,(t=>{const o=P(t.items,(t=>be(e,t)||be(oI,t)));return{name:t.name,items:o}}));return P(t,(e=>e.items.length>0))})(o):r(t)?(e=>{const t=e.split("|");return L(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>be(e,"name")&&be(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=L(s,(s=>{const r=q(s.items,(s=>0===s.trim().length?[]:((e,t,o,n,s,r)=>fe(t,o.toLowerCase()).orThunk((()=>r.bind((e=>se(e,(e=>fe(t,e+o.toLowerCase()))))))).fold((()=>fe(oI,o.toLowerCase()).map((t=>t(e,s)))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o,n)=>fe(tI,e.type).fold((()=>(console.error("skipping button defined by",e),A.none())),(s=>A.some(s(e,t,o,n)))))(t,s,e,o.toLowerCase()):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),A.none()))))(e,t.buttons,s,t.allowToolbarGroups,o,n).toArray()));return{title:A.from(e.translate(s.name)),items:r}}));return P(a,(e=>e.items.length>0))},sI=(e,t,o,n)=>{const s=t.mainUi.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,r)){const t=a.map((t=>{const s={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return nI(e,s,n,A.none())}));XD.setToolbars(s,t)}else XD.setToolbar(s,nI(e,o,n,A.none()))},rI=Bo(),aI=rI.os.isiOS()&&rI.os.version.major<=12;var iI=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=t,i=on(0),l=r.outerContainer;sB(e);const d=ze(s.targetNode),u=bt(ft(d));pu(d,r.mothership),((e,t,o)=>{Ab(e)&&pu(o.mainUi.mothership.element,o.popupUi.mothership),gu(t,o.dialogUi.mothership)})(e,u,t),e.on("SkinLoaded",(()=>{XD.setSidebar(l,o.sidebar,ub(e)),sI(e,t,o,n),i.set(e.getWin().innerWidth),XD.setMenubar(l,QD(e,o)),XD.setViews(l,o.views),((e,t)=>{const{uiMotherships:o}=t,n=e.dom;let s=e.getWin();const r=e.getDoc().documentElement,a=on(qt(s.innerWidth,s.innerHeight)),i=on(qt(r.offsetWidth,r.offsetHeight)),l=()=>{const t=a.get();t.left===s.innerWidth&&t.top===s.innerHeight||(a.set(qt(s.innerWidth,s.innerHeight)),tS(e))},c=()=>{const t=e.getDoc().documentElement,o=i.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(i.set(qt(t.offsetWidth,t.offsetHeight)),tS(e))},d=t=>{((e,t)=>{e.dispatch("ScrollContent",t)})(e,t)};n.bind(s,"resize",l),n.bind(s,"scroll",d);const u=Rc(ze(e.getBody()),"load",c);e.on("hide",(()=>{V(o,(e=>{Bt(e.element,"display","none")}))})),e.on("show",(()=>{V(o,(e=>{Ht(e.element,"display")}))})),e.on("NodeChange",c),e.on("remove",(()=>{u.unbind(),n.unbind(s,"resize",l),n.unbind(s,"scroll",d),s=null}))})(e,t)}));const m=XD.getSocket(l).getOrDie("Could not find expected socket element");if(aI){It(m.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=((e,t)=>{let o=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null)},throttle:(...t)=>{c(o)&&(o=setTimeout((()=>{o=null,e.apply(null,t)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=Fc(m.element,"scroll",t.throttle);e.on("remove",o.unbind)}cw(e,t),e.addCommand("ToggleSidebar",((t,o)=>{XD.toggleSidebar(l,o),e.dispatch("ToggleSidebar")})),e.addQueryValueHandler("ToggleSidebar",(()=>{var e;return null!==(e=XD.whichSidebar(l))&&void 0!==e?e:""})),e.addCommand("ToggleView",((t,o)=>{if(XD.toggleView(l,o)){const t=l.element;r.mothership.broadcastOn([Eu()],{target:t}),V(a,(e=>{e.broadcastOn([Eu()],{target:t})})),c(XD.whichView(l))&&(e.focus(),e.nodeChanged(),XD.refreshToolbar(l))}})),e.addQueryValueHandler("ToggleView",(()=>{var e;return null!==(e=XD.whichView(l))&&void 0!==e?e:""}));const g=Wf(e);g!==Sf.sliding&&g!==Sf.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==i.get()&&(XD.refreshToolbar(t.mainUi.outerContainer),i.set(o))}));const p={setEnabled:e=>{lw(t,!e)},isEnabled:()=>!pg.isDisabled(l)};return{iframeContainer:m.element.dom,editorContainer:l.element.dom,api:p}}});const lI=e=>/^[0-9\.]+(|px)$/i.test(""+e)?A.some(parseInt(""+e,10)):A.none(),cI=e=>h(e)?e+"px":e,dI=(e,t,o)=>{const n=t.filter((t=>e<t)),s=o.filter((t=>e>t));return n.or(s).getOr(e)},uI=e=>{const t=If(e),o=Ff(e),n=Nf(e);return lI(t).map((e=>dI(e,o,n)))},{ToolbarLocation:mI,ToolbarMode:gI}=Db,pI=(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=o,i=Of.DOM,l=Ob(e),c=Eb(e),d=Nf(e).or(uI(e)),u=n.shared.header,m=u.isPositionedAtTop,g=Wf(e),p=g===gI.sliding||g===gI.floating,h=on(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(x(0),(e=>e.components().length>1?jt(e.components()[1].element):0)):0,v=()=>{V(a,(e=>{e.broadcastOn([Au()],{})}))},y=o=>{if(!f())return;l||s.on((e=>{const o=d.getOrThunk((()=>Go().width-Kt(t).left-10));Bt(e.element,"max-width",o+"px")}));const n=Wo(),a=!(l||l||!(Xt(r.outerContainer.element).left+Zt(r.outerContainer.element)>=window.innerWidth-40||zt(r.outerContainer.element,"width").isSome())||(Bt(r.outerContainer.element,"position","absolute"),Bt(r.outerContainer.element,"left","0px"),Ht(r.outerContainer.element,"width"),0));if(p&&XD.refreshToolbar(r.outerContainer),!l){const o=Wo(),i=ke(n.left!==o.left,n);((o,n)=>{s.on((s=>{const a=XD.getToolbar(r.outerContainer),i=b(a),l=Qo(t),c=((e,t)=>Ab(e)?Ba(t):A.none())(e,r.outerContainer.element),d=c.fold((()=>l.x),(e=>{const t=Qo(e);return Ze(e,wt())?l.x:l.x-t.x})),u=ke(o,Math.ceil(r.outerContainer.element.dom.getBoundingClientRect().width)).filter((e=>e>150)).map((e=>{const t=n.getOr(Wo()),o=window.innerWidth-(d-t.left),s=Math.max(Math.min(e,o),150);return o<e&&Bt(r.outerContainer.element,"width",s+"px"),{width:s+"px"}})).getOr({width:"max-content"}),g={position:"absolute",left:Math.round(d)+"px",top:c.fold((()=>m()?Math.max(l.y-jt(s.element)+i,0):l.bottom),(e=>{var t;const o=Qo(e),n=null!==(t=e.dom.scrollTop)&&void 0!==t?t:0,r=Ze(e,wt())?Math.max(l.y-jt(s.element)+i,0):l.y-o.y+n-jt(s.element)+i;return m()?r:l.bottom}))+"px"};It(r.outerContainer.element,{...g,...u})}))})(a,i),i.each((e=>{jo(e.left,o.top)}))}c&&s.on(o),v()},w=()=>!(l||!c||!f())&&s.get().exists((o=>{const n=u.getDockingMode(),a=(o=>{switch($f(e)){case mI.auto:const e=XD.getToolbar(r.outerContainer),n=b(e),s=jt(o.element)-n,a=Qo(t);if(a.y>s)return"top";{const e=nt(t),o=Math.max(e.dom.scrollHeight,jt(e));return a.bottom<o-s||tn().bottom<a.bottom-s?"bottom":"top"}case mI.bottom:return"bottom";case mI.top:default:return"top"}})(o);return a!==n&&(i=a,s.on((e=>{Ei.setModes(e,[i]),u.setDockingMode(i);const t=m()?Jc.TopToBottom:Jc.BottomToTop;Ct(e.element,Qc,t)})),!0);var i}));return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),Bt(r.outerContainer.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),V(a,(e=>{Ht(e.element,"display")})),w(),Ab(e)?y((e=>Ei.isDocked(e)?Ei.reset(e):Ei.refresh(e))):y(Ei.refresh)},hide:()=>{h.set(!1),Bt(r.outerContainer.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus"),V(a,(e=>{Bt(e.element,"display","none")}))},update:y,updateMode:()=>{w()&&y(Ei.reset)},repositionPopups:v}},hI=(e,t)=>{const o=Qo(e);return{pos:t?o.y:o.bottom,bounds:o}};var fI=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r}=t,a=rn(),i=ze(s.targetNode),l=pI(e,i,t,n,a),c=Yf(e);rB(e);const d=()=>{if(a.isSet())return void l.show();a.set(XD.getHeader(r.outerContainer).getOrDie());const s=_b(e);Ab(e)?(pu(i,r.mothership),pu(i,t.popupUi.mothership)):gu(s,r.mothership),gu(s,t.dialogUi.mothership);const d=()=>{sI(e,t,o,n),XD.setMenubar(r.outerContainer,QD(e,o)),l.show(),((e,t,o,n)=>{const s=on(hI(t,o.isPositionedAtTop())),r=n=>{const{pos:r,bounds:a}=hI(t,o.isPositionedAtTop()),{pos:i,bounds:l}=s.get(),c=a.height!==l.height||a.width!==l.width;s.set({pos:r,bounds:a}),c&&tS(e,n),o.isVisible()&&(i!==r?o.update(Ei.reset):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update(Ei.reset))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>r(e)))}));let a=0;const i=A_((()=>o.update(Ei.refresh)),33);e.on("ScrollWindow",(()=>{const e=Wo().left;e!==a&&(a=e,i.throttle()),o.updateMode()})),Ab(e)&&e.on("ElementScroll",(e=>{o.update(Ei.refresh)}));const l=sn();l.set(Rc(ze(e.getBody()),"load",(e=>r(e.raw)))),e.on("remove",(()=>{l.clear()}))})(e,i,l,c),e.nodeChanged()};c?e.once("SkinLoaded",d):d()};e.on("show",d),e.on("hide",l.hide),c||(e.on("focus",d),e.on("blur",l.hide)),e.on("init",(()=>{(e.hasFocus()||c)&&d()})),cw(e,t);const u={show:d,hide:l.hide,setEnabled:e=>{lw(t,!e)},isEnabled:()=>!pg.isDisabled(r.outerContainer)};return{editorContainer:r.outerContainer.element.dom,api:u}}});const bI="contexttoolbar-hide",vI=(e,t)=>Gr(Q_,((o,n)=>{const s=(e=>({hide:()=>zr(e,yr()),getValue:()=>qu.getValue(e)}))(e.get(o));t.onAction(s,n.event.buttonApi)})),yI=(e,t)=>{const o=e.label.fold((()=>({})),(e=>({"aria-label":e}))),n=Vb(Iy.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:e.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:ma([xh.config({mode:"special",onEnter:e=>s.findPrimary(e).map((e=>(Vr(e),!0))),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})),s=((e,t,o)=>{const n=L(t,(t=>Vb(((e,t,o)=>(e=>"contextformtogglebutton"===e.type)(t)?((e,t,o)=>{const{primary:n,...s}=t.original,r=Zn(Ix({...s,type:"togglebutton",onAction:b}));return JB(r,o,[vI(e,t)])})(e,t,o):((e,t,o)=>{const{primary:n,...s}=t.original,r=Zn(Mx({...s,type:"button",onAction:b}));return KB(r,o,[vI(e,t)])})(e,t,o))(e,t,o))));return{asSpecs:()=>L(n,(e=>e.asSpec())),findPrimary:e=>se(t,((t,o)=>t.primary?A.from(n[o]).bind((t=>t.getOpt(e))).filter(C(pg.isDisabled)):A.none()))}})(n,e.commands,t);return[{title:A.none(),items:[n.asSpec()]},{title:A.none(),items:s.asSpecs()}]},xI=(e,t,o)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,wI=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=mt(ze(e.startContainer),e.startOffset).element;return(Ge(o)?rt(o):A.some(o)).filter($e).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=Wo();return Jo(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=Zo(ze(e.getBody()));return Jo(o.x+t.left,o.y+t.top,t.width,t.height)}},SI=(e,t,o,n=0)=>{const s=Go(window),r=Qo(ze(e.getContentAreaContainer())),a=fb(e)||xb(e)||Sb(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(r,s,n);if(e.inline&&!a)return Jo(i,s.y,l,s.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,s,r)=>{const a=ze(e.getContainer()),i=_l(a,".tox-editor-header").getOr(a),l=Qo(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+r,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-r,o.bottom)};const u="line"===s?Qo(a):t;return d?{y:Math.max(l.bottom+r,o.y),bottom:Math.min(u.bottom-r,o.bottom)}:{y:Math.max(u.y+r,o.y),bottom:Math.min(l.y-r,o.bottom)}})(e,r,s,a,o,n);return Jo(i,c,l,d-c)}},kI={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},CI={maxHeightFunction:Uc(),maxWidthFunction:QM()},OI=e=>"node"===e,_I=(e,t,o,n,s)=>{const r=wI(e),a=n.lastElement().exists((e=>Ze(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=mt(ze(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&Ze(n.element,t)})(e,o)?a?tA:KE:a?((e,o,s)=>{const a=zt(e,"position");Bt(e,"position",o);const i=xI(r,Qo(t),-20)&&!n.isReposition()?nA:tA;return a.each((t=>Bt(e,"position",t))),i})(t,n.getMode()):("fixed"===n.getMode()?s.y+Wo().top:s.y)+(jt(t)+12)<=r.y?KE:JE},TI=(e,t,o,n)=>{const s=t=>(n,s,r,a,i)=>({..._I(e,a,t,o,i)({...n,y:i.y,height:i.height},s,r,a,i),alwaysFit:!0}),r=e=>OI(n)?[s(e)]:[];return t?{onLtr:e=>[oc,Jl,Ql,Zl,ec,tc].concat(r(e)),onRtl:e=>[oc,Ql,Jl,ec,Zl,tc].concat(r(e))}:{onLtr:e=>[tc,oc,Zl,Jl,ec,Ql].concat(r(e)),onRtl:e=>[tc,oc,ec,Ql,Zl,Jl].concat(r(e))}},EI=(e,t)=>{const o=P(t,(t=>t.predicate(e.dom))),{pass:n,fail:s}=H(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:s}},AI=(e,t)=>{const o={},n=[],s=[],r={},a={},i=re(e);return V(i,(i=>{const l=e[i];"contextform"===l.type?((e,i)=>{const l=Zn(Qn("ContextForm",Hx,i));o[e]=l,l.launch.map((o=>{r["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?s.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,t)=>{var o;(o=t,Qn("ContextToolbar",Px,o)).each((o=>{"editor"===t.scope?s.push(o):n.push(o),a[e]=o}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:s,lookupTable:a,formNavigators:r}},MI=Bi("forward-slide"),DI=Bi("backward-slide"),BI=Bi("change-slide-event"),II="tox-pop--resizing",FI="tox-pop--transition",RI=(e,t,o,n)=>{const s=n.backstage,r=s.shared,a=Bo().deviceType.isTouch,i=rn(),l=rn(),c=rn(),d=hl((e=>{const t=on([]);return wf.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),wf.getContent(e).each((e=>{Ht(e.element,"visibility")})),Oa(e.element,II),Ht(e.element,"width")},inlineBehaviours:ma([Mh("context-toolbar-events",[Zr(ar(),((e,t)=>{"width"===t.event.raw.propertyName&&(Oa(e.element,II),Ht(e.element,"width"))})),Gr(BI,((e,t)=>{const o=e.element;Ht(o,"width");const n=Qt(o);wf.setContent(e,t.event.contents),ka(o,II);const s=Qt(o);Bt(o,"width",n+"px"),wf.getContent(e).each((e=>{t.event.focus.bind((e=>(hc(e),yc(o)))).orThunk((()=>(xh.focusIn(e),vc(ft(o)))))})),setTimeout((()=>{Bt(e.element,"width",s+"px")}),0)})),Gr(MI,((e,o)=>{wf.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:vc(ft(e.element))}]))})),Lr(e,BI,{contents:o.event.forwardContents,focus:A.none()})})),Gr(DI,((e,o)=>{oe(t.get()).each((o=>{t.set(t.get().slice(0,t.get().length-1)),Lr(e,BI,{contents:fl(o.bar),focus:o.focus})}))}))]),xh.config({mode:"special",onEscape:o=>oe(t.get()).fold((()=>e.onEscape()),(e=>(zr(o,DI),A.some(!0))))})]),lazySink:()=>dn.value(e.sink)})})({sink:o,onEscape:()=>(e.focus(),A.some(!0))})),u=()=>{const t=c.get().getOr("node"),o=OI(t)?1:0;return SI(e,r,t,o)},m=()=>!(e.removed||a()&&s.isContextMenuOpen()),g=()=>{if(m()){const t=u(),o=ye(c.get(),"node")?((e,t)=>t.filter((e=>xt(e)&&je(e))).map(Zo).getOrThunk((()=>wI(e))))(e,i.get()):wI(e);return t.height<=0||!xI(o,t,.01)}return!0},p=()=>{i.clear(),l.clear(),c.clear(),wf.hide(d)},h=()=>{if(wf.isOpen(d)){const e=d.element;Ht(e,"display"),g()?Bt(e,"display","none"):(l.set(0),wf.reposition(d))}},f=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:ma([xh.config({mode:"acyclic"}),Mh("pop-dialog-wrap-events",[ea((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>xh.focusIn(t)))})),ta((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),v=eo((()=>AI(t,(e=>{const t=y([e]);Lr(d,MI,{forwardContents:f(t)})})))),y=t=>{const{buttons:o}=e.ui.registry.getAll(),s={...o,...v().formNavigators},a=Wf(e)===Sf.scrolling?Sf.scrolling:Sf.default,i=G(L(t,(t=>"contexttoolbar"===t.type?((t,o)=>nI(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n.backstage,A.some(["form:"])))(s,t):((e,t)=>yI(e,t))(t,r.providers))));return kD({type:a,uid:Bi("context-toolbar"),initGroups:i,onEscape:A.none,cyclicKeying:!0,providers:r.providers})},x=(t,n)=>{if(S.cancel(),!m())return;const s=y(t),p=t[0].position,h=((t,n)=>{const s="node"===t?r.anchors.node(n):r.anchors.cursor(),c=((e,t,o,n)=>"line"===t?{bubble:Gc(12,0,kI),layouts:{onLtr:()=>[nc],onRtl:()=>[sc]},overrides:CI}:{bubble:Gc(0,12,kI,1/12),layouts:TI(e,o,n,t),overrides:CI})(e,t,a(),{lastElement:i.get,isReposition:()=>ye(l.get(),0),getMode:()=>tu.getMode(o)});return wn(s,c)})(p,n);c.set(p),l.set(1);const b=d.element;Ht(b,"display"),(e=>ye(we(e,i.get(),Ze),!0))(n)||(Oa(b,FI),tu.reset(o,d)),wf.showWithinBounds(d,f(s),{anchor:h,transition:{classes:[FI],mode:"placement"}},(()=>A.some(u()))),n.fold(i.clear,i.set),g()&&Bt(b,"display","none")};let w=!1;const S=A_((()=>{!e.hasFocus()||e.removed||w||(_a(d.element,FI)?S.throttle():((e,t)=>{const o=ze(t.getBody()),n=e=>Ze(e,o),s=ze(t.selection.getNode());return(e=>!n(e)&&!et(o,e))(s)?A.none():((e,t,o)=>{const n=EI(e,t);if(n.contextForms.length>0)return A.some({elem:e,toolbars:[n.contextForms[0]]});{const t=EI(e,o);if(t.contextForms.length>0)return A.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>R(e,(e=>e.position===t)),o=t=>P(e,(e=>e.position===t)),n=t("selection"),s=t("node");if(n||s){if(s&&n){const e=o("node"),t=L(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return A.some({elem:e,toolbars:o})}return A.none()}})(s,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?A.none():zs(t,(e=>{if($e(e)){const{contextToolbars:t,contextForms:n}=EI(e,o.inNodeScope),s=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>j(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>P(e,(e=>e.position===t))))}})(t);return s.length>0?A.some({elem:e,toolbars:s}):A.none()}return A.none()}),e))(n,s,e)))})(v(),e).fold(p,(e=>{x(e.toolbars,A.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",p),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",h),e.on("click keyup focus SetContent",S.throttle),e.on(bI,p),e.on("contexttoolbar-show",(t=>{const o=v();fe(o.lookupTable,t.toolbarKey).each((o=>{x([o],ke(t.target!==e,t.target)),wf.getContent(d).each(xh.focusIn)}))})),e.on("focusout",(t=>{Cf.setEditorTimeout(e,(()=>{yc(o.element).isNone()&&yc(d.element).isNone()&&p()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&p()})),e.on("ExecCommand",(({command:e})=>{"toggleview"===e.toLowerCase()&&p()})),e.on("AfterProgressState",(t=>{t.state?p():e.hasFocus()&&S.throttle()})),e.on("dragstart",(()=>{w=!0})),e.on("dragend drop",(()=>{w=!1})),e.on("NodeChange",(e=>{yc(d.element).fold(S.throttle,b)}))}))},NI=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),s=rn();return L(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:r=>{const a=e=>{e&&(s.on((e=>e.setActive(!1))),s.set(r)),r.setActive(e)};a(ye(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{s.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},zI=e=>{NI(e,(e=>({name:"lineheight",text:"Line height",icon:"line-height",getOptions:yb,hash:e=>((e,t)=>EB(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:w,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>A.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t),onToolbarSetup:rS(e),onMenuSetup:rS(e)}))(e)),(e=>A.from(Pf(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:x(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>{var n;return e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:null!==(n=t.customCode)&&void 0!==n?n:null}).unbind},getCurrent:e=>{const t=ze(e.selection.getNode());return Ls(t,(e=>A.some(e).filter($e).bind((e=>Tt(e,"lang").map((t=>({code:t,customCode:Tt(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=sn();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),sS(o.clear,rS(e)(t))},onMenuSetup:rS(e)}))))(e).each((t=>NI(e,t)))},LI=e=>iS(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent")&&e.selection.isEditable())})),VI=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),sS((()=>e.off("PastePlainTextToggle",n)),rS(e)(o))},HI=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},PI=e=>{(e=>{(e=>{E_.each([{name:"bold",text:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:aS(e,t.name),onAction:HI(e,t.name),shortcut:t.shortcut})}));for(let t=1;t<=6;t++){const o="h"+t,n=`Access+${t}`;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:aS(e,o),onAction:HI(e,o),shortcut:n})}})(e),(e=>{E_.each([{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"help",text:"Help",action:"mceHelp",icon:"help",shortcut:"Alt+0"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"print",text:"Print",action:"mcePrint",icon:"print",shortcut:"Meta+P"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:cS(e,t.action),shortcut:t.shortcut})})),E_.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:rS(e),onAction:cS(e,t.action)})}))})(e),(e=>{E_.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:cS(e,t.action),onSetup:aS(e,t.name)})}))})(e)})(e),(e=>{E_.each([{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:cS(e,t.action)})})),E_.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onSetup:rS(e),onAction:cS(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onSetup:rS(e),onAction:HI(e,"code")})})(e)},UI=(e,t)=>iS(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),WI=e=>iS(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),jI=(e,t)=>{(e=>{V([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:cS(e,t.cmd),onSetup:aS(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onSetup:rS(e),onAction:cS(e,"JustifyNone")})})(e),PI(e),((e,t)=>{((e,t)=>{const o=lB(t,hB(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),onSetup:rS(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=lB(t,OB(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),onSetup:rS(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=lB(t,LB(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",onSetup:rS(e),getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=lB(t,yB(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",onSetup:rS(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=lB(t,NB(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",onSetup:rS(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:UI(e,"hasUndo"),onAction:cS(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:UI(e,"hasRedo"),onAction:cS(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:UI(e,"hasUndo"),onAction:cS(e,"undo"),shortcut:"Meta+Z"}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:UI(e,"hasRedo"),onAction:cS(e,"redo"),shortcut:"Meta+Y"})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},void 0,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=ES(e),o=AS(e),n=on(t),s=on(o);VS(e,"forecolor","forecolor",n),VS(e,"backcolor","hilitecolor",s),HS(e,"forecolor","forecolor","Text color",n),HS(e,"backcolor","hilitecolor","Background color",s)})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:cS(e,"mceToggleVisualAid")})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:WI(e),onAction:cS(e,"mceToggleVisualAid")})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:LI(e),onAction:cS(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onSetup:rS(e),onAction:cS(e,"indent")})})(e)})(e),zI(e),(e=>{const t=on(db(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:VI(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:VI(e,t)})})(e)},$I=e=>r(e)?e.split(/[ ,]/):e,GI=e=>t=>t.options.get(e),qI=GI("contextmenu_never_use_native"),YI=GI("contextmenu_avoid_overlap"),XI=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:P(o,(e=>be(t,e)))},KI=(e,t)=>({type:"makeshift",x:e,y:t}),JI=e=>"longpress"===e.type||0===e.type.indexOf("touch"),QI=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if(JI(e)){const t=e.touches[0];return KI(t.pageX,t.pageY)}return KI(e.pageX,e.pageY)})(t):((e,t)=>{const o=Of.DOM.getPos(e);return((e,t,o)=>KI(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if(JI(e)){const t=e.touches[0];return KI(t.clientX,t.clientY)}return KI(e.clientX,e.clientY)})(t)):ZI(e),ZI=e=>({type:"selection",root:ze(e.selection.getNode())}),eF=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:A.some(ze(e.selection.getNode())),root:ze(e.getBody())}))(e);case"point":return QI(e,t);case"selection":return ZI(e)}},tF=(e,t,o,n,s,r)=>{const a=o(),i=eF(e,t,r);mT(a,dy.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!1,search:A.none()}).map((e=>{t.preventDefault(),wf.showMenuAt(s,{anchor:i},{menu:{markers:Oy("normal")},data:e})}))},oF={onLtr:()=>[oc,Jl,Ql,Zl,ec,tc,KE,JE,XE,qE,YE,GE],onRtl:()=>[oc,Ql,Jl,ec,Zl,tc,KE,JE,YE,GE,XE,qE]},nF={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},sF=(e,t,o,n,s,r)=>{const a=Bo(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>{const n=eF(e,t,o);return{bubble:Gc(0,"point"===o?12:0,nF),layouts:oF,overrides:{maxWidthFunction:QM(),maxHeightFunction:Uc()},...n}})(e,t,r);mT(o,dy.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!0,search:A.none()}).map((o=>{t.preventDefault();const l=a?vf.HighlightMenuAndItem:vf.HighlightNone;wf.showMenuWithinBounds(s,{anchor:i},{menu:{markers:Oy("normal"),highlightOnOpen:l},data:o,type:"horizontal"},(()=>A.some(SI(e,n.shared,"node"===r?"node":"selection")))),e.dispatch(bI)}))})(e,t,a,n,s,r,!(c||i||l&&d))};if((l||i)&&"node"!==r){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{Cf.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),r()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const s=()=>r();e.once("longpresscancel",s);const r=()=>{e.off("touchend",o),e.off("longpresscancel",s),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],s=o.getRng();return Md(e.getWin(),yd.domRange(s)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},rF=e=>r(e)?"|"===e:"separator"===e.type,aF={type:"separator"},iF=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(r(e))return e;switch(e.type){case"separator":return aF;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return r(t)?t:L(t,iF)}};default:const o=e;return{type:"menuitem",...t(o),onAction:v(o.onAction)}}},lF=(e,t)=>{if(0===t.length)return e;const o=oe(e).filter((e=>!rF(e))).fold((()=>[]),(e=>[aF]));return e.concat(o).concat(t).concat([aF])},cF=(e,t)=>!(e=>"longpress"===e.type||be(e,"touches"))(t)&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),dF=(e,t)=>cF(e,t)?e.selection.getStart(!0):t.target,uF=(e,t,o)=>{const n=Bo().deviceType.isTouch,s=hl(wf.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:ma([Mh("dismissContextMenu",[Gr(Er(),((t,o)=>{Tu.close(t),e.focus()}))])])})),a=()=>wf.hide(s),i=t=>{if(qI(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!qI(e))(e,t)||(e=>0===XI(e).length)(e))return;const a=((e,t)=>{const o=YI(e),n=cF(e,t)?"selection":"point";if(Me(o)){const s=dF(e,t);return Bk(ze(s),o)?"node":n}return n})(e,t);(n()?sF:tF)(e,t,(()=>{const o=dF(e,t),n=e.ui.registry.getAll(),s=XI(e);return((e,t,o)=>{const n=W(t,((t,n)=>fe(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(r(n)&&Me(Ae(n)))return lF(t,n.split(" "));if(l(n)&&n.length>0){const e=L(n,iF);return lF(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&rF(n[n.length-1])&&n.pop(),n})(n.contextMenus,s,o)}),o,s,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},mF=Is([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),gF=e=>t=>t.translate(-e.left,-e.top),pF=e=>t=>t.translate(e.left,e.top),hF=e=>(t,o)=>W(e,((e,t)=>t(e)),qt(t,o)),fF=(e,t,o)=>e.fold(hF([pF(o),gF(t)]),hF([gF(t)]),hF([])),bF=(e,t,o)=>e.fold(hF([pF(o)]),hF([]),hF([pF(t)])),vF=(e,t,o)=>e.fold(hF([]),hF([gF(o)]),hF([pF(t),gF(o)])),yF=(e,t,o)=>{const n=e.fold(((e,t)=>({position:A.some("absolute"),left:A.some(e+"px"),top:A.some(t+"px")})),((e,t)=>({position:A.some("absolute"),left:A.some(e-o.left+"px"),top:A.some(t-o.top+"px")})),((e,t)=>({position:A.some("fixed"),left:A.some(e+"px"),top:A.some(t+"px")})));return{right:A.none(),bottom:A.none(),...n}},xF=(e,t,o,n)=>{const s=(e,s)=>(r,a)=>{const i=e(t,o,n);return s(r.getOr(i.left),a.getOr(i.top))};return e.fold(s(vF,wF),s(bF,SF),s(fF,kF))},wF=mF.offset,SF=mF.absolute,kF=mF.fixed,CF=(e,t)=>{const o=_t(e,t);return u(o)?NaN:parseInt(o,10)},OF=(e,t,o,n,s,r)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=CF(o,t.leftAttr),s=CF(o,t.topAttr);return isNaN(n)||isNaN(s)?A.none():A.some(qt(n,s))})(e,t).fold((()=>o),(e=>kF(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?TF(e,t,a,s,r):EF(e,t,a,s,r),l=fF(a,s,r);return((e,t,o)=>{const n=e.element;Ct(n,t.leftAttr,o.left+"px"),Ct(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:kF(l.left,l.top),extra:A.none()})),(e=>({coord:e.output,extra:e.extra})))},_F=(e,t,o,n)=>se(e,(e=>{const s=e.sensor,r=((e,t,o,n,s,r)=>{const a=bF(e,s,r),i=bF(t,s,r);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,s,e.range.left,e.range.top,o,n);return r?A.some({output:xF(e.output,t,o,n),extra:e.extra}):A.none()})),TF=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return _F(r,o,n,s).orThunk((()=>{const e=W(r,((e,t)=>{const r=t.sensor,a=((e,t,o,n,s,r)=>{const a=bF(e,s,r),i=bF(t,s,r),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return qt(l,c)})(o,r,t.range.left,t.range.top,n,s);return e.deltas.fold((()=>({deltas:A.some(a),snap:A.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:A.some(a),snap:A.some(t)}:e))}),{deltas:A.none(),snap:A.none()});return e.snap.map((e=>({output:xF(e.output,o,n,s),extra:e.extra})))}))},EF=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return _F(r,o,n,s)};var AF=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const t=tt(e.element),o=Wo(t),r=Ia(s),a=((e,t,o)=>({coord:xF(e.output,e.output,t,o),extra:e.extra}))(n,o,r),i=yF(a.coord,0,r);Ft(s,i)}}});const MF="data-initial-z-index",DF=(e,t)=>{e.getSystem().addToGui(t),(e=>{rt(e.element).filter($e).each((t=>{zt(t,"z-index").each((e=>{Ct(t,MF,e)})),Bt(t,"z-index",Rt(e.element,"z-index"))}))})(t)},BF=e=>{(e=>{rt(e.element).filter($e).each((e=>{Tt(e,MF).fold((()=>Ht(e,"z-index")),(t=>Bt(e,"z-index",t))),At(e,MF)}))})(e),e.getSystem().removeFromGui(e)},IF=(e,t,o)=>e.getSystem().build(Uk.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var FF=ks("snaps",[is("getSnapPoints"),xi("onSensor"),is("leftAttr"),is("topAttr"),Cs("lazyViewport",tn),Cs("mustSnap",!1)]);const RF=[Cs("useFixed",T),is("blockerClass"),Cs("getTarget",w),Cs("onDrag",b),Cs("repositionTarget",!0),Cs("onDrop",b),Ms("getBounds",tn),FF],NF=e=>{return(t=zt(e,"left"),o=zt(e,"top"),n=zt(e,"position"),t.isSome()&&o.isSome()&&n.isSome()?A.some(((e,t,o)=>("fixed"===o?kF:wF)(parseInt(e,10),parseInt(t,10)))(t.getOrDie(),o.getOrDie(),n.getOrDie())):A.none()).getOrThunk((()=>{const t=Xt(e);return SF(t.left,t.top)}));var t,o,n},zF=(e,t)=>({bounds:e.getBounds(),height:$t(t.element),width:Zt(t.element)}),LF=(e,t,o,n,s)=>{const r=o.update(n,s),a=o.getStartData().getOrThunk((()=>zF(t,e)));r.each((o=>{((e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const r=tt(e.element),a=Wo(r),i=Ia(s),l=NF(s),c=((e,t,o,n,s,r,a)=>((e,t,o,n,s)=>{const r=s.bounds,a=bF(t,o,n),i=Ul(a.left,r.x,r.x+r.width-s.width),l=Ul(a.top,r.y,r.y+r.height-s.height),c=SF(i,l);return t.fold((()=>{const e=vF(c,o,n);return wF(e.left,e.top)}),x(c),(()=>{const e=fF(c,o,n);return kF(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=r.left,i=r.top,t.fold(((e,t)=>wF(e+a,t+i)),((e,t)=>SF(e+a,t+i)),((e,t)=>kF(e+a,t+i))));var t,a,i;const l=fF(e,n,s);return kF(l.left,l.top)}),(t=>{const a=OF(e,t,o,r,n,s);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,s,a))(e,t.snaps,l,a,i,n,o),d=yF(c,0,i);Ft(s,d)}t.onDrag(e,s,n)})(e,t,a,o)}))},VF=(e,t,o,n)=>{t.each(BF),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;At(o,t.leftAttr),At(o,t.topAttr)})(e,t)})(e,t)}));const s=o.getTarget(e.element);n.reset(),o.onDrop(e,s)},HF=e=>(t,o)=>{const n=e=>{o.setStartData(zF(t,e))};return Wr([Gr(Cr(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var PF=Object.freeze({__proto__:null,getData:e=>A.from(qt(e.x,e.y)),getDelta:(e,t)=>qt(t.left-e.left,t.top-e.top)});const UF=(e,t,o)=>[Gr(qs(),((n,s)=>{if(0!==s.event.raw.button)return;s.stop();const r=()=>VF(n,A.some(l),e,t),a=Ik(r,200),i={drop:r,delayDrop:a.schedule,forceDrop:r,move:o=>{a.cancel(),LF(n,e,t,PF,o)}},l=IF(n,e.blockerClass,(e=>Wr([Gr(qs(),e.forceDrop),Gr(Ks(),e.drop),Gr(Ys(),((t,o)=>{e.move(o.event)})),Gr(Xs(),e.delayDrop)]))(i));o(n),DF(n,l)}))],WF=[...RF,Ci("dragger",{handlers:HF(UF)})];var jF=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return A.some(qt(t.clientX,t.clientY))})(t):A.none()},getDelta:(e,t)=>qt(t.left-e.left,t.top-e.top)});const $F=(e,t,o)=>{const n=rn(),s=o=>{VF(o,n.get(),e,t),n.clear()};return[Gr(Ws(),((r,a)=>{a.stop();const i=()=>s(r),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{LF(r,e,t,jF,o)}},c=IF(r,e.blockerClass,(e=>Wr([Gr(Ws(),e.forceDrop),Gr($s(),e.drop),Gr(Gs(),e.drop),Gr(js(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(r),DF(r,c)})),Gr(js(),((o,n)=>{n.stop(),LF(o,e,t,jF,n.event)})),Gr($s(),((e,t)=>{t.stop(),s(e)})),Gr(Gs(),s)]},GF=WF,qF=[...RF,Ci("dragger",{handlers:HF($F)})],YF=[...RF,Ci("dragger",{handlers:HF(((e,t,o)=>[...UF(e,t,o),...$F(e,t,o)]))})];var XF=Object.freeze({__proto__:null,mouse:GF,touch:qF,mouseOrTouch:YF}),KF=Object.freeze({__proto__:null,init:()=>{let e=A.none(),t=A.none();const o=x({});return ua({readState:o,reset:()=>{e=A.none(),t=A.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=A.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=A.some(e)}})}});const JF=fa({branchKey:"mode",branches:XF,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:A.from(e.extra)})},state:KF,apis:AF}),QF=(e,t,o,n,s,r)=>e.fold((()=>JF.snap({sensor:SF(o-20,n-20),range:qt(s,r),output:SF(A.some(o),A.some(n)),extra:{td:t}})),(e=>{const s=o-20,r=n-20,a=e.element.dom.getBoundingClientRect();return JF.snap({sensor:SF(s,r),range:qt(40,40),output:SF(A.some(o-a.width/2),A.some(n-a.height/2)),extra:{td:t}})})),ZF=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const s=n.td;((e,t)=>e.exists((e=>Ze(e,t))))(t.get(),s)||(t.set(s),o(s))},mustSnap:!0}),eR=e=>Vb(zb.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:ma([JF.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),vC.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),tR=(e,t)=>{const o=on([]),n=on([]),s=on(!1),r=rn(),a=rn(),i=e=>{const o=Zo(e);return QF(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=Zo(e);return QF(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=ZF((()=>L(o.get(),(e=>i(e)))),r,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=ZF((()=>L(n.get(),(e=>l(e)))),a,(t=>{r.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=eR(c),m=eR(d),g=hl(u.asSpec()),p=hl(m.asSpec()),h=(t,o,n,s)=>{const r=n(o);JF.snapTo(t,r),((t,o,n,r)=>{const a=o.dom.getBoundingClientRect();Ht(t.element,"display");const i=st(ze(e.getBody())).dom.innerHeight,l=a[s]<0,c=((e,t)=>e[s]>t)(a,i);(l||c)&&Bt(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");if(Bo().deviceType.isTouch()){const i=e=>L(e,ze);e.on("TableSelectionChange",(e=>{s.get()||(lu(t,g),lu(t,p),s.set(!0));const l=ze(e.start),c=ze(e.finish);r.set(l),a.set(c),A.from(e.otherCells).each((e=>{o.set(i(e.upOrLeftCells)),n.set(i(e.downOrRightCells)),f(l),b(c)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{r.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{s.get()&&(uu(g),uu(p),s.set(!1)),r.clear(),a.clear()}))}},oR=(e,t,o)=>{var n;const s=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:ma([xh.config({mode:"flow",selector:"div[role=button]"}),pg.config({disabled:o.isDisabled}),dw(),Ub.config({}),Ah.config({}),Mh("elementPathEvents",[ea(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>xh.focusIn(t))),e.on("NodeChange",(n=>{const r=(t=>{const o=[];let n=t.length;for(;n-- >0;){const r=t[n];if(1===r.nodeType&&"BR"!==(s=r).nodeName&&!s.getAttribute("data-mce-bogus")&&"bookmark"!==s.getAttribute("data-mce-type")){const t=nS(e,r);if(t.isDefaultPrevented()||o.push({name:t.name,element:r}),t.isPropagationStopped())break}}var s;return o})(n.parents),a=r.length>0?W(r,((t,n,r)=>{const a=((t,n,s)=>zb.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":s}},components:[ul(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:ma([Zb.config({...o.tooltips.getConfig({tooltipText:o.translate(["Select the {0} element",n.nodeName.toLowerCase()]),onShow:(e,t)=>{((e,t)=>{const o=A.from(_t(e,"id")).getOrThunk((()=>{const e=Bi("aria");return Ct(t,"id",e),e}));Ct(e,"aria-describedby",o)})(e.element,t.element)},onHide:e=>{var t;t=e.element,At(t,"aria-describedby")}})}),uw(o.isDisabled),dw()])}))(n.name,n.element,r);return 0===r?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[ul(` ${s} `)]},a])}),[]):[];Ah.set(t,a)}))}))])]),components:[]}};var nR;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}(nR||(nR={}));const sR=(e,t,o)=>{const n=ze(e.getContainer()),s=((e,t,o,n,s)=>{const r={height:dI(n+t.top,Rf(e),zf(e))};return o===nR.Both&&(r.width=dI(s+t.left,Ff(e),Nf(e))),r})(e,t,o,jt(n),Qt(n));ie(s,((e,t)=>{h(e)&&Bt(n,t,cI(e))})),(e=>{e.dispatch("ResizeEditor")})(e)},rR=(e,t,o,n)=>{const s=qt(20*o,20*n);return sR(e,s,t),A.some(!0)},aR=(e,t)=>{const o=()=>{const o=[],n=gb(e),s=ib(e),r=lb(e)||e.hasPlugin("wordcount");return s&&o.push(oR(e,{},t)),n&&o.push((()=>{const e=Sw("Alt+0");return{dom:{tag:"div",classes:["tox-statusbar__help-text"]},components:[ul(Kv.translate(["Press {0} for help",e]))]}})()),r&&o.push((()=>{const o=[];return e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>Ah.set(e,[ul(t.translate(["{0} "+n,o[n]]))]);return zb.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:ma([uw(t.isDisabled),dw(),Ub.config({}),Ah.config({}),qu.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),Mh("wordcount-events",[na((e=>{const t=qu.getValue(e),n="words"===t.mode?"characters":"words";qu.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),ea((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=qu.getValue(t);qu.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[hr()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),lb(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=poweredby&utm_source=tiny&utm_medium=referral&utm_content=v7",rel:"noopener",target:"_blank","aria-label":e.translate(["Build with {0}","TinyMCE"])},innerHtml:e.translate(["Build with {0}",'<svg height="16" viewBox="0 0 80 16" width="80" xmlns="http://www.w3.org/2000/svg"><g opacity=".8"><path d="m80 3.537v-2.202h-7.976v11.585h7.976v-2.25h-5.474v-2.621h4.812v-2.069h-4.812v-2.443zm-10.647 6.929c-.493.217-1.13.337-1.864.337s-1.276-.156-1.805-.47a3.732 3.732 0 0 1 -1.3-1.298c-.324-.554-.48-1.191-.48-1.877s.156-1.335.48-1.877a3.635 3.635 0 0 1 1.3-1.299 3.466 3.466 0 0 1 1.805-.481c.65 0 .914.06 1.263.18.36.12.698.277.986.47.289.192.578.384.842.6l.12.085v-2.586l-.023-.024c-.385-.35-.855-.614-1.384-.818-.53-.205-1.155-.313-1.877-.313-.721 0-1.6.144-2.333.445a5.773 5.773 0 0 0 -1.937 1.251 5.929 5.929 0 0 0 -1.324 1.9c-.324.735-.48 1.565-.48 2.455s.156 1.72.48 2.454c.325.734.758 1.383 1.324 1.913.553.53 1.215.938 1.937 1.25a6.286 6.286 0 0 0 2.333.434c.819 0 1.384-.108 1.961-.313.59-.216 1.083-.505 1.468-.866l.024-.024v-2.49l-.12.096c-.41.337-.878.626-1.396.866zm-14.869-4.15-4.8-5.04-.024-.025h-.902v11.67h2.502v-6.847l2.827 3.08.385.409.397-.41 2.791-3.067v6.845h2.502v-11.679h-.902l-4.788 5.052z"/><path clip-rule="evenodd" d="m15.543 5.137c0-3.032-2.466-5.113-4.957-5.137-.36 0-.745.024-1.094.096-.157.024-3.85.758-3.85.758-3.032.602-4.62 2.466-4.704 4.788-.024.89-.024 4.27-.024 4.27.036 3.165 2.406 5.138 5.017 5.126.337 0 1.119-.109 1.287-.145.144-.024.385-.084.746-.144.661-.12 1.684-.325 3.067-.602 2.37-.409 4.103-2.009 4.44-4.33.156-1.023.084-4.692.084-4.692zm-3.213 3.308-2.346.457v2.31l-5.859 1.143v-5.75l2.346-.458v3.441l3.513-.686v-3.44l-3.513.685v-2.297l5.859-1.143v5.75zm20.09-3.296-.083-1.023h-2.13v8.794h2.346v-4.884c0-1.107.95-1.985 2.057-1.997 1.095 0 1.901.89 1.901 1.997v4.884h2.346v-5.245c-.012-2.105-1.588-3.777-3.67-3.765a3.764 3.764 0 0 0 -2.778 1.25l.012-.011zm-6.014-4.102 2.346-.458v2.298l-2.346.457z" fill-rule="evenodd"/><path d="m28.752 4.126h-2.346v8.794h2.346z"/><path clip-rule="evenodd" d="m43.777 15.483 4.043-11.357h-2.418l-1.54 4.355-.445 1.324-.36-1.324-1.54-4.355h-2.418l3.151 8.794-1.083 3.08zm-21.028-5.51c0 .722.541 1.034.878 1.034s.638-.048.95-.144l.518 1.708c-.217.145-.879.518-2.13.518a2.565 2.565 0 0 1 -2.562-2.587c-.024-1.082-.024-2.49 0-4.21h-1.54v-2.142h1.54v-1.912l2.346-.458v2.37h2.201v2.142h-2.2v3.693-.012z" fill-rule="evenodd"/></g></svg>\n'.trim()])},behaviours:ma([Rh.config({})])}]}),{dom:{tag:"div",classes:["tox-statusbar__right-container"]},components:o}})()),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container",...(()=>{const e="tox-statusbar__text-container--flex-start",t="tox-statusbar__text-container--flex-end";if(n){const o="tox-statusbar__text-container-3-cols";return r||s?r&&!s?[o,t]:[o,e]:[o,"tox-statusbar__text-container--space-around"]}return[r&&!s?t:e]})()]},components:o}]:[]};return{dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const n=o(),s=((e,t)=>{const o=(e=>{const t=cb(e);return!1===t?nR.None:"both"===t?nR.Both:nR.Vertical})(e);if(o===nR.None)return A.none();const n=o===nR.Both?"Press the arrow keys to resize the editor.":"Press the Up and Down arrow keys to resize the editor.";return A.some(ry("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{"aria-label":t.translate(n),"data-mce-name":"resize-handle"},behaviours:[JF.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,s)=>sR(e,s,o),blockerClass:"tox-blocker"}),xh.config({mode:"special",onLeft:()=>rR(e,o,-1,0),onRight:()=>rR(e,o,1,0),onUp:()=>rR(e,o,0,-1),onDown:()=>rR(e,o,0,1)}),Ub.config({}),Rh.config({}),Zb.config(t.tooltips.getConfig({tooltipText:t.translate("Resize")}))]},t.icons))})(e,t);return n.concat(s.toArray())})()}},iR=(e,t)=>t.get().getOrDie(`UI for ${e} has not been rendered`),lR=(e,t)=>{const o=e.inline,n=o?fI:iI,s=Eb(e)?vM:lM,r=(()=>{const e=rn(),t=rn(),o=rn();return{dialogUi:e,popupUi:t,mainUi:o,getUiMotherships:()=>{const o=e.get().map((e=>e.mothership)),n=t.get().map((e=>e.mothership));return o.fold((()=>n.toArray()),(e=>n.fold((()=>[e]),(t=>Ze(e.element,t.element)?[e]:[e,t]))))},lazyGetInOuterOrDie:(e,t)=>()=>o.get().bind((e=>t(e.outerContainer))).getOrDie(`Could not find ${e} element in OuterContainer`)}})(),a=rn(),i=rn(),l=rn(),c=Bo().deviceType.isTouch()?["tox-platform-touch"]:[],d=kb(e),u=Wf(e),m=Vb({dom:{tag:"div",classes:["tox-anchorbar"]}}),g=Vb({dom:{tag:"div",classes:["tox-bottom-anchorbar"]}}),p=()=>r.mainUi.get().map((e=>e.outerContainer)).bind(XD.getHeader),h=r.lazyGetInOuterOrDie("anchor bar",m.getOpt),f=r.lazyGetInOuterOrDie("bottom anchor bar",g.getOpt),b=r.lazyGetInOuterOrDie("toolbar",XD.getToolbar),v=r.lazyGetInOuterOrDie("throbber",XD.getThrobber),y=((e,t,o,n)=>{const s=on(!1),r=(e=>{const t=on(kb(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),a={icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:Kv.translate,isDisabled:()=>t.mode.isReadOnly()||!t.ui.isEnabled(),getOption:t.options.get,tooltips:kA(e.dialog)},i=YA(t),l=(e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},n=on([]),s=on([]),r=on(!1);return e.on("PreInit",(s=>{const r=xA(e),a=SA(e,r,t,o);n.set(a)})),e.on("addStyleModifications",(n=>{const a=SA(e,n.items,t,o);s.set(a),r.set(n.replace)})),{getData:()=>{const e=r.get()?[]:n.get(),t=s.get();return e.concat(t)}}})(t),c=(e=>({colorPicker:uA(e),hasCustomColors:mA(e),getColors:gA(e),getColorCols:pA(e)}))(t),d=(e=>({isDraggableModal:hA(e)}))(t),u={shared:{providers:a,anchors:dA(t,o,n,r.isPositionedAtTop),header:r},urlinput:i,styles:l,colorinput:c,dialog:d,isContextMenuOpen:()=>s.get(),setContextMenuState:e=>s.set(e)},m=e=>A.none(),g={...u,shared:{...u.shared,interpreter:e=>LE(e,{},g,m),getSink:e.popup}},p={...u,shared:{...u.shared,interpreter:e=>LE(e,{},p,m),getSink:e.dialog}};return{popup:g,dialog:p}})({popup:()=>dn.fromOption(r.popupUi.get().map((e=>e.sink)),"(popup) UI has not been rendered"),dialog:()=>dn.fromOption(r.dialogUi.get().map((e=>e.sink)),"UI has not been rendered")},e,h,f),x=()=>{const t=(()=>{const t={attributes:{[Qc]:d?Jc.BottomToTop:Jc.TopToBottom}},o=XD.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:y.popup,onEscape:()=>{e.focus()}}),n=XD.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:y.popup.shared.getSink,providers:y.popup.shared.providers,onEscape:()=>{e.focus()},onToolbarToggled:t=>{((e,t)=>{e.dispatch("ToggleToolbarDrawer",{state:t})})(e,t)},type:u,lazyToolbar:b,lazyHeader:()=>p().getOrDie("Could not find header element"),...t}),s=XD.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:y.popup.shared.providers,onEscape:()=>{e.focus()},type:u}),r=Sb(e),a=xb(e),i=fb(e),l=mb(e),c=XD.parts.promotion({dom:{tag:"div",classes:["tox-promotion"]}}),g=r||a||i,h=l?[c,o]:[o];return XD.parts.header({dom:{tag:"div",classes:["tox-editor-header"].concat(g?[]:["tox-editor-header--empty"]),...t},components:G([i?h:[],r?[s]:a?[n]:[],Ob(e)?[]:[m.asSpec()]]),sticky:Eb(e),editor:e,sharedBackstage:y.popup.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[XD.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),XD.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},s=XD.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:y.popup}),r=XD.parts.viewWrapper({backstage:y.popup}),i=ab(e)&&!o?A.some(aR(e,y.popup.shared.providers)):A.none(),l=G([d?[]:[t],o?[]:[n],d?[t]:[]]),h=XD.parts.editorContainer({components:G([l,o?[]:[g.asSpec()]])}),f=Tb(e),v={role:"application",...Kv.isRtl()?{dir:"rtl"}:{},...f?{"aria-hidden":"true"}:{}},x=hl(XD.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(o?["tox-tinymce-inline"]:[]).concat(d?["tox-tinymce--toolbar-bottom"]:[]).concat(c),styles:{visibility:"hidden",...f?{opacity:"0",border:"0"}:{}},attributes:v},components:[h,...o?[]:[r,...i.toArray()],s],behaviours:ma([dw(),pg.config({disableClass:"tox-tinymce--disabled"}),xh.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),w=Wk(x);return a.set(w),{mothership:w,outerContainer:x}},w=t=>{const o=cI((e=>{const t=(e=>{const t=Bf(e),o=Rf(e),n=zf(e);return lI(t).map((e=>dI(e,o,n)))})(e);return t.getOr(Bf(e))})(e)),n=cI((e=>uI(e).getOr(If(e)))(e));return e.inline||(Vt("div","width",n)&&Bt(t.element,"width",n),Vt("div","height",o)?Bt(t.element,"height",o):Bt(t.element,"height","400px")),o};return{popups:{backstage:y.popup,getMothership:()=>iR("popups",l)},dialogs:{backstage:y.dialog,getMothership:()=>iR("dialogs",i)},renderUI:()=>{const o=x(),a=(()=>{const t=_b(e),o=Ze(wt(),t)&&"grid"===Rt(t,"display"),n={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(c),attributes:{...Kv.isRtl()?{dir:"rtl"}:{}}},behaviours:ma([tu.config({useFixed:()=>s.isDocked(p)})])},r={dom:{styles:{width:document.body.clientWidth+"px"}},events:Wr([Gr(Or(),(e=>{Bt(e.element,"width",document.body.clientWidth+"px")}))])},a=hl(wn(n,o?r:{})),l=Wk(a);return i.set(l),{sink:a,mothership:l}})(),d=Ab(e)?(()=>{const e={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-silver-popup-sink","tox-tinymce-aux"].concat(c),attributes:{...Kv.isRtl()?{dir:"rtl"}:{}}},behaviours:ma([tu.config({useFixed:()=>s.isDocked(p),getBounds:()=>t.getPopupSinkBounds()})])},o=hl(e),n=Wk(o);return l.set(n),{sink:o,mothership:n}})():(e=>(l.set(e.mothership),e))(a);r.dialogUi.set(a),r.popupUi.set(d),r.mainUi.set(o);return(t=>{const{mainUi:o,popupUi:r,uiMotherships:a}=t;le(jf(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:i,menuItems:l,contextToolbars:c,sidebars:d,views:m}=e.ui.registry.getAll(),g=wb(e),h={menuItems:l,menus:Mb(e),menubar:Kf(e),toolbar:g.getOrThunk((()=>Jf(e))),allowToolbarGroups:u===Sf.floating,buttons:i,sidebar:d,views:m};var f;f=o.outerContainer,e.addShortcut("alt+F9","focus menubar",(()=>{XD.focusMenubar(f)})),e.addShortcut("alt+F10","focus toolbar",(()=>{XD.focusToolbar(f)})),e.addCommand("ToggleToolbarDrawer",((e,t)=>{(null==t?void 0:t.skipFocus)?XD.toggleToolbarDrawerWithoutFocusing(f):XD.toggleToolbarDrawer(f)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>XD.isToolbarDrawerToggled(f))),((e,t,o)=>{const n=(e,n)=>{V([t,...o],(t=>{t.broadcastEvent(e,n)}))},s=(e,n)=>{V([t,...o],(t=>{t.broadcastOn([e],n)}))},r=e=>s(Eu(),{target:e.target}),a=qo(),i=Fc(a,"touchstart",r),l=Fc(a,"touchmove",(e=>n(Sr(),e))),c=Fc(a,"touchend",(e=>n(kr(),e))),d=Fc(a,"mousedown",r),u=Fc(a,"mouseup",(e=>{0===e.raw.button&&s(Mu(),{target:e.target})})),m=e=>s(Eu(),{target:ze(e.target)}),g=e=>{0===e.button&&s(Mu(),{target:ze(e.target)})},p=()=>{V(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},h=e=>n(Cr(),Nc(e)),f=e=>{s(Au(),{}),n(Or(),Nc(e))},b=ft(ze(e.getElement())),v=Rc(b,"scroll",(o=>{requestAnimationFrame((()=>{if(null!=e.getContainer()){const s=Rb(e,t.element).map((e=>[e.element,...e.others])).getOr([]);R(s,(e=>Ze(e,o.target)))&&(e.dispatch("ElementScroll",{target:o.target.dom}),n(Br(),o))}}))})),y=()=>s(Au(),{}),x=t=>{t.state&&s(Eu(),{target:ze(e.getContainer())})},w=e=>{s(Eu(),{target:ze(e.relatedTarget.getContainer())})},S=t=>e.dispatch("focusin",t),k=t=>e.dispatch("focusout",t);e.on("PostRender",(()=>{e.on("click",m),e.on("tap",m),e.on("mouseup",g),e.on("mousedown",p),e.on("ScrollWindow",h),e.on("ResizeWindow",f),e.on("ResizeEditor",y),e.on("AfterProgressState",x),e.on("DismissPopups",w),V([t,...o],(e=>{e.element.dom.addEventListener("focusin",S),e.element.dom.addEventListener("focusout",k)}))})),e.on("remove",(()=>{e.off("click",m),e.off("tap",m),e.off("mouseup",g),e.off("mousedown",p),e.off("ScrollWindow",h),e.off("ResizeWindow",f),e.off("ResizeEditor",y),e.off("AfterProgressState",x),e.off("DismissPopups",w),V([t,...o],(e=>{e.element.dom.removeEventListener("focusin",S),e.element.dom.removeEventListener("focusout",k)})),d.unbind(),i.unbind(),l.unbind(),c.unbind(),u.unbind(),v.unbind()})),e.on("detach",(()=>{V([t,...o],fu),V([t,...o],(e=>e.destroy()))}))})(e,o.mothership,a),s.setup(e,y.popup.shared,p),jI(e,y.popup),uF(e,y.popup.shared.getSink,y.popup),(e=>{const{sidebars:t}=e.ui.registry.getAll();V(re(t),(o=>{const n=t[o],s=()=>ye(A.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(s())},onSetup:t=>{t.setActive(s());const o=()=>t.setActive(s());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}}})}))})(e),WM(e,v,y.popup.shared),RI(e,c,r.sink,{backstage:y.popup}),tR(e,r.sink);const b={targetNode:e.getElement(),height:w(o.outerContainer)};return n.render(e,t,h,y.popup,b)})({popupUi:d,dialogUi:a,mainUi:o,uiMotherships:r.getUiMotherships()})}}},cR=x([is("lazySink"),fs("dragBlockClass"),Ms("getBounds",tn),Cs("useTabstopAt",E),Cs("firstTabstop",0),Cs("eventOrder",{}),Yu("modalBehaviours",[xh]),wi("onExecute"),ki("onEscape")]),dR={sketch:w},uR=x([km({name:"draghandle",overrides:(e,t)=>({behaviours:ma([JF.config({mode:"mouse",getTarget:e=>kl(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),wm({schema:[is("dom")],name:"title"}),wm({factory:dR,schema:[is("dom")],name:"close"}),wm({factory:dR,schema:[is("dom")],name:"body"}),km({factory:dR,schema:[is("dom")],name:"footer"}),Sm({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[Cs("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),Cs("components",[])],name:"blocker"})]),mR=Km({name:"ModalDialog",configFields:cR(),partFields:uR(),factory:(e,t,o,n)=>{const s=rn(),r=Bi("modal-events"),a={...e.eventOrder,[_r()]:[r].concat(e.eventOrder["alloy.system.attached"]||[])},i=Bo();return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{s.set(t);const o=e.lazySink(t).getOrDie(),r=n.blocker(),a=o.getSystem().build({...r,components:r.components.concat([fl(t)]),behaviours:ma([Rh.config({}),Mh("dialog-blocker-events",[Zr(Qs(),(()=>{PM.isBlocked(t)||xh.focusIn(t)}))])])});lu(o,a),xh.focusIn(t)},hide:e=>{s.clear(),rt(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{uu(e)}))}))},getBody:t=>Nm(t,e,"body"),getFooter:t=>Rm(t,e,"footer"),setIdle:e=>{PM.unblock(e)},setBusy:(e,t)=>{PM.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Ku(e.modalBehaviours,[Ah.config({}),xh.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt,firstTabstop:e.firstTabstop}),PM.config({getRoot:s.get}),Mh(r,[ea((t=>{const o=Nm(t,e,"title").element,n=(e=>e.dom.textContent)(o);i.os.isMacOS()&&g(n)?Ct(t.element,"aria-label",n):((e,t)=>{const o=Tt(e,"id").fold((()=>{const e=Bi("dialog-label");return Ct(t,"id",e),e}),w);Ct(e,"aria-labelledby",o)})(t.element,o)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),gR=Nn([Jy,Qy].concat(Yx)),pR=$n,hR=[Ox("button"),mx,Es("align","end",["start","end"]),xx,yx,xs("buttonType",["primary","secondary"])],fR=[...hR,ex],bR=[us("type",["submit","cancel","custom"]),...fR],vR=[us("type",["menu"]),dx,gx,mx,hs("items",gR),...hR],yR=[...hR,us("type",["togglebutton"]),gx,mx,dx,As("active",!1)],xR=os("type",{submit:bR,cancel:bR,custom:bR,menu:vR,togglebutton:yR}),wR=[Jy,ex,us("level",["info","warn","error","success"]),ox,Cs("url","")],SR=Nn(wR),kR=[Jy,ex,yx,Ox("button"),mx,vx,xs("buttonType",["primary","secondary","toolbar"]),xx],CR=Nn(kR),OR=[Jy,Qy],_R=OR.concat([px]),TR=OR.concat([Zy,yx]),ER=Nn(TR),AR=$n,MR=_R.concat([wx("auto")]),DR=Nn(MR),BR=Hn([nx,ex,ox]),IR=_R.concat([Ts("storageKey","default")]),FR=Nn(IR),RR=jn,NR=Nn(_R),zR=jn,LR=OR.concat([Ts("tag","textarea"),ds("scriptId"),ds("scriptUrl"),ws("onFocus"),Os("settings",void 0,Yn)]),VR=OR.concat([Ts("tag","textarea"),ms("init")]),HR=Kn((e=>Qn("customeditor.old",Rn(VR),e).orThunk((()=>Qn("customeditor.new",Rn(LR),e))))),PR=jn,UR=Nn(_R),WR=zn(Mn),jR=e=>[Jy,cs("columns"),e],$R=[Jy,ds("html"),Es("presets","presentation",["presentation","document"]),Ms("onInit",b),As("stretched",!1)],GR=Nn($R),qR=_R.concat([As("border",!1),As("sandboxed",!0),As("streamContent",!1),As("transparent",!0)]),YR=Nn(qR),XR=jn,KR=Nn(OR.concat([ys("height")])),JR=Nn([ds("url"),vs("zoom"),vs("cachedWidth"),vs("cachedHeight")]),QR=_R.concat([ys("inputMode"),ys("placeholder"),As("maximized",!1),yx]),ZR=Nn(QR),eN=jn,tN=e=>[Jy,Zy,e,Es("align","start",["start","center","end"]),ys("for")],oN=[ex,nx],nN=[ex,hs("items",ns(0,(()=>sN)))],sN=Ln([Nn(oN),Nn(nN)]),rN=_R.concat([hs("items",sN),yx]),aN=Nn(rN),iN=jn,lN=_R.concat([ps("items",[ex,nx]),_s("size",1),yx]),cN=Nn(lN),dN=jn,uN=_R.concat([As("constrain",!0),yx]),mN=Nn(uN),gN=Nn([ds("width"),ds("height")]),pN=OR.concat([Zy,_s("min",0),_s("max",0)]),hN=Nn(pN),fN=Wn,bN=[Jy,hs("header",jn),hs("cells",zn(jn))],vN=Nn(bN),yN=_R.concat([ys("placeholder"),As("maximized",!1),yx]),xN=Nn(yN),wN=jn,SN=[us("type",["directory","leaf"]),tx,ds("id"),bs("menu",wM)],kN=Nn(SN),CN=SN.concat([hs("children",ns(0,(()=>Xn("type",{directory:ON,leaf:kN}))))]),ON=Nn(CN),_N=Xn("type",{directory:ON,leaf:kN}),TN=[Jy,hs("items",_N),ws("onLeafAction"),ws("onToggleExpand"),Ds("defaultExpandedIds",[],jn),ys("defaultSelectedId")],EN=Nn(TN),AN=_R.concat([Es("filetype","file",["image","media","file"]),yx,ys("picker_text")]),MN=Nn(AN),DN=Nn([nx,Sx]),BN=e=>ss("items","items",{tag:"required",process:{}},zn(Kn((t=>Qn(`Checking item of ${e}`,IN,t).fold((e=>dn.error(ts(e))),(e=>dn.value(e))))))),IN=In((()=>{return Xn("type",{alertbanner:SR,bar:Nn((e=BN("bar"),[Jy,e])),button:CR,checkbox:ER,colorinput:FR,colorpicker:NR,dropzone:UR,grid:Nn(jR(BN("grid"))),iframe:YR,input:ZR,listbox:aN,selectbox:cN,sizeinput:mN,slider:hN,textarea:xN,urlinput:MN,customeditor:HR,htmlpanel:GR,imagepreview:KR,collection:DR,label:Nn(tN(BN("label"))),table:vN,tree:EN,panel:RN});var e})),FN=[Jy,Cs("classes",[]),hs("items",IN)],RN=Nn(FN),NN=[Ox("tab"),tx,hs("items",IN)],zN=[Jy,ps("tabs",NN)],LN=Nn(zN),VN=fR,HN=xR,PN=Nn([ds("title"),ls("body",Xn("type",{panel:RN,tabpanel:LN})),Ts("size","normal"),Ds("buttons",[],HN),Cs("initialData",{}),Ms("onAction",b),Ms("onChange",b),Ms("onSubmit",b),Ms("onClose",b),Ms("onCancel",b),Ms("onTabChange",b)]),UN=Nn([us("type",["cancel","custom"]),...VN]),WN=Nn([ds("title"),ds("url"),vs("height"),vs("width"),Ss("buttons",UN),Ms("onAction",b),Ms("onCancel",b),Ms("onClose",b),Ms("onMessage",b)]),jN=e=>a(e)?[e].concat(q(he(e),jN)):l(e)?q(e,jN):[],$N=e=>r(e.type)&&r(e.name),GN={checkbox:AR,colorinput:RR,colorpicker:zR,dropzone:WR,input:eN,iframe:XR,imagepreview:JR,selectbox:dN,sizeinput:gN,slider:fN,listbox:iN,size:gN,textarea:wN,urlinput:DN,customeditor:PR,collection:BR,togglemenuitem:pR},qN=e=>{const t=(e=>P(jN(e),$N))(e),o=q(t,(e=>(e=>A.from(GN[e.type]))(e).fold((()=>[]),(t=>[ls(e.name,t)]))));return Nn(o)},YN=e=>{var t;return{internalDialog:Zn(Qn("dialog",PN,e)),dataValidator:qN(e),initialData:null!==(t=e.initialData)&&void 0!==t?t:{}}},XN={open:(e,t)=>{const o=YN(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(Zn(Qn("dialog",WN,t))),redial:e=>YN(e)};var KN=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const s=e(o,n);t.set(s)})),e.renderComponents.each((s=>{const r=s(n,t.get());(e.reuseDom?Sh:wh)(o,r)}))};return Wr([Gr(pr(),((t,n)=>{const s=n;if(!s.universal){const n=e.channel;F(s.channels,n)&&o(t,s.data)}})),ea(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),JN=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),QN=[is("channel"),fs("renderComponents"),fs("updateState"),fs("initialData"),As("reuseDom",!0)];const ZN=pa({fields:QN,name:"reflecting",active:KN,apis:JN,state:Object.freeze({__proto__:null,init:()=>{const e=on(A.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(A.none())}}})}),ez=e=>{const t=[],o={};return ie(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?dn.error(t):dn.value(o)},tz=(e,t,o,n)=>{const s=Vb(u_.sketch((s=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:L(e.items,(e=>NE(s,e,t,o,n)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[s.asSpec()]}],behaviours:ma([xh.config({mode:"acyclic",useTabstopAt:C(N_)}),(r=s,eg.config({find:r.getOpt})),w_(s,{postprocess:e=>ez(e).fold((e=>(console.error(e),{})),w)}),Mh("dialog-body-panel",[Gr(Qs(),((e,t)=>{e.getSystem().broadcastOn([U_],{newFocus:A.some(t.event.target)})}))])])};var r},oz=Xm({name:"TabButton",configFields:[Cs("uid",void 0),is("value"),ss("dom","dom",On((()=>({attributes:{role:"tab",id:Bi("aria"),"aria-selected":"false"}}))),Pn()),fs("action"),Cs("domModification",{}),Yu("tabButtonBehaviours",[Rh,xh,qu]),is("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:$h(e.action),behaviours:Ku(e.tabButtonBehaviours,[Rh.config({}),xh.config({mode:"execution",useSpace:!0,useEnter:!0}),qu.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),nz=x([is("tabs"),is("dom"),Cs("clickToDismiss",!1),Yu("tabbarBehaviours",[Cg,xh]),vi(["tabClass","selectedClass"])]),sz=Cm({factory:oz,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Cg.dehighlight(e,t),Lr(e,Fr(),{tabbar:e,button:t})},o=(e,t)=>{Cg.highlight(e,t),Lr(e,Ir(),{tabbar:e,button:t})};return{action:n=>{const s=n.getSystem().getByUid(e.uid).getOrDie(),r=Cg.isHighlighted(s,n);(r&&e.clickToDismiss?t:r?b:o)(s,n)},domModification:{classes:[e.markers.tabClass]}}}}),rz=x([sz]),az=Km({name:"Tabbar",configFields:nz(),partFields:rz(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Ku(e.tabbarBehaviours,[Cg.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{Ct(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{Ct(t.element,"aria-selected","false")}}),xh.config({mode:"flow",getInitial:e=>Cg.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),iz=Xm({name:"Tabview",configFields:[Yu("tabviewBehaviours",[Ah])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:Ku(e.tabviewBehaviours,[Ah.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),lz=x([Cs("selectFirst",!0),xi("onChangeTab"),xi("onDismissTab"),Cs("tabs",[]),Yu("tabSectionBehaviours",[])]),cz=wm({factory:az,schema:[is("dom"),gs("markers",[is("tabClass"),is("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),dz=wm({factory:iz,name:"tabview"}),uz=x([cz,dz]),mz=Km({name:"TabSection",configFields:lz(),partFields:uz(),factory:(e,t,o,n)=>{const s=(t,o)=>{Rm(t,e,"tabbar").each((e=>{o(e).each(Vr)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:Xu(e.tabSectionBehaviours),events:Wr(G([e.selectFirst?[ea(((e,t)=>{s(e,Cg.getFirst)}))]:[],[Gr(Ir(),((t,o)=>{(t=>{const o=qu.getValue(t);Rm(t,e,"tabview").each((n=>{j(e.tabs,(e=>e.value===o)).each((o=>{const s=o.view();Tt(t.element,"id").each((e=>{Ct(n.element,"aria-labelledby",e)})),Ah.set(n,s),e.onChangeTab(n,t,s)}))}))})(o.event.button)})),Gr(Fr(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>Rm(t,e,"tabview").map((e=>Ah.contents(e))).getOr([]),showTab:(e,t)=>{s(e,(e=>{const o=Cg.getCandidates(e);return j(o,(e=>qu.getValue(e)===t)).filter((t=>!Cg.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),gz=(e,t)=>{Bt(e,"height",t+"px"),Bt(e,"flex-basis",t+"px")},pz=(e,t,o)=>{kl(e,'[role="dialog"]').each((e=>{_l(e,'[role="tablist"]').each((n=>{o.get().map((o=>(Bt(t,"height","0"),Bt(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=nt(e).dom,s=kl(e,".tox-dialog-wrap").getOr(e);let r;r="fixed"===Rt(s,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=jt(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+Qt(o)?Math.max(jt(o),a):a,l=parseInt(Rt(e,"margin-top"),10)||0,c=parseInt(Rt(e,"margin-bottom"),10)||0;return r-(jt(e)+l+c-i)})(e,t,n))))).each((e=>{gz(t,e)}))}))}))},hz=e=>_l(e,'[role="tabpanel"]'),fz="send-data-to-section",bz="send-data-to-view",vz=(e,t,o,n)=>{const s=on({}),r=e=>{const t=qu.getValue(e),o=ez(t).getOr({}),n=s.get(),r=wn(n,o);s.set(r)},a=e=>{const t=s.get();qu.setValue(e,t)},i=on(null),l=L(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[ul(o.shared.providers.translate(e.title))],view:()=>[u_.sketch((s=>({dom:{tag:"div",classes:["tox-form"]},components:L(e.items,(e=>NE(s,e,t,o,n))),formBehaviours:ma([xh.config({mode:"acyclic",useTabstopAt:C(N_)}),Mh("TabView.form.events",[ea(a),ta(r)]),gc.config({channels:Rs([{key:fz,value:{onReceive:r}},{key:bz,value:{onReceive:a}}])})])})))]}))),c=(e=>{const t=rn(),o=[ea((o=>{const n=o.element;hz(n).each((s=>{Bt(s,"visibility","hidden"),o.getSystem().getByDom(s).toOptional().each((o=>{const n=((e,t,o)=>L(e,((n,s)=>{Ah.set(o,e[s].view());const r=t.dom.getBoundingClientRect();return Ah.set(o,[]),r.height})))(e,s,o),r=(e=>te(Z(e,((e,t)=>e>t?-1:e<t?1:0))))(n);r.fold(t.clear,t.set)})),pz(n,s,t),Ht(s,"visibility"),((e,t)=>{te(e).each((e=>mz.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{pz(n,s,t)}))}))})),Gr(Or(),(e=>{const o=e.element;hz(o).each((e=>{pz(o,e,t)}))})),Gr(aC,((e,o)=>{const n=e.element;hz(n).each((e=>{const o=vc(ft(e));Bt(e,"visibility","hidden");const s=zt(e,"height").map((e=>parseInt(e,10)));Ht(e,"height"),Ht(e,"flex-basis");const r=e.dom.getBoundingClientRect().height;s.forall((e=>r>e))?(t.set(r),pz(n,e,t)):s.each((t=>{gz(e,t)})),Ht(e,"visibility"),o.each(hc)}))}))];return{extraEvents:o,selectFirst:!1}})(l);return mz.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=qu.getValue(t);Lr(e,rC,{name:n,oldName:i.get()}),i.set(n)},tabs:l,components:[mz.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[az.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:ma([Ub.config({})])}),mz.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:c.selectFirst,tabSectionBehaviours:ma([Mh("tabpanel",c.extraEvents),xh.config({mode:"acyclic"}),eg.config({find:e=>te(mz.getViewItems(e))}),S_(A.none(),(e=>(e.getSystem().broadcastOn([fz],{}),s.get())),((e,t)=>{s.set(t),e.getSystem().broadcastOn([bz],{})}))])})},yz=(e,t,o,n,s,r)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...s?{"aria-live":"polite"}:{}}},components:[],behaviours:ma([y_(0),ZN.config({channel:`${V_}-${t}`,updateState:(e,t)=>A.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[vz(t,e.initialData,n,r)]:[tz(t,e.initialData,n,r)]},initialData:e})])}),xz=Tf.deviceType.isTouch(),wz=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),Sz=(e,t)=>mR.parts.close(zb.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:ma([Ub.config({})])})),kz=()=>mR.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),Cz=(e,t)=>mR.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:Lb(`<p>${Xv(t.translate(e))}</p>`)}]}]}),Oz=e=>mR.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),_z=(e,t)=>[Uk.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),Uk.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],Tz=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",s=t+"__disable-scroll";return mR.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),A.some(!0)),useTabstopAt:e=>!N_(e),firstTabstop:e.firstTabstop,dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:Lb(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:xz?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:ma([Rh.config({}),Mh("dialog-events",e.dialogEvents.concat([Zr(Qs(),((e,t)=>{PM.isBlocked(e)||xh.focusIn(e)})),Gr(Mr(),((e,t)=>{e.getSystem().broadcastOn([U_],{newFocus:t.event.newFocus})}))])),Mh("scroll-lock",[ea((()=>{ka(wt(),s)})),ta((()=>{Oa(wt(),s)}))]),...e.extraBehaviours]),eventOrder:{[hr()]:["dialog-events"],[_r()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[Tr()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},Ez=e=>zb.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),"data-mce-name":"close"}},buttonBehaviours:ma([Ub.config({}),Zb.config(e.tooltips.getConfig({tooltipText:e.translate("Close")}))]),components:[ry("close",{tag:"span",classes:["tox-icon"]},e.icons)],action:e=>{zr(e,eC)}}),Az=(e,t,o,n)=>({dom:{tag:"h1",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:ma([ZN.config({channel:`${L_}-${t}`,initialData:e,renderComponents:e=>[ul(n.translate(e.title))]})])}),Mz=()=>({dom:Lb('<div class="tox-dialog__draghandle"></div>')}),Dz=(e,t,o)=>((e,t,o)=>{const n=mR.parts.title(Az(e,t,A.none(),o)),s=mR.parts.draghandle(Mz()),r=mR.parts.close(Ez(o)),a=[n].concat(e.draggable?[s]:[]).concat([r]);return Uk.sketch({dom:Lb('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),Bz=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:`${n.getOr(0)}px`,position:"absolute"}},behaviours:t,components:[{dom:Lb('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),Iz=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{const n=_l(e().element,".tox-dialog__header").map((e=>jt(e)));mR.setBusy(e(),((e,s)=>Bz(o.message,s,t,n)))},onUnblock:()=>{mR.setIdle(e())}}),Fz="tox-dialog--fullscreen",Rz="tox-dialog--width-lg",Nz="tox-dialog--width-md",zz=e=>{switch(e){case"large":return A.some(Rz);case"medium":return A.some(Nz);default:return A.none()}},Lz=(e,t)=>{const o=ze(t.element.dom);_a(o,Fz)||(Ea(o,[Rz,Nz]),zz(e).each((e=>ka(o,e))))},Vz=(e,t)=>{const o=ze(e.element.dom),n=Aa(o),s=j(n,(e=>e===Rz||e===Nz)).or(zz(t));((e,t)=>{V(t,(t=>{((e,t)=>{const o=ya(e)?e.dom.classList.toggle(t):((e,t)=>F(xa(e),t)?Sa(e,t):wa(e,t))(e,t);Ca(e)})(e,t)}))})(o,[Fz,...s.toArray()])},Hz=(e,t,o)=>hl(Tz({...e,firstTabstop:1,lazySink:o.shared.getSink,extraBehaviours:[C_({}),...e.extraBehaviours],onEscape:e=>{zr(e,eC)},dialogEvents:t,eventOrder:{[pr()]:[ZN.name(),gc.name()],[_r()]:["scroll-lock",ZN.name(),"messages","dialog-events","alloy.base.behaviour"],[Tr()]:["alloy.base.behaviour","dialog-events","messages",ZN.name(),"scroll-lock"]}})),Pz=(e,t={})=>L(e,(e=>"menu"===e.type?(e=>{const o=L(e.items,(e=>{const o=fe(t,e.name).getOr(on(!1));return{...e,storage:o}}));return{...e,items:o}})(e):e)),Uz=e=>W(e,((e,t)=>"menu"===t.type?W(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),Wz=(e,t)=>[Kr(Qs(),R_),e(Zk,((e,o,n,s)=>{vc(ft(s.element)).fold(b,fc),t.onClose(),o.onClose()})),e(eC,((e,t,o,n)=>{t.onCancel(e),zr(n,Zk)})),Gr(sC,((e,o)=>t.onUnblock())),Gr(nC,((e,o)=>t.onBlock(o.event)))],jz=(e,t,o)=>{const n=(t,o)=>Gr(t,((t,n)=>{s(t,((s,r)=>{o(e(),s,n.event,t)}))})),s=(e,t)=>{ZN.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...Wz(n,t),n(oC,((e,t)=>t.onSubmit(e))),n(Qk,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(tC,((e,t,n,s)=>{const r=()=>s.getSystem().isConnected()?xh.focusIn(s):void 0,a=e=>Et(e,"disabled")||Tt(e,"aria-disabled").exists((e=>"true"===e)),i=ft(s.element),l=vc(i);t.onAction(e,{name:n.name,value:n.value}),vc(i).fold(r,(e=>{a(e)||l.exists((t=>et(e,t)&&a(t)))?r():o().toOptional().filter((t=>!et(t.element,e))).each(r)}))})),n(rC,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),ta((t=>{const o=e();qu.setValue(t,o.getData())}))]},$z=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=H(o,(e=>"start"===e.align)),s=(e,t)=>Uk.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:L(t,(e=>e.memento.asSpec()))});return[s("start",n.pass),s("end",n.fail)]},Gz=(e,t,o)=>({dom:Lb('<div class="tox-dialog__footer"></div>'),components:[],behaviours:ma([ZN.config({channel:`${H_}-${t}`,initialData:e,updateState:(e,t)=>{const n=L(t.buttons,(e=>{const t=Vb(((e,t)=>xE(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return A.some({lookupByName:t=>((e,t,o)=>j(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:$z})])}),qz=(e,t,o)=>mR.parts.footer(Gz(e,t,o)),Yz=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=eg.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return u_.getField(o,t).orThunk((()=>{const o=e.getFooter().bind((e=>ZN.getState(e).get()));return o.bind((e=>e.lookupByName(t)))}))}return A.none()},Xz=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},s={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...qu.getValue(n),...le(o,(e=>e.get()))}},setData:t=>{n((n=>{const r=s.getData(),a=wn(r,t),i=((e,t)=>{const o=e.getRoot();return ZN.getState(o).get().map((e=>Zn(Qn("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();qu.setValue(l,i),ie(o,((e,t)=>{be(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{Yz(e,t).each(o?pg.enable:pg.disable)},focus:t=>{Yz(e,t).each(Rh.focus)},block:e=>{if(!r(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{Lr(t,nC,{message:e})}))},unblock:()=>{n((e=>{zr(e,sC)}))},showTab:t=>{n((o=>{const n=e.getBody();ZN.getState(n).get().exists((e=>e.isTabPanel()))&&eg.getCurrent(n).each((e=>{mz.showTab(e,t)}))}))},redial:r=>{n((n=>{const a=e.getId(),i=t(r),l=Pz(i.internalDialog.buttons,o);n.getSystem().broadcastOn([`${z_}-${a}`],i),n.getSystem().broadcastOn([`${L_}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${V_}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${H_}-${a}`],{...i.internalDialog,buttons:l}),s.setData(i.initialData)}))},close:()=>{n((e=>{zr(e,Zk)}))},toggleFullscreen:e.toggleFullscreen};return s},Kz=(e,t,o,n=!1,s)=>{const r=Bi("dialog"),a=Bi("dialog-label"),i=Bi("dialog-content"),l=e.internalDialog,c=on(l.size),d=zz(c.get()).toArray(),u=Vb(((e,t,o,n)=>Uk.sketch({dom:Lb('<div class="tox-dialog__header"></div>'),components:[Az(e,t,A.some(o),n),Mz(),Ez(n)],containerBehaviours:ma([JF.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>Tl(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))({title:l.title,draggable:!0},r,a,o.shared.providers)),m=Vb(((e,t,o,n,s,r)=>yz(e,t,A.some(o),n,s,r))({body:l.body,initialData:l.initialData},r,i,o,n,(e=>Yz(y,e)))),g=Pz(l.buttons),p=Uz(g),h=ke(0!==g.length,Vb(((e,t,o)=>Gz(e,t,o))({buttons:g},r,o))),f=jz((()=>w),{onBlock:e=>{PM.block(v,((t,n)=>{const s=u.getOpt(v).map((e=>jt(e.element)));return Bz(e.message,n,o.shared.providers,s)}))},onUnblock:()=>{PM.unblock(v)},onClose:()=>t.closeWindow()},o.shared.getSink),b=Bo().os,v=hl({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline",...d],attributes:{role:"dialog",...b.isMacOS()?{"aria-label":l.title}:{"aria-labelledby":a}}},eventOrder:{[pr()]:[ZN.name(),gc.name()],[hr()]:["execute-on-form"],[_r()]:["reflecting","execute-on-form"]},behaviours:ma([xh.config({mode:"cyclic",onEscape:e=>(zr(e,Zk),A.some(!0)),useTabstopAt:e=>!N_(e)&&("button"!==Ue(e)||"disabled"!==_t(e,"disabled")),firstTabstop:1}),ZN.config({channel:`${z_}-${r}`,updateState:(e,t)=>(c.set(t.internalDialog.size),Lz(t.internalDialog.size,e),s(),A.some(t)),initialData:e}),Rh.config({}),Mh("execute-on-form",f.concat([Zr(Qs(),((e,t)=>{xh.focusIn(e)})),Gr(Mr(),((e,t)=>{e.getSystem().broadcastOn([U_],{newFocus:t.event.newFocus})}))])),PM.config({getRoot:()=>A.some(v)}),Ah.config({}),C_({})]),components:[u.asSpec(),m.asSpec(),...h.map((e=>e.asSpec())).toArray()]}),y={getId:x(r),getRoot:x(v),getFooter:()=>h.map((e=>e.get(v))),getBody:()=>m.get(v),getFormWrapper:()=>{const e=m.get(v);return eg.getCurrent(e).getOr(e)},toggleFullscreen:()=>{Vz(v,c.get())}},w=Xz(y,t.redial,p);return{dialog:v,instanceApi:w}};var Jz=tinymce.util.Tools.resolve("tinymce.util.URI");const Qz=["insertContent","setContent","execCommand","close","block","unblock"],Zz=e=>a(e)&&-1!==Qz.indexOf(e.mceAction),eL=(e,t,o,n)=>{const s=Bi("dialog"),i=Dz(e.title,s,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[I_(A.none(),{dom:{tag:"iframe",attributes:{src:e.url}},behaviours:ma([Ub.config({}),Rh.config({})])})]}],behaviours:ma([xh.config({mode:"acyclic",useTabstopAt:C(N_)})])};return mR.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?A.none():A.some(qz({buttons:e},s,n)))),u=((e,t)=>{const o=(e,t)=>Gr(e,((e,o)=>{n(e,((n,s)=>{t(x,n,o.event,e)}))})),n=(e,t)=>{ZN.getState(e).get().each((o=>{t(o,e)}))};return[...Wz(o,t),o(tC,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})(0,Iz((()=>y),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},p=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],h=new Jz(e.url,{base_uri:new Jz(window.location.href)}),f=`${h.protocol}://${h.host}${h.port?":"+h.port:""}`,b=sn(),v=[ZN.config({channel:`${z_}-${s}`,updateState:(e,t)=>A.some(t),initialData:e}),Mh("messages",[ea((()=>{const t=Fc(ze(window),"message",(t=>{if(h.isSameOrigin(new Jz(t.raw.origin))){const n=t.raw.data;Zz(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,x,n):(e=>!Zz(e)&&a(e)&&be(e,"mceAction"))(n)&&e.onMessage(x,n)}}));b.set(t)})),ta(b.clear)]),gc.config({channels:{[P_]:{onReceive:(e,t)=>{_l(e.element,"iframe").each((e=>{const o=e.dom.contentWindow;g(o)&&o.postMessage(t,f)}))}}}})],y=Hz({id:s,header:i,body:l,footer:c,extraClasses:p,extraBehaviours:v,extraStyles:m},u,n),x=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!r(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{Lr(t,nC,{message:e})}))},unblock:()=>{t((e=>{zr(e,sC)}))},close:()=>{t((e=>{zr(e,Zk)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([P_],e)}))}}})(y);return{dialog:y,instanceApi:x}},tL=(e,t)=>Zn(Qn("data",t,e)),oL=e=>Bk(e,".tox-alert-dialog")||Bk(e,".tox-confirm-dialog"),nL=(e,t,o)=>t&&o?[]:[Ei.config({contextual:{lazyContext:()=>A.some(Qo(ze(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"],lazyViewport:t=>Rb(e,t.element).map((e=>({bounds:Nb(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Xt(e.element).top})}))).getOrThunk((()=>({bounds:tn(),optScrollEnv:A.none()})))})],sL=e=>{const t=e.editor,o=Eb(t),n=(e=>{const t=e.shared;return{open:(o,n)=>{const s=()=>{mR.hide(l),n()},r=Vb(xE({name:"close-alert",text:"OK",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"cancel",e)),a=kz(),i=Sz(s,t.providers),l=hl(Tz({lazySink:()=>t.getSink(),header:wz(a,i),body:Cz(o,t.providers),footer:A.some(Oz(_z([],[r.asSpec()]))),onEscape:s,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Gr(eC,s)],eventOrder:{}}));mR.show(l);const c=r.get(l);Rh.focus(c)}}})(e.backstages.dialog),s=(e=>{const t=e.shared;return{open:(o,n)=>{const s=e=>{mR.hide(c),n(e)},r=Vb(xE({name:"yes",text:"Yes",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"submit",e)),a=xE({name:"no",text:"No",primary:!1,buttonType:A.some("secondary"),align:"end",enabled:!0,icon:A.none()},"cancel",e),i=kz(),l=Sz((()=>s(!1)),t.providers),c=hl(Tz({lazySink:()=>t.getSink(),header:wz(i,l),body:Cz(o,t.providers),footer:A.some(Oz(_z([],[a,r.asSpec()]))),onEscape:()=>s(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Gr(eC,(()=>s(!1))),Gr(oC,(()=>s(!0)))],eventOrder:{}}));mR.show(c);const d=r.get(c);Rh.focus(d)}}})(e.backstages.dialog),r=(t,o)=>XN.open(((t,n,s)=>{const r=n,a=((e,t,o)=>{const n=Bi("dialog"),s=e.internalDialog,r=Dz(s.title,n,o),a=on(s.size),i=zz(a.get()).toArray(),l=((e,t,o,n)=>{const s=yz(e,t,A.none(),o,!1,n);return mR.parts.body(s)})({body:s.body,initialData:s.initialData},n,o,(e=>Yz(h,e))),c=Pz(s.buttons),d=Uz(c),u=ke(0!==c.length,qz({buttons:c},n,o)),m=jz((()=>f),Iz((()=>p),o.shared.providers,t),o.shared.getSink),g={id:n,header:r,body:l,footer:u,extraClasses:i,extraBehaviours:[ZN.config({channel:`${z_}-${n}`,updateState:(e,t)=>(a.set(t.internalDialog.size),Lz(t.internalDialog.size,e),A.some(t)),initialData:e})],extraStyles:{}},p=Hz(g,m,o),h={getId:x(n),getRoot:x(p),getBody:()=>mR.getBody(p),getFooter:()=>mR.getFooter(p),getFormWrapper:()=>{const e=mR.getBody(p);return eg.getCurrent(e).getOr(e)},toggleFullscreen:()=>{Vz(p,a.get())}},f=Xz(h,t.redial,d);return{dialog:p,instanceApi:f}})({dataValidator:s,initialData:r,internalDialog:t},{redial:XN.redial,closeWindow:()=>{mR.hide(a.dialog),o(a.instanceApi)}},e.backstages.dialog);return mR.show(a.dialog),a.instanceApi.setData(r),a.instanceApi}),t),a=(n,s,r,a)=>XN.open(((n,i,l)=>{const c=tL(i,l),d=rn(),u=e.backstages.popup.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{wf.reposition(e),o&&u||Ei.refresh(e)})),g=Kz({dataValidator:l,initialData:c,internalDialog:n},{redial:XN.redial,closeWindow:()=>{d.on(wf.hide),t.off("ResizeEditor",m),d.clear(),r(g.instanceApi)}},e.backstages.popup,a.ariaAttrs,m),p=hl(wf.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:a.persistent?{event:"doNotDismissYet"}:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:ma([Mh("window-manager-inline-events",[Gr(Er(),((e,t)=>{zr(g.dialog,eC)}))]),...nL(t,o,u)]),isExtraPart:(e,t)=>oL(t)}));return d.set(p),wf.showWithinBounds(p,fl(g.dialog),{anchor:s},(()=>{const e=t.inline?wt():ze(t.getContainer()),o=Qo(e);return A.some(o)})),o&&u||(Ei.refresh(p),t.on("ResizeEditor",m)),g.instanceApi.setData(c),xh.focusIn(g.dialog),g.instanceApi}),n),i=(o,n,s,r)=>XN.open(((o,a,i)=>{const l=tL(a,i),c=rn(),d=e.backstages.popup.shared.header.isPositionedAtTop(),u=()=>c.on((e=>{wf.reposition(e),Ei.refresh(e)})),m=Kz({dataValidator:i,initialData:l,internalDialog:o},{redial:XN.redial,closeWindow:()=>{c.on(wf.hide),t.off("ResizeEditor ScrollWindow ElementScroll",u),c.clear(),s(m.instanceApi)}},e.backstages.popup,r.ariaAttrs,u),g=hl(wf.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:r.persistent?{event:"doNotDismissYet"}:{},...d?{}:{fireRepositionEventInstead:{}},inlineBehaviours:ma([Mh("window-manager-inline-events",[Gr(Er(),((e,t)=>{zr(m.dialog,eC)}))]),Ei.config({contextual:{lazyContext:()=>A.some(Qo(ze(t.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top","bottom"],lazyViewport:e=>Rb(t,e.element).map((e=>({bounds:Nb(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Xt(e.element).top})}))).getOrThunk((()=>({bounds:tn(),optScrollEnv:A.none()})))})]),isExtraPart:(e,t)=>oL(t)}));return c.set(g),wf.showWithinBounds(g,fl(m.dialog),{anchor:n},(()=>e.backstages.popup.shared.getSink().toOptional().bind((e=>{const o=Rb(t,e.element).map((e=>Nb(e))).getOr(tn()),n=Qo(ze(t.getContentAreaContainer())),s=en(n,o);return A.some(Jo(s.x,s.y,s.width,s.height-15))})))),Ei.refresh(g),t.on("ResizeEditor ScrollWindow ElementScroll ResizeWindow",u),m.instanceApi.setData(l),xh.focusIn(m.dialog),m.instanceApi}),o);return{open:(t,o,n)=>{if(!u(o)){if("toolbar"===o.inline)return a(t,e.backstages.popup.shared.anchors.inlineDialog(),n,o);if("bottom"===o.inline)return i(t,e.backstages.popup.shared.anchors.inlineBottomDialog(),n,o);if("cursor"===o.inline)return a(t,e.backstages.popup.shared.anchors.cursor(),n,o)}return r(t,n)},openUrl:(o,n)=>((o,n)=>XN.openUrl((o=>{const s=eL(o,{closeWindow:()=>{mR.hide(s.dialog),n(s.instanceApi)}},t,e.backstages.dialog);return mR.show(s.dialog),s.instanceApi}),o))(o,n),alert:(e,t)=>{n.open(e,t)},close:e=>{e.close()},confirm:(e,t)=>{s.open(e,t)}}};an.add("silver",(e=>{(e=>{Mf(e),(e=>{const t=e.options.register,o=e=>f(e,r)?{value:xS(e),valid:!0}:{valid:!1,message:"Must be an array of strings."},n=e=>h(e)&&e>0?{value:e,valid:!0}:{valid:!1,message:"Must be a positive number."};t("color_map",{processor:o,default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_map_background",{processor:o}),t("color_map_foreground",{processor:o}),t("color_cols",{processor:n,default:CS(e)}),t("color_cols_foreground",{processor:n,default:OS(e,vS)}),t("color_cols_background",{processor:n,default:OS(e,yS)}),t("custom_colors",{processor:"boolean",default:!0}),t("color_default_foreground",{processor:"string",default:SS}),t("color_default_background",{processor:"string",default:SS})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:r(e)||f(e,r)?{value:$I(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)})(e);let t=()=>tn();const{dialogs:o,popups:n,renderUI:s}=lR(e,{getPopupSinkBounds:()=>t()});Dk(e,n.backstage.shared);const a=sL({editor:e,backstages:{popup:n.backstage,dialog:o.backstage}}),i=rn();return{renderUI:()=>{const o=s();return Rb(e,n.getMothership().element).each((e=>{t=()=>Nb(e)})),o},getWindowManagerImpl:x(a),getNotificationManagerImpl:()=>ly(e,{backstage:n.backstage},n.getMothership(),i)}}))}();