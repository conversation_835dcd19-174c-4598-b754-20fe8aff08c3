<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">

    <title><PERSON><PERSON><PERSON><PERSON></title>
    <meta content="" name="description">
    <meta content="" name="keywords">

    <!-- Favicons -->
    <link href="img/AURORALOGOSFUNDO.ico" rel="icon">
    <link href="img/AURORALOGOSFUNDO.ico" rel="apple-touch-icon">

    <!-- Google Fonts -->
    <link href="https://fonts.gstatic.com" rel="preconnect">
    <link
        href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Nunito:300,300i,400,400i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i"
        rel="stylesheet">

    <!-- Vendor CSS Files -->
    <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
    <link href="assets/vendor/quill/quill.snow.css" rel="stylesheet">
    <link href="assets/vendor/quill/quill.bubble.css" rel="stylesheet">
    <link href="assets/vendor/remixicon/remixicon.css" rel="stylesheet">
    <link href="assets/vendor/simple-datatables/style.css" rel="stylesheet">

    <!-- Template Main CSS File -->
    <link href="assets/css/style.css" rel="stylesheet">

    <!-- =======================================================
  * Template Name: NiceAdmin
  * Template URL: https://bootstrapmade.com/nice-admin-bootstrap-admin-html-template/
  * Updated: Apr 20 2024 with Bootstrap v5.3.3
  * Author: BootstrapMade.com
  * License: https://bootstrapmade.com/license/
  ======================================================== -->
</head>

<body>

    <!-- ======= Header ======= -->
    <header id="header" class="header fixed-top d-flex align-items-center">

        <div class="d-flex align-items-center justify-content-between">
            <a href="/" class="logo d-flex align-items-center">
                <img src="img/AURORALOGOSFUNDO.ico" alt="">
                <span class="d-none d-lg-block">Aurora Boreal</span>
            </a>
            <i class="bi bi-list toggle-sidebar-btn"></i>
        </div><!-- End Logo -->




        </ul>
        </nav><!-- End Icons Navigation -->

    </header><!-- End Header -->

    <main id="main" class="main">

        <div class="pagetitle">
            <h1>Painéis e gerenciamentos</h1>
            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Pág.inicial</a></li>
                    <li class="breadcrumb-item active">Painéis</li>
                </ol>
            </nav>
        </div><!-- End Page Title -->

        <section class="section dashboard">
            <div class="row">

                <!-- Left side columns -->
                <div class="col-lg-8">
                    <div class="row">






                               <!-- Perfil Card com Link -->
<div class="col-xxl-4 col-md-6">
  <a href="perfil.html" style="text-decoration: none; cursor: pointer;">
    <div class="card info-card revenue-card">
      <div class="card-body">
        <h5 class="card-title">Perfil <span></span></h5>

        <div class="d-flex align-items-center">
          <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
            <i class="bi bi-person-circle"></i>
          </div>
          <div class="ps-3">
            <h6>nome</h6>
          </div>
        </div>
      </div>
    </div>
  </a>
</div>

<!-- End Perfil Card -->






                    </div>
                </div><!-- End Left side columns -->

                <!-- Right side columns -->
                <div class="col-lg-4">
                    <!-- Multi Columns Form -->
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Registro de Uso De Material</h5>

                            <form id="form-material" class="row g-3">
                                <div class="col-md-12">
                                    <label for="material-select" class="form-label">Material:</label>
                                    <select class="form-select" id="material-select" name="produto_id" required>
                                        <option value="" selected disabled>Selecione um material</option>
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label for="quantidade" class="form-label">Quantidade: </label>
                                    <input type="number" class="form-control" id="quantidade" name="quantidade_usada" min="1" required>
                                </div>

                                <div class="col-md-12">
                                    <label for="observacoes" class="form-label">Observações (opcional):</label>
                                    <textarea class="form-control" id="observacoes" name="observacoes" rows="2"></textarea>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary">Registrar Uso</button>
                                    <button type="reset" class="btn btn-secondary">Limpar</button>
                                </div>
                            </form>
                    </div>
                </div><!-- End Multi Columns Form -->

                <!-- Histórico de Uso de Materiais -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h5 class="card-title">Histórico de Uso de Materiais</h5>
                        <div class="table-responsive">
                            <table class="table table-striped" id="tabela-historico-materiais">
                                <thead>
                                    <tr>
                                        <th>Material</th>
                                        <th>Quantidade</th>
                                        <th>Data/Hora</th>
                                        <th>Observações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Dados serão carregados via JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div><!-- End Histórico de Materiais -->

                <!-- Table with stripped rows -->



                  <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <label for="turma-select" class="form-label">Adicionar Nota bimestral</label>

                            <select class="form-select" id="turma-select" name="turma">
                                <option value="" selected disabled>Selecione a turma</option>
                                <option value="1A">1° A- Química</option>
                                <option value="1B">1° B-Física</option>
                                <option value="1C">1° B- Química</option>
                            </select>
                        </h5>

                        <h5 class="card-title mt-4">Bimestre</h5>
                        <select class="form-select" id="turma-select" name="turma">
                            <option value="" selected disabled>Selecione a turma</option>
                            <option value="1B">1° Bimestre</option>
                            <option value="2B">2° Bimestre</option>
                            <option value="3B">3° Bimestre</option>
                            <option value="4B">4° Bimestre</option>
                        </select>

                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Nome</th>
                                    <th scope="col">Nota</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% if (typeof rows != 'undefined') { %>
                                    <% for (let index = 0; index < rows.length; index++) { %>
                                        <% if (rows[index].per_id == 1) { %>
                                            <tr>
                                                <th scope="row">#<%= rows[index].usu_id %></th>
                                                <td><%= rows[index].usu_nome %></td>
                                                <td><input type="number" class="form-control" id="nota" name="nota"></td>
                                                <td><a type="button" id="enviarNota" class="btn btn-primary">Enviar nota</a></td>
                                            </tr>
                                        <% } %>
                                    <% } %>
                                <% } %>

                            </tbody>

                        </table>

                    </div>
                </div>
                <!--fim do formulário-->

                <!--tabela nova atividades-->
                <div class="card">
                    <div class="card-body">
                      <h5 class="card-title">Nova Atividade</h5>
                      <form id="form-atividade" method="POST" action="/system/professores/atividades">
                        <div class="mb-3">
                          <label for="titulo" class="form-label">Título da Atividade</label>
                          <input type="text" class="form-control" name="titulo" required>
                        </div>
                        <div class="mb-3">
                          <label for="descricao" class="form-label">Descrição</label>
                          <textarea class="form-control" name="descricao" rows="4" required></textarea>
                        </div>
                        <div class="mb-3">
                          <label for="data_entrega" class="form-label">Data de Entrega</label>
                          <input type="datetime-local" class="form-control" name="data_entrega" required>
                        </div>
                    <!--    <div class="mb-3">
                          <label for="anexo_atividade" class="form-label">Anexo (opcional)</label>
                          <input type="file" class="form-control" name="anexo_atividade">
                        </div> -->

                        <button class="btn btn-primary">Cadastrar Atividade</button>
                      </form>
                    </div>
                  </div>
                  <!--fim de novas atividades-->
                  <!-- Listagem de Atividades Cadastradas -->
<div class="card mt-4">
    <div class="card-body">
      <h5 class="card-title">Atividades Cadastradas</h5>
      <% if (typeof atividades_cadastradas  != 'undefined') { %>
      <% if (atividades_cadastradas && atividades_cadastradas.length > 0) { %>
        <table class="table table-bordered">
          <thead>
            <tr>
              <th>Título</th>
              <th>Descrição</th>
              <th>Data de Entrega</th>
              <th>Ações</th>
            </tr>
          </thead>
          <tbody>
            <% atividades_cadastradas.forEach(atividade => { %>
              <tr>
                <td><%= atividade.titulo %></td>
                <td><%= atividade.descricao %></td>
                <td><%= new Date(atividade.data_entrega).toLocaleString() %></td>
                <td>
                  <a href="/system/professores/editarAtividade?id=<%= atividade.ati_id %>" class="btn btn-warning btn-sm">Editar</a>
                  <form action="/system/professores/deletarAtividade?id=<%= atividade.ati_id %>" method="POST" style="display:inline;">
                    <button type="submit" class="btn btn-danger btn-sm" onsubmit="return confirm('Tem certeza que deseja excluir esta atividade?');">Excluir</button>
                  </form>
                </td>
              </tr>
            <% }) %>
          </tbody>
        </table>
        <% } %>
      <% } else { %>
        <p>Nenhuma atividade cadastrada ainda.</p>
      <% } %>

    </div>
  </div>
  <!--fim de listar atividades-->
                  <!--visualizar respostas-->
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Respostas dos Alunos</h5>

                        <% if (typeof atividades_row !='undefined' ) { %>

                            <% atividades_row.forEach(atividade=> { %>
                                <div class="mb-3"></div>
                                <h6>
                                    <span class="fw-bold">Atividade:</span> <%= atividade.titulo %> - <%= atividade.data_entrega.toLocaleString() %>
                                </h6>
                                <ul class="list-group mb-3">
                                    <% atividade.respostas.forEach(res=> { %>
                                        <% if (!res) { %>
                                            <div class="mb-2 alert-black">Nenhuma resposta enviada ainda</div>
                                        <% } %>
                                        <li class="list-group-item">
                                            <p><strong>Aluno:</strong>
                                                <%= res.aluno %> - <%= res.data_envio.toLocaleString() %>
                                            </p>
                                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#exampleModal<%= res.res_id %>">Resposta</button>
                                            <div class="modal fade" id="exampleModal<%= res.res_id %>" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
                                                <div class="modal-dialog">
                                                  <div class="modal-content">
                                                    <div class="modal-header">
                                                      <h5 class="modal-title" id="exampleModalLabel">Modal title</h5>
                                                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p><strong>Resposta:</strong>
                                                            <%= res.resposta %>
                                                        </p>
                                                        <% if (typeof res.anexo_resposta != 'undefined') { %>
                                                            <p><a href="<%= res.anexo_resposta %>" target="_blank" download>Ver anexo</a></p>
                                                        <% } %>
                                                        <form method="POST" action="/system/professores/corrigirResposta">
                                                            <input type="hidden" name="res_id" value="<%= res.res_id %>">
                                                            <div class="mb-2">
                                                                <label>Nota:</label>
                                                                <input type="number" name="nota" class="form-control" step="0.1" min="0"
                                                                    max="10" value="<%= res.nota || '' %>">
                                                            </div>
                                                            <div class="mb-2">
                                                                <label>Comentário:</label>
                                                                <textarea name="comentario_professor"
                                                                    class="form-control"><%= res.comentario_professor || '' %></textarea>
                                                            </div>
                                                            <button type="submit" class="btn btn-success">Salvar Correção</button>
                                                        </form>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            <% if (res.anexo_resposta) { %>

                                                <p><a href="<%= res.anexo_resposta %>" target="_blank">Ver anexo</a></p>
                                                <% } %>

                                        </li>
                                        <% }) %>
                                </ul>
                                <% }) %>
                                <% } %>

                    </div>
                </div>
                <!--fim de visualizar respostas-->
            </div><!-- End Right side columns -->
            </div>
        </section>

    </main><!-- End #main -->

    <!-- ======= Footer ======= -->
    <footer id="footer" class="footer">
        <div class="copyright">
            &copy; Copyright <strong><span>NiceAdmin</span></strong>. All Rights Reserved
            &copy; Copyright <strong><span>Devminds</span></strong>. All Rights Reserved
            &copy; Copyright <strong><span>Aurora Boreal</span></strong>. All Rights Reserved
        </div>

    </footer><!-- End Footer -->

    <a href="#" class="back-to-top d-flex align-items-center justify-content-center"><i
            class="bi bi-arrow-up-short"></i></a>

    <!-- Vendor JS Files -->
    <script src="assets/vendor/apexcharts/apexcharts.min.js"></script>
    <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="assets/vendor/chart.js/chart.umd.js"></script>
    <script src="assets/vendor/echarts/echarts.min.js"></script>
    <script src="assets/vendor/quill/quill.js"></script>
    <script src="assets/vendor/simple-datatables/simple-datatables.js"></script>
    <script src="assets/vendor/tinymce/tinymce.min.js"></script>
    <script src="assets/vendor/php-email-form/validate.js"></script>

    <!-- Template Main JS File -->
    <script src="assets/js/main.js"></script>

    <!-- Script para Gerenciamento de Materiais -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            carregarMateriaisDisponiveis();
            carregarHistoricoMateriais();

            // Event listener para o formulário
            document.getElementById('form-material').addEventListener('submit', function(e) {
                e.preventDefault();
                registrarUsoMaterial();
            });
        });

        function carregarMateriaisDisponiveis() {
            fetch('/system/professores/fetchMateriaisDisponiveis', {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                const select = document.getElementById('material-select');
                select.innerHTML = '<option value="" selected disabled>Selecione um material</option>';

                data.forEach(material => {
                    const option = document.createElement('option');
                    option.value = material.id;
                    option.textContent = `${material.nome_produto} (Estoque: ${material.quantidade})`;
                    option.setAttribute('data-estoque', material.quantidade);
                    select.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Erro ao carregar materiais:', error);
                alert('Erro ao carregar materiais disponíveis');
            });
        }

        function registrarUsoMaterial() {
            const formData = new FormData(document.getElementById('form-material'));
            const data = Object.fromEntries(formData);

            // Validar estoque
            const select = document.getElementById('material-select');
            const selectedOption = select.options[select.selectedIndex];
            const estoqueDisponivel = parseInt(selectedOption.getAttribute('data-estoque'));
            const quantidadeSolicitada = parseInt(data.quantidade_usada);

            if (quantidadeSolicitada > estoqueDisponivel) {
                alert(`Quantidade solicitada (${quantidadeSolicitada}) é maior que o estoque disponível (${estoqueDisponivel})`);
                return;
            }

            fetch('/system/professores/registrarUsoMaterial', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.sucesso) {
                    alert(result.mensagem);
                    document.getElementById('form-material').reset();
                    carregarMateriaisDisponiveis(); // Recarregar para atualizar estoque
                    carregarHistoricoMateriais(); // Recarregar histórico
                } else {
                    alert(result.mensagem);
                }
            })
            .catch(error => {
                console.error('Erro ao registrar uso:', error);
                alert('Erro ao registrar uso do material');
            });
        }

        function carregarHistoricoMateriais() {
            fetch('/system/professores/fetchHistoricoUsoMateriais', {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                const tbody = document.querySelector('#tabela-historico-materiais tbody');
                tbody.innerHTML = '';

                if (data.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="4" class="text-center">Nenhum registro encontrado</td></tr>';
                    return;
                }

                data.forEach(registro => {
                    const tr = document.createElement('tr');
                    const dataFormatada = new Date(registro.data_uso).toLocaleString('pt-BR');

                    tr.innerHTML = `
                        <td>${registro.nome_produto}</td>
                        <td>${registro.quantidade_usada}</td>
                        <td>${dataFormatada}</td>
                        <td>${registro.observacoes || '-'}</td>
                    `;
                    tbody.appendChild(tr);
                });
            })
            .catch(error => {
                console.error('Erro ao carregar histórico:', error);
            });
        }
    </script>

</body>

</html>