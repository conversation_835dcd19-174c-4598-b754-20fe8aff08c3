{"version": 3, "file": "quill.snow.css", "mappings": ";;;;;;;AAWA,cACE,qBAAY,CACZ,sCAAsB,CACtB,cAAW,CACX,WAAQ,CACR,QAAQ,CACR,iBAAU,CAGV,sCACE,iBAAY,CAKZ,gIACE,cAAQ,CAEd,cACE,cAAM,CACN,UAAQ,CACR,iBAAY,CACZ,iBAAU,CACV,OAAK,CACL,gBACE,QAAQ,CACR,SAAS,CAEb,WACE,qBAAY,CACZ,mFAAe,CACf,gBAAa,CACb,WAAQ,CACR,YAAS,CACT,eAAY,CACZ,iBAAS,CACT,UAAU,CACV,eAAe,CACf,eAAY,CACZ,oBAAa,CACb,oBAAW,CACX,eACE,WAAQ,CAEV,oJACE,QAAQ,CACR,SAAS,CAEmB,8HAC1B,iFAAa,EACiB,kIAC9B,mFAAe,EACnB,iBACE,wBAAiB,CACnB,cACE,qBAAQ,CACR,eAAS,CACX,cACE,kBAAc,CAChB,cACE,oBAAiB,CACjB,kBAAc,CACd,iBAAU,CAEV,8BACE,oBAAS,CACT,kBAAgB,CAChB,iBAAc,CACd,gBAAY,CACZ,kBAAa,CACb,WAAO,CAIT,sFACE,UAAO,CAEX,gDACE,eAAS,CACX,iDACE,eAAS,CACX,mDACE,eAAS,CAGmB,sDAC1B,0EAAa,EACiB,0DAC9B,4EAAe,EAEnB,iCACE,wBAAmB,CACnB,iDACE,qCAAS,CAEX,6CACE,wBAAmB,CACnB,6DACE,yCAAS,CAGmB,kEAC1B,mEAAa,EACiB,sEAC9B,qEAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,yCAAS,CAGmB,kEAC1B,4DAAa,EACiB,sEAC9B,8DAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,qCAAS,CAGmB,kEAC1B,qDAAa,EACiB,sEAC9B,uDAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,yCAAS,CAGmB,kEAC1B,8CAAa,EACiB,sEAC9B,gDAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,yCAAS,CAGmB,kEAC1B,uCAAa,EACiB,sEAC9B,yCAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,qCAAS,CAGmB,kEAC1B,gCAAa,EACiB,sEAC9B,kCAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,yCAAS,CAGmB,kEAC1B,yBAAa,EACiB,sEAC9B,2BAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,yCAAS,CAGmB,kEAC1B,kBAAa,EACiB,sEAC9B,oBAAe,EATrB,6CACE,wBAAmB,CACnB,6DACE,qCAAS,CASb,+CACE,gBAAiB,CACnB,iDACE,kBAAuB,CACzB,wDACE,iBAAkB,CACpB,0DACE,mBAAwB,CAP1B,+CACE,gBAAiB,CACnB,iDACE,kBAAuB,CACzB,wDACE,iBAAkB,CACpB,0DACE,mBAAwB,CAP1B,+CACE,gBAAiB,CACnB,iDACE,mBAAuB,CACzB,wDACE,iBAAkB,CACpB,0DACE,oBAAwB,CAP1B,+CACE,iBAAiB,CACnB,iDACE,mBAAuB,CACzB,wDACE,kBAAkB,CACpB,0DACE,oBAAwB,CAP1B,+CACE,iBAAiB,CACnB,iDACE,mBAAuB,CACzB,wDACE,kBAAkB,CACpB,0DACE,oBAAwB,CAP1B,+CACE,iBAAiB,CACnB,iDACE,mBAAuB,CACzB,wDACE,kBAAkB,CACpB,0DACE,oBAAwB,CAP1B,+CACE,iBAAiB,CACnB,iDACE,mBAAuB,CACzB,wDACE,kBAAkB,CACpB,0DACE,oBAAwB,CAP1B,+CACE,iBAAiB,CACnB,iDACE,mBAAuB,CACzB,wDACE,kBAAkB,CACpB,0DACE,oBAAwB,CAP1B,+CACE,iBAAiB,CACnB,iDACE,mBAAuB,CACzB,wDACE,kBAAkB,CACpB,0DACE,oBAAwB,CAE5B,+BACE,mBAAe,CACf,+CACE,gBAAa,CACb,mBAAiB,CACjB,eAAY,CAEhB,iBACE,kBAAc,CACd,UAAO,CACP,oBACE,YAAS,CAEb,oCACE,qBAAa,CAEf,qBACE,aAAS,CACT,cAAW,CACb,qCACE,aAAQ,CACV,oCACE,iBAAQ,CAEV,wBACE,qBAAkB,CACpB,sBACE,wBAAkB,CACpB,yBACE,qBAAkB,CACpB,yBACE,qBAAkB,CACpB,wBACE,wBAAkB,CACpB,uBACE,qBAAkB,CACpB,yBACE,qBAAkB,CAEpB,2BACE,UAAO,CACT,yBACE,aAAO,CACT,4BACE,UAAO,CACT,4BACE,UAAO,CACT,2BACE,aAAO,CACT,0BACE,UAAO,CACT,4BACE,UAAO,CAET,0BACE,yCAAoB,CACtB,8BACE,wCAAmB,CAErB,0BACE,eAAW,CACb,0BACE,eAAW,CACb,yBACE,eAAW,CAEb,6BACE,aAAW,CACX,kBAAY,CAEd,4BACE,iBAAY,CACd,6BACE,kBAAY,CACd,2BACE,gBAAY,CAEd,kBACE,iBAAU,CAEd,4BACE,qBAAO,CACP,8BAAS,CACT,iBAAY,CACZ,SAAM,CACN,mBAAgB,CAChB,iBAAU,CACV,UAAO,CC1MP,qDACE,UAAO,CACP,UAAS,CACT,aAAS,CAEX,uDACE,eAAY,CACZ,WAAQ,CACR,cAAQ,CACR,oBAAS,CACT,UAAO,CACP,WAAQ,CACR,eAAS,CACT,UAAgE,CAEhE,+DACE,UAAO,CACP,WAAQ,CAEV,iFACE,YAAS,CAEb,6FACE,YAAS,CAEX,6jBAGE,UAAO,CACP,kgDACE,SAAM,CACR,kgDACE,WAAQ,CAGS,wBAEnB,mGACE,UAAO,CACP,8PACE,SAAM,CACR,8PACE,WAAQ,EAEhB,SACE,qBAAY,CACZ,WACE,qBAAY,CAEd,oBACE,YAAS,CACX,6CACE,iBAAY,CAEd,qBACE,iBAAU,CACV,0BAAW,CACX,uBACE,cAAQ,CACR,oBAAiB,CACrB,6BACE,2BAAW,CAEb,qBAKE,oBAAS,CACT,qBAAgB,CALhB,2BACE,UAAO,CACP,UAAS,CACT,aAAS,CAIb,oBACE,SAAM,CACN,WAAQ,CACR,oBAAgB,CAChB,qBAAiB,CACjB,cAAc,CAChB,0BACE,SAAM,CACN,WAAQ,CACR,oBAAmB,CACnB,cAAc,CAEhB,8CACE,SAAM,CAER,mBACE,SAAM,CACR,kBACE,iBAAW,CACb,8CACE,cAAc,CAChB,yBACE,UAAS,CAGT,sCACE,YAAS,CAEX,gDACE,cAAS,CACX,iDACE,YAAS,CAGX,uBACE,aAAW,CACb,uBACE,eAAW,CACb,uBACE,gBAAW,CACb,uBACE,aAAW,CACb,uBACE,eAAW,CACb,uBACE,eAAW,CACb,sBACE,yBAAiB,CACnB,+BACE,0BAAa,CACb,iBAAe,CACf,cAAY,CACZ,iBAAc,CAChB,sEACE,wBAAkB,CAClB,iBAAe,CACjB,6CACE,iBAAe,CACf,cAAY,CACZ,gBAAS,CACX,yBACE,aAAW,CACX,eAAS,CACX,6CACE,wBAAkB,CAClB,aAAO,CACP,gBAAU,CACZ,wBACE,cAAW,CAEf,oBACE,UAAO,CACP,oBAAS,CACT,UAAO,CACP,cAAW,CACX,eAAa,CACb,WAAQ,CACR,iBAAU,CACV,qBAAgB,CAClB,0BACE,cAAQ,CACR,oBAAS,CACT,WAAQ,CACR,gBAAc,CACd,iBAAe,CACf,iBAAU,CACV,UAAO,CACP,kCACE,oBAAS,CACT,gBAAa,CACjB,4BACE,qBAAkB,CAClB,YAAS,CACT,cAAW,CACX,eAAS,CACT,iBAAU,CACV,kBAAa,CACb,4CACE,cAAQ,CACR,aAAS,CACT,kBAAgB,CAChB,eAAa,CAEf,iDACE,UAAO,CACP,SAAS,CACT,0DACE,SAAM,CACR,4DACE,WAAQ,CACZ,mDACE,aAAS,CACT,eAAY,CACZ,QAAK,CACL,SAAS,CAEb,mDACE,UAAuB,CACvB,qFACE,eAAS,CACT,6FACE,SAAO,CAEX,4CACE,aAAS,CACX,yCACE,WAAQ,CACR,UAAO,CACP,eAAS,CAEX,6CACE,eAAS,CACT,WAAsF,CACxF,0CACE,4BAAQ,CACR,UAAO,CACP,WAAQ,CACR,UAAQ,CACR,SAAS,CACT,UAAO,CAGT,mEACE,iBAAU,CACV,eAAY,CACZ,OAAO,CACP,OAAK,CACL,UAAO,CAKP,qgBACE,wBAAS,CAEf,8BACE,UAAO,CACP,6GAEE,gBAAS,CAET,6IAEE,mBAAsB,CAFxB,6IAEE,mBAAsB,CAFxB,6IAEE,mBAAsB,CAFxB,6IAEE,mBAAsB,CAFxB,6IAEE,mBAAsB,CAFxB,6IAEE,mBAAsB,CAC1B,sEACE,aAAW,CACb,sEACE,eAAW,CACb,sEACE,gBAAW,CACb,sEACE,aAAW,CACb,sEACE,eAAW,CACb,sEACE,eAAW,CAEf,4BACE,WAAO,CACP,yGAEE,oBAAS,CACX,6IAEE,eAAS,CACX,qJAEE,mBAAS,CACX,sEACE,yCAAoB,CACtB,0EACE,wCAAmB,CAEvB,4BACE,UAAO,CACP,yGAEE,gBAAS,CACX,6IAEE,eAAS,CACX,6IAEE,eAAS,CACX,2IAEE,cAAS,CACX,sEACE,cAAW,CACb,sEACE,cAAW,CACb,qEACE,cAAW,CAGb,wDACE,qBAAkB,CAEpB,mDACE,qBAAkB,CAExB,yBACE,iBAAU,CACV,gCACE,SAAO,CACP,OAAK,CCpTT,oBACE,qBAAQ,CACR,qBAAY,CACZ,2DAA6B,CAC7B,WAAS,CAET,gCACE,iBAAc,CAEhB,qCACE,4BAAQ,CACV,uCACE,4BAAQ,CACR,oCAAY,CAEZ,4DACE,iBAAc,CAChB,8DACE,iBAAc,CAGhB,4HACE,iBAAc,CAEpB,4CACE,YAAY,CCtBZ,qBACE,qBAAkB,CAClB,qBAAQ,CACR,uBAAY,CACZ,UAAO,CACP,gBAAS,CACT,kBAAa,CACb,6BACE,oBAAS,CACT,gBAAa,CACb,gBAAc,CAChB,sCACE,YAAS,CACT,qBAAQ,CACR,cAAW,CACX,WAAQ,CACR,QAAQ,CACR,eAAS,CACT,WAAO,CACT,kCACE,oBAAS,CACT,eAAW,CACX,iBAAY,CACZ,sBAAe,CACf,kBAAgB,CAClB,wCACE,2BAAc,CACd,cAAS,CACT,gBAA2B,CAC3B,iBAAe,CACjB,yCACE,gBAAS,CACT,eAAa,CACf,uBACE,gBAAa,CAEf,yFACE,YAAS,CACX,iDACE,oBAAS,CACX,mDACE,cAAc,CACd,cAAS,CACT,eAAe,CACnB,6CACE,qBAAS,CACX,gDACE,wBAAS,CACX,8CACE,sBAAS,CCvCX,WACE,UAAO,CAEX,sBACE,qBAAQ", "sources": ["webpack://Quill/./src/assets/core.styl", "webpack://Quill/./src/assets/base.styl", "webpack://Quill/./src/assets/snow/toolbar.styl", "webpack://Quill/./src/assets/snow/tooltip.styl", "webpack://Quill/./src/assets/snow.styl"], "sourcesContent": ["// Styles necessary for Quill\n\nLIST_STYLE = decimal lower-alpha lower-roman\nLIST_STYLE_WIDTH = 1.2em\nLIST_STYLE_MARGIN = 0.3em\nLIST_STYLE_OUTER_WIDTH = LIST_STYLE_MARGIN + LIST_STYLE_WIDTH\nMAX_INDENT = 9\n\nresets(arr)\n  unquote('list-' + join(' list-', arr))\n\n.ql-container\n  box-sizing: border-box\n  font-family: Helvetica, Arial, sans-serif\n  font-size: 13px\n  height: 100%\n  margin: 0px\n  position: relative\n\n.ql-container.ql-disabled\n  .ql-tooltip\n    visibility: hidden\n\n.ql-container:not(.ql-disabled)\n  li[data-list=checked],\n  li[data-list=unchecked]\n    > .ql-ui\n      cursor: pointer\n\n.ql-clipboard\n  left: -100000px\n  height: 1px\n  overflow-y: hidden\n  position: absolute\n  top: 50%\n  p\n    margin: 0\n    padding: 0\n\n.ql-editor\n  box-sizing: border-box\n  counter-reset: resets(0..MAX_INDENT)\n  line-height: 1.42\n  height: 100%\n  outline: none\n  overflow-y: auto\n  padding: 12px 15px\n  tab-size: 4\n  -moz-tab-size: 4\n  text-align: left\n  white-space: pre-wrap\n  word-wrap: break-word\n  > *\n    cursor: text\n\n  p, ol, pre, blockquote, h1, h2, h3, h4, h5, h6\n    margin: 0\n    padding: 0\n  p, h1, h2, h3, h4, h5, h6\n    @supports (counter-set: none)\n      counter-set: resets(0..MAX_INDENT)\n    @supports not (counter-set: none)\n      counter-reset: resets(0..MAX_INDENT)\n  table\n    border-collapse: collapse\n  td\n    border: 1px solid #000\n    padding: 2px 5px\n  ol\n    padding-left: 1.5em\n  li\n    list-style-type: none\n    padding-left: LIST_STYLE_OUTER_WIDTH\n    position: relative\n\n    > .ql-ui:before\n      display: inline-block\n      margin-left: -1*LIST_STYLE_OUTER_WIDTH\n      margin-right: LIST_STYLE_MARGIN\n      text-align: right\n      white-space: nowrap\n      width: LIST_STYLE_WIDTH\n\n  li[data-list=checked],\n  li[data-list=unchecked]\n    > .ql-ui\n      color: #777\n\n  li[data-list=bullet] > .ql-ui:before\n    content: '\\2022'\n  li[data-list=checked] > .ql-ui:before\n    content: '\\2611'\n  li[data-list=unchecked] > .ql-ui:before\n    content: '\\2610'\n\n  li[data-list]\n    @supports (counter-set: none)\n      counter-set: resets(1..MAX_INDENT)\n    @supports not (counter-set: none)\n      counter-reset: resets(1..MAX_INDENT)\n\n  li[data-list=ordered]\n    counter-increment: list-0\n    > .ql-ui:before\n      content: unquote('counter(list-0, ' + LIST_STYLE[0] + ')') '. '\n  for num in (1..MAX_INDENT)\n    li[data-list=ordered].ql-indent-{num}\n      counter-increment: unquote('list-' + num)\n      > .ql-ui:before\n        content: unquote('counter(list-' + num + ', ' + LIST_STYLE[num%3] + ')') '. '\n    if (num < MAX_INDENT)\n      li[data-list].ql-indent-{num}\n        @supports (counter-set: none)\n          counter-set: resets((num+1)..MAX_INDENT)\n        @supports not (counter-set: none)\n          counter-reset: resets((num+1)..MAX_INDENT)\n\n  for num in (1..MAX_INDENT)\n    .ql-indent-{num}:not(.ql-direction-rtl)\n      padding-left: (3*num)em\n    li.ql-indent-{num}:not(.ql-direction-rtl)\n      padding-left: (3*num + LIST_STYLE_OUTER_WIDTH)em\n    .ql-indent-{num}.ql-direction-rtl.ql-align-right\n      padding-right: (3*num)em\n    li.ql-indent-{num}.ql-direction-rtl.ql-align-right\n      padding-right: (3*num + LIST_STYLE_OUTER_WIDTH)em\n\n  li.ql-direction-rtl\n    padding-right: LIST_STYLE_OUTER_WIDTH\n    > .ql-ui:before\n      margin-left: LIST_STYLE_MARGIN\n      margin-right: -1*LIST_STYLE_OUTER_WIDTH\n      text-align: left\n\n  table\n    table-layout: fixed\n    width: 100%\n    td\n      outline: none\n\n  .ql-code-block-container\n    font-family: monospace\n\n  .ql-video\n    display: block\n    max-width: 100%\n  .ql-video.ql-align-center\n    margin: 0 auto\n  .ql-video.ql-align-right\n    margin: 0 0 0 auto\n\n  .ql-bg-black\n    background-color: rgb(0,0,0)\n  .ql-bg-red\n    background-color: rgb(230,0,0)\n  .ql-bg-orange\n    background-color: rgb(255,153,0)\n  .ql-bg-yellow\n    background-color: rgb(255,255,0)\n  .ql-bg-green\n    background-color: rgb(0,138,0)\n  .ql-bg-blue\n    background-color: rgb(0,102,204)\n  .ql-bg-purple\n    background-color: rgb(153,51,255)\n\n  .ql-color-white\n    color: rgb(255,255,255)\n  .ql-color-red\n    color: rgb(230,0,0)\n  .ql-color-orange\n    color: rgb(255,153,0)\n  .ql-color-yellow\n    color: rgb(255,255,0)\n  .ql-color-green\n    color: rgb(0,138,0)\n  .ql-color-blue\n    color: rgb(0,102,204)\n  .ql-color-purple\n    color: rgb(153,51,255)\n\n  .ql-font-serif\n    font-family: Georgia, Times New Roman, serif\n  .ql-font-monospace\n    font-family: Monaco, Courier New, monospace\n\n  .ql-size-small\n    font-size: 0.75em\n  .ql-size-large\n    font-size: 1.5em\n  .ql-size-huge\n    font-size: 2.5em\n\n  .ql-direction-rtl\n    direction: rtl\n    text-align: inherit\n\n  .ql-align-center\n    text-align: center\n  .ql-align-justify\n    text-align: justify\n  .ql-align-right\n    text-align: right\n\n  .ql-ui\n    position: absolute\n\n.ql-editor.ql-blank::before\n  color: rgba(0,0,0,0.6)\n  content: attr(data-placeholder)\n  font-style: italic\n  left: 15px\n  pointer-events: none\n  position: absolute\n  right: 15px\n", "// Styles shared between snow and bubble\n\ncontrolHeight = 24px\ninputPaddingWidth = 5px\ninputPaddingHeight = 3px\n\ncolorItemMargin = 2px\ncolorItemSize = 16px\ncolorItemsPerRow = 7\n\n\n.ql-{themeName}.ql-toolbar, .ql-{themeName} .ql-toolbar\n  &:after\n    clear: both\n    content: ''\n    display: table\n\n  button\n    background: none\n    border: none\n    cursor: pointer\n    display: inline-block\n    float: left\n    height: controlHeight\n    padding: inputPaddingHeight inputPaddingWidth\n    width: controlHeight + (inputPaddingWidth - inputPaddingHeight)*2\n\n    svg\n      float: left\n      height: 100%\n\n    &:active:hover\n      outline: none\n\n  input.ql-image[type=file]\n    display: none\n\n  button:hover, button:focus, button.ql-active,\n  .ql-picker-label:hover, .ql-picker-label.ql-active,\n  .ql-picker-item:hover, .ql-picker-item.ql-selected\n    color: activeColor\n    .ql-fill, .ql-stroke.ql-fill\n      fill: activeColor\n    .ql-stroke, .ql-stroke-miter\n      stroke: activeColor\n\n// Fix for iOS not losing hover on touch\n@media (pointer: coarse)\n  .ql-{themeName}.ql-toolbar, .ql-{themeName} .ql-toolbar\n    button:hover:not(.ql-active)\n      color: inactiveColor\n      .ql-fill, .ql-stroke.ql-fill\n        fill: inactiveColor\n      .ql-stroke, .ql-stroke-miter\n        stroke: inactiveColor\n\n.ql-{themeName}\n  box-sizing: border-box\n  *\n    box-sizing: border-box\n\n  .ql-hidden\n    display: none\n  .ql-out-bottom, .ql-out-top\n    visibility: hidden\n\n  .ql-tooltip\n    position: absolute\n    transform: translateY(10px)\n    a\n      cursor: pointer\n      text-decoration: none\n  .ql-tooltip.ql-flip\n    transform: translateY(-10px)\n\n  .ql-formats\n    &:after\n      clear: both\n      content: ''\n      display: table\n    display: inline-block\n    vertical-align: middle\n\n  .ql-stroke\n    fill: none\n    stroke: inactiveColor\n    stroke-linecap: round\n    stroke-linejoin: round\n    stroke-width: 2\n  .ql-stroke-miter\n    fill: none\n    stroke: inactiveColor\n    stroke-miterlimit: 10\n    stroke-width: 2\n\n  .ql-fill, .ql-stroke.ql-fill\n    fill: inactiveColor\n\n  .ql-empty\n    fill: none\n  .ql-even\n    fill-rule: evenodd\n  .ql-thin, .ql-stroke.ql-thin\n    stroke-width: 1\n  .ql-transparent\n    opacity: 0.4\n\n  .ql-direction\n    svg:last-child\n      display: none\n  .ql-direction.ql-active\n    svg:last-child\n      display: inline\n    svg:first-child\n      display: none\n\n  .ql-editor\n    h1\n      font-size: 2em\n    h2\n      font-size: 1.5em\n    h3\n      font-size: 1.17em\n    h4\n      font-size: 1em\n    h5\n      font-size: 0.83em\n    h6\n      font-size: 0.67em\n    a\n      text-decoration: underline\n    blockquote\n      border-left: 4px solid #ccc\n      margin-bottom: 5px\n      margin-top: 5px\n      padding-left: 16px\n    code, .ql-code-block-container\n      background-color: #f0f0f0\n      border-radius: 3px\n    .ql-code-block-container\n      margin-bottom: 5px\n      margin-top: 5px\n      padding: 5px 10px\n    code\n      font-size: 85%\n      padding: 2px 4px\n    .ql-code-block-container\n      background-color: #23241f\n      color: #f8f8f2\n      overflow: visible\n    img\n      max-width: 100%\n\n  .ql-picker\n    color: inactiveColor\n    display: inline-block\n    float: left\n    font-size: 14px\n    font-weight: 500\n    height: controlHeight\n    position: relative\n    vertical-align: middle\n  .ql-picker-label\n    cursor: pointer\n    display: inline-block\n    height: 100%\n    padding-left: 8px\n    padding-right: 2px\n    position: relative\n    width: 100%\n    &::before\n      display: inline-block\n      line-height: 22px\n  .ql-picker-options\n    background-color: backgroundColor\n    display: none\n    min-width: 100%\n    padding: 4px 8px\n    position: absolute\n    white-space: nowrap\n    .ql-picker-item\n      cursor: pointer\n      display: block\n      padding-bottom: 5px\n      padding-top: 5px\n  .ql-picker.ql-expanded\n    .ql-picker-label\n      color: borderColor\n      z-index: 2\n      .ql-fill\n        fill: borderColor\n      .ql-stroke\n        stroke: borderColor\n    .ql-picker-options\n      display: block\n      margin-top: -1px\n      top: 100%\n      z-index: 1\n\n  .ql-color-picker, .ql-icon-picker\n    width: controlHeight + 4\n    .ql-picker-label\n      padding: 2px 4px\n      svg\n        right: 4px\n  .ql-icon-picker\n    .ql-picker-options\n      padding: 4px 0px\n    .ql-picker-item\n      height: controlHeight\n      width: controlHeight\n      padding: 2px 4px\n  .ql-color-picker\n    .ql-picker-options\n      padding: inputPaddingHeight inputPaddingWidth\n      width: (colorItemSize + 2*colorItemMargin) * colorItemsPerRow + 2*inputPaddingWidth + 2  // +2 for the border\n    .ql-picker-item\n      border: 1px solid transparent\n      float: left\n      height: colorItemSize\n      margin: colorItemMargin\n      padding: 0px\n      width: colorItemSize\n\n  .ql-picker:not(.ql-color-picker):not(.ql-icon-picker)\n    svg\n      position: absolute\n      margin-top: -9px\n      right: 0\n      top: 50%\n      width: 18px\n\n  .ql-picker.ql-header, .ql-picker.ql-font, .ql-picker.ql-size\n    .ql-picker-label[data-label]:not([data-label='']),\n    .ql-picker-item[data-label]:not([data-label=''])\n      &::before\n        content: attr(data-label)\n\n  .ql-picker.ql-header\n    width: 98px\n    .ql-picker-label::before,\n    .ql-picker-item::before\n      content: 'Normal'\n    for num in (1..6)\n      .ql-picker-label[data-value=\\\"{num}\\\"]::before,\n      .ql-picker-item[data-value=\\\"{num}\\\"]::before\n        content: 'Heading ' + num\n    .ql-picker-item[data-value=\"1\"]::before\n      font-size: 2em\n    .ql-picker-item[data-value=\"2\"]::before\n      font-size: 1.5em\n    .ql-picker-item[data-value=\"3\"]::before\n      font-size: 1.17em\n    .ql-picker-item[data-value=\"4\"]::before\n      font-size: 1em\n    .ql-picker-item[data-value=\"5\"]::before\n      font-size: 0.83em\n    .ql-picker-item[data-value=\"6\"]::before\n      font-size: 0.67em\n\n  .ql-picker.ql-font\n    width: 108px\n    .ql-picker-label::before,\n    .ql-picker-item::before\n      content: 'Sans Serif'\n    .ql-picker-label[data-value=serif]::before,\n    .ql-picker-item[data-value=serif]::before\n      content: 'Serif'\n    .ql-picker-label[data-value=monospace]::before,\n    .ql-picker-item[data-value=monospace]::before\n      content: 'Monospace'\n    .ql-picker-item[data-value=serif]::before\n      font-family: Georgia, Times New Roman, serif\n    .ql-picker-item[data-value=monospace]::before\n      font-family: Monaco, Courier New, monospace\n\n  .ql-picker.ql-size\n    width: 98px\n    .ql-picker-label::before,\n    .ql-picker-item::before\n      content: 'Normal'\n    .ql-picker-label[data-value=small]::before,\n    .ql-picker-item[data-value=small]::before\n      content: 'Small'\n    .ql-picker-label[data-value=large]::before,\n    .ql-picker-item[data-value=large]::before\n      content: 'Large'\n    .ql-picker-label[data-value=huge]::before,\n    .ql-picker-item[data-value=huge]::before\n      content: 'Huge'\n    .ql-picker-item[data-value=small]::before\n      font-size: 10px\n    .ql-picker-item[data-value=large]::before\n      font-size: 18px\n    .ql-picker-item[data-value=huge]::before\n      font-size: 32px\n\n  .ql-color-picker.ql-background\n    .ql-picker-item\n      background-color: #fff\n  .ql-color-picker.ql-color\n    .ql-picker-item\n      background-color: #000\n\n.ql-code-block-container\n  position: relative\n  .ql-ui\n    right: 5px\n    top: 5px\n", ".ql-toolbar.ql-snow\n  border: 1px solid borderColor\n  box-sizing: border-box\n  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\n  padding: 8px\n\n  .ql-formats\n    margin-right: 15px\n\n  .ql-picker-label\n    border: 1px solid transparent\n  .ql-picker-options\n    border: 1px solid transparent\n    box-shadow: rgba(0,0,0,0.2) 0 2px 8px\n  .ql-picker.ql-expanded\n    .ql-picker-label\n      border-color: borderColor\n    .ql-picker-options\n      border-color: borderColor\n\n  .ql-color-picker\n    .ql-picker-item.ql-selected, .ql-picker-item:hover\n      border-color: #000\n\n.ql-toolbar.ql-snow + .ql-container.ql-snow\n  border-top: 0px;\n", "tooltipMargin = 8px\n\n.ql-snow\n  .ql-tooltip\n    background-color: #fff\n    border: 1px solid borderColor\n    box-shadow: 0px 0px 5px shadowColor\n    color: textColor\n    padding: 5px 12px\n    white-space: nowrap\n    &::before\n      content: \"Visit URL:\"\n      line-height: 26px\n      margin-right: tooltipMargin\n    input[type=text]\n      display: none\n      border: 1px solid borderColor\n      font-size: 13px\n      height: 26px\n      margin: 0px\n      padding: 3px 5px\n      width: 170px\n    a.ql-preview\n      display: inline-block\n      max-width: 200px\n      overflow-x: hidden\n      text-overflow: ellipsis\n      vertical-align: top\n    a.ql-action::after\n      border-right: 1px solid borderColor\n      content: 'Edit'\n      margin-left: tooltipMargin*2\n      padding-right: tooltipMargin\n    a.ql-remove::before\n      content: 'Remove'\n      margin-left: tooltipMargin\n    a\n      line-height: 26px\n  .ql-tooltip.ql-editing\n    a.ql-preview, a.ql-remove\n      display: none\n    input[type=text]\n      display: inline-block\n    a.ql-action::after\n      border-right: 0px\n      content: 'Save'\n      padding-right: 0px\n  .ql-tooltip[data-mode=link]::before\n    content: \"Enter link:\"\n  .ql-tooltip[data-mode=formula]::before\n    content: \"Enter formula:\"\n  .ql-tooltip[data-mode=video]::before\n    content: \"Enter video:\"\n", "themeName = 'snow'\nactiveColor = #06c\nborderColor = #ccc\nbackgroundColor = #fff\ninactiveColor = #444\nshadowColor = #ddd\ntextColor = #444\n\n@import './core'\n@import './base'\n@import './snow/*'\n\n.ql-snow\n  a\n    color: activeColor\n\n.ql-container.ql-snow\n  border: 1px solid borderColor\n"], "names": [], "sourceRoot": ""}