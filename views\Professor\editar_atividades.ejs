<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">

    <title><PERSON><PERSON><PERSON><PERSON></title>
    <meta content="" name="description">
    <meta content="" name="keywords">

    <!-- Favicons -->
    <link href="img/AURORALOGOSFUNDO.ico" rel="icon">
    <link href="img/AURORALOGOSFUNDO.ico" rel="apple-touch-icon">

    <!-- Google Fonts -->
    <link href="https://fonts.gstatic.com" rel="preconnect">
    <link
        href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Nunito:300,300i,400,400i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i"
        rel="stylesheet">

    <!-- Vendor CSS Files -->
    <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
    <link href="assets/vendor/quill/quill.snow.css" rel="stylesheet">
    <link href="assets/vendor/quill/quill.bubble.css" rel="stylesheet">
    <link href="assets/vendor/remixicon/remixicon.css" rel="stylesheet">
    <link href="assets/vendor/simple-datatables/style.css" rel="stylesheet">

    <!-- Template Main CSS File -->
    <link href="assets/css/style.css" rel="stylesheet">

    <!-- =======================================================
  * Template Name: NiceAdmin
  * Template URL: https://bootstrapmade.com/nice-admin-bootstrap-admin-html-template/
  * Updated: Apr 20 2024 with Bootstrap v5.3.3
  * Author: BootstrapMade.com
  * License: https://bootstrapmade.com/license/
  ======================================================== -->
</head>

<body>

    <!-- ======= Header ======= -->
    <header id="header" class="header fixed-top d-flex align-items-center">

        <div class="d-flex align-items-center justify-content-between">
            <a href="/" class="logo d-flex align-items-center">
                <img src="img/AURORALOGOSFUNDO.ico" alt="">
                <span class="d-none d-lg-block">Aurora Boreal</span>
            </a>
            <i class="bi bi-list toggle-sidebar-btn"></i>
        </div><!-- End Logo -->




        </ul>
        </nav><!-- End Icons Navigation -->

    </header><!-- End Header -->

    <main id="main" class="main">

        <div class="pagetitle">
            <h1>Painéis e gerenciamentos</h1>
            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Pág.inicial</a></li>
                    <li class="breadcrumb-item active">Painéis</li>
                </ol>
            </nav>
        </div><!-- End Page Title -->

        <section class="section dashboard">
            <div class="row">
                <!--tabela nova atividades-->            
                <div class="card">
                    <div class="card-body">
                      <h5 class="card-title">Nova Atividade</h5>
                      <form id="form-atividade" method="POST" action="/system/editarAtividade">
                        <input type="hidden" name="id" value="<%=atividade[0].ati_id%>">
                        <div class="mb-3">
                          <label for="titulo" class="form-label">Título da Atividade</label>
                          <input type="text" class="form-control" name="titulo" required value="<%=atividade[0].titulo%>">
                        </div>
                        <div class="mb-3">
                          <label for="descricao" class="form-label">Descrição</label>
                          <textarea class="form-control" name="descricao" rows="4" required><%=atividade[0].descricao%></textarea>
                        </div>
                        <div class="mb-3">
                          <label for="data_entrega" class="form-label">Data de Entrega</label>
                          <input type="datetime-local" class="form-control" name="data_entrega" required value="<%=atividade[0].data_entrega%>">
                        </div>
                    <!--    <div class="mb-3">
                          <label for="anexo_atividade" class="form-label">Anexo (opcional)</label>
                          <input type="file" class="form-control" name="anexo_atividade">
                        </div> -->

                        <button class="btn btn-primary">Cadastrar Atividade</button>
                      </form>
                    </div>
                  </div>
                  <!--fim de novas atividades-->
                  <!-- Listagem de Atividades Cadastradas -->
                </div>
                <!--fim de visualizar respostas-->
            </div><!-- End Right side columns -->
            </div>
        </section>

    </main><!-- End #main -->

    <!-- ======= Footer ======= -->
    <footer id="footer" class="footer">
        <div class="copyright">
            &copy; Copyright <strong><span>NiceAdmin</span></strong>. All Rights Reserved
            &copy; Copyright <strong><span>Devminds</span></strong>. All Rights Reserved
            &copy; Copyright <strong><span>Aurora Boreal</span></strong>. All Rights Reserved
        </div>

    </footer><!-- End Footer -->

    <a href="#" class="back-to-top d-flex align-items-center justify-content-center"><i
            class="bi bi-arrow-up-short"></i></a>

    <!-- Vendor JS Files -->
    <script src="assets/vendor/apexcharts/apexcharts.min.js"></script>
    <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="assets/vendor/chart.js/chart.umd.js"></script>
    <script src="assets/vendor/echarts/echarts.min.js"></script>
    <script src="assets/vendor/quill/quill.js"></script>
    <script src="assets/vendor/simple-datatables/simple-datatables.js"></script>
    <script src="assets/vendor/tinymce/tinymce.min.js"></script>
    <script src="assets/vendor/php-email-form/validate.js"></script>

    <!-- Template Main JS File -->
    <script src="assets/js/main.js"></script>

</body>

</html>