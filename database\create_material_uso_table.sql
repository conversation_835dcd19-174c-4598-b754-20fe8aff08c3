-- Criação da tabela para registro de uso de materiais
CREATE TABLE IF NOT EXISTS tb_material_uso (
    id INT AUTO_INCREMENT PRIMARY KEY,
    produto_id INT NOT NULL,
    professor_id INT NOT NULL,
    quantidade_usada INT NOT NULL,
    data_uso DATETIME NOT NULL,
    observacoes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- <PERSON><PERSON> estrangeiras
    FOREIGN KEY (produto_id) REFERENCES tb_produtos(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (professor_id) REFERENCES tb_usuarios(usu_id) ON DELETE CASCADE,
    
    -- Índices para melhor performance
    INDEX idx_produto_id (produto_id),
    INDEX idx_professor_id (professor_id),
    INDEX idx_data_uso (data_uso)
);

-- Comentários para documentação
ALTER TABLE tb_material_uso COMMENT = 'Tabela para registrar o uso de materiais pelos professores';
ALTER TABLE tb_material_uso MODIFY COLUMN produto_id INT NOT NULL COMMENT 'ID do produto/material utilizado';
ALTER TABLE tb_material_uso MODIFY COLUMN professor_id INT NOT NULL COMMENT 'ID do professor que utilizou o material';
ALTER TABLE tb_material_uso MODIFY COLUMN quantidade_usada INT NOT NULL COMMENT 'Quantidade do material utilizada';
ALTER TABLE tb_material_uso MODIFY COLUMN data_uso DATETIME NOT NULL COMMENT 'Data e hora do uso do material';
ALTER TABLE tb_material_uso MODIFY COLUMN observacoes TEXT COMMENT 'Observações sobre o uso do material';
